package com.zsm.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.zsm.entity.ViewYsfZyfycf;
import com.zsm.model.vo.InpatientSettlementVo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 阜阳肿瘤住院发药处方视图 Mapper 接口
 * 
 * <AUTHOR>
 * @since 2025-07-01
 */
@Mapper
public interface ViewYsfZyfycfMapper extends BaseMapper<ViewYsfZyfycf> {

    /**
     * 查询去重后的住院患者列表（直接在SQL层面去重）
     * 
     * @param startDateTime 开始时间（格式：yyyy-MM-dd HH:mm:ss）
     * @param endDateTime 结束时间（格式：yyyy-MM-dd HH:mm:ss）
     * @return 去重后的住院患者列表
     */
    List<InpatientSettlementVo> selectDistinctInpatients(@Param("startDateTime") String startDateTime, 
                                                          @Param("endDateTime") String endDateTime);
}