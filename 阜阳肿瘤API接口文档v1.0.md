# 阜阳肿瘤医院API接口文档

## 概述

本文档描述了阜阳肿瘤医院门诊发药业务系统的API接口规范，包含门诊处方查询、住院领药单、药品追溯码管理等核心功能接口。系统提供了完整的药品追溯管理功能，支持发药、退药、扫码任务管理等业务流程。

## 基本信息

- **swagger文档**: `http://***********:1168/api/swagger-ui.html`
- **Base URL**: `http://***********:1168/api`
- **Content-Type**: `application/json`
- **字符编码**: `UTF-8`
- **服务端口**: `1168`
- **上下文路径**: `/api`

## 接口分类

### FuYangZhongLiuController 阜阳肿瘤控制器
- 门诊处方查询: `/api/fuYangZhongLiu/queryOutpatientPrescription`
- 住院处方查询: `/api/fuYangZhongLiu/queryInpatientPrescription`  
- 药品字典同步: `/api/fuYangZhongLiu/syncDrugDictionary`

### YsfCommonController 云速付通用控制器
- 药品追溯码上传: `/api/ysfCommon/uploadScans`
- 扫码任务管理: `/api/ysfCommon/cancel/{taskId}`
- 发药单查询: `/api/ysfCommon/YsfStoDps/list`
- 发药明细查询: `/api/ysfCommon/YsfStoDpsSub/list`
- 任务列表查询: `/api/ysfCommon/YsfStoTcTask/list`
- 任务明细查询: `/api/ysfCommon/YsfStoTcTaskSub/list`
- 追溯码查询: `/api/ysfCommon/YsfStoTc/getByDrugtracinfo/{drugtracinfo}`
- 流转记录查询: `/api/ysfCommon/YsfStoTcStatus/list`
- 发药明细查询: `/api/ysfCommon/dispensing/detailsByBizSubId`
- 退药处理: `/api/ysfCommon/return`

## 通用响应格式

### ApiResult<T> 通用响应结构

所有API接口都返回统一的响应格式：

```json
{
  "code": 200,
  "msg": "操作成功",
  "data": {}
}
```

| 字段 | 类型 | 说明 |
|-----|------|------|
| code | Integer | 响应状态码，200表示成功，其他表示失败 |
| msg | String | 响应消息 |
| data | T | 响应数据，类型根据具体接口而定 |

### TableDataInfo 分页响应结构

用于分页查询接口的响应格式：

```json
{
  "code": 200,
  "msg": "查询成功",
  "total": 100,
  "rows": []
}
```

| 字段 | 类型 | 说明 |
|-----|------|------|
| code | Integer | 响应状态码 |
| msg | String | 响应消息 |
| total | Long | 总记录数 |
| rows | List | 当前页数据列表 |

## 接口列表

### 1. 门诊处方查询

**接口描述**: 查询门诊处方信息，支持按处方序号、患者ID、时间范围等条件查询

**URL**: `POST /api/fuYangZhongLiu/queryOutpatientPrescription`

**认证**: 需要 `@SaasAuthorizationVerify` 认证

**请求参数** (OutpatientPrescriptionQueryDto):

| 参数名 | 类型 | 必填 | 说明 | 取值范围/格式 | 示例值 |
|--------|------|------|------|-------------|--------|
| cfxh | String | 否 | 处方序号，多个用逗号分隔，对应HIS系统的唯一识别号。这是最主要的查询条件，建议优先使用 | 长度不限，多个处方号用英文逗号分隔 | "O250529003426,O250529003503" |
| blh | String | 否 | 门诊/住院病历号，用于关联患者就诊信息 | 长度不限 | "MZ202501010001" |
| patient_id | String | 否 | 患者ID，主要用于门诊和急诊药房通过患者ID获取可发药的数据。当不传处方号时可通过此字段查询 | 长度不限，默认为空字符串 | "P001" |
| startTime | String | 否 | 开始时间，在不传处方号或患者ID时，需要能够根据时间获取当天的发药数据，为后续统计工作服务 | yyyy-MM-dd HH:mm:ss 格式，默认为空字符串 | "2025-01-01 00:00:00" |
| endTime | String | 否 | 结束时间，与开始时间配合使用进行时间范围查询 | yyyy-MM-dd HH:mm:ss 格式，默认为空字符串 | "2025-01-31 23:59:59" |
| fg_dps | String | 否 | 发药单标记ID，用于区分发药和退药操作 | 0：发药，1：退药，默认为"0" | "0" |
| send_flag | String | 否 | 发送标识，表示处方的处理状态 | 0：未发送，1：已发送，2：已退药，默认为"1" | "1" |

**请求示例**:
```bash
curl -X POST "http://127.0.0.1:1168/api/fuYangZhongLiu/queryOutpatientPrescription" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer <token>" \
  -d '{
    "cfxh": "O250529003426,O250529003503",
    "blh": "MZ202501010001",
    "patient_id": "P001",
    "startTime": "2025-01-01 00:00:00",
    "endTime": "2025-01-31 23:59:59",
    "fg_dps": "0",
    "send_flag": "1"
  }'
```

**响应数据** (ApiResult<List<PrescriptionItem>>):

所有响应均遵循通用的ApiResult格式，data字段包含PrescriptionItem列表。

**PrescriptionItem字段说明**:

| 字段名 | 类型 | 说明 | 示例值 |
|--------|------|------|--------|
| med_list_codg | String | 药品目录编码，国家医保药品目录中的唯一编码 | "A01AA01001001001001" |
| fixmedins_hilist_id | String | 定点医疗机构目录ID，医疗机构内部药品目录的唯一标识 | "HIS_001" |
| fixmedins_hilist_name | String | 定点医疗机构目录名称，医疗机构内部使用的药品名称 | "阿司匹林肠溶片" |
| fixmedins_bchno | String | 定点医疗机构批次号，医疗机构内部的药品批次管理编号 | "BATCH_001" |
| prsc_dr_certno | String | 开方医师证件号码，处方医师的身份证号码 | "110101197001010001" |
| prsc_dr_name | String | 开方医师姓名，开具该处方的医师姓名 | "张医生" |
| phar_certno | String | 药师证件号码，审核处方的药师身份证号码 | "110101197002020002" |
| phar_name | String | 药师姓名，审核该处方的药师姓名 | "李药师" |
| phar_prac_cert_no | String | 药师执业证书编号，药师的执业资格证书编号 | "PHAR20250001" |
| mdtrt_sn | String | 就诊序列号，患者本次就诊的唯一标识 | "MDT2025010100001" |
| psn_name | String | 人员姓名，患者的真实姓名 | "张三" |
| manu_lotnum | String | 生产批号，药品生产厂家的批次编号 | "20250101" |
| manu_date | String | 生产日期，药品的实际生产日期，格式为YYYY-MM-DD | "2025-01-01" |
| expy_end | String | 有效期截止，药品的有效期截止日期，格式为YYYY-MM-DD | "2027-01-01" |
| rx_flag | Integer | 处方标志，标识处方的类型和状态。0-普通处方，1-急诊处方，2-儿童处方 | 1 |
| trdn_flag | Integer | 拆零标志，标识药品是否需要拆零发放。0-不拆零，1-拆零发放 | 0 |
| rxno | String | 处方号，处方的唯一编号，在医疗机构内具有唯一性 | "RX2025010100001" |
| rx_circ_flag | String | 处方流转标志，处方在不同系统间流转的状态标识 | "0" |
| rtal_docno | String | 零售单据号，药品零售时的单据编号 | "RETAIL001" |
| stoout_no | String | 库存出库单号，库房药品出库的单据编号 | "OUT001" |
| bchno | String | 批次号，药品的批次管理编号 | "BATCH001" |
| sel_retn_cnt | String | 售退数量，发药或退药的数量 | "1" |
| min_sel_retn_cnt | String | 最小售退数量，最小发药或退药单位的数量 | "10" |
| selRetnUnit | String | 售退单位，发药或退药的计量单位 | "盒" |
| hisDosUnit | String | HIS剂量单位，HIS系统中的剂量计量单位 | "片" |
| hisPacUnit | String | HIS包装单位，HIS系统中的包装计量单位 | "盒" |
| sel_retn_time | String | 售退时间，发药或退药的具体时间 | "2025-01-01 11:00:00" |
| sel_retn_opter_name | String | 售退经办人姓名，处理发药或退药的操作员姓名 | "王药师" |
| mdtrt_setl_type | Integer | 就诊结算类型，医保结算的类型标识 | 1 |
| spec | String | 规格，药品的规格描述 | "100mg*10片" |
| prodentp_name | String | 生产企业名称，药品生产厂家的名称 | "某某制药有限公司" |
| cfxh | String | 处方序号，HIS系统中的处方唯一标识 | "O250529003426" |
| cfmxxh | String | 处方明细序号，处方中具体药品条目的序号 | "1" |
| sjh | String | 售价号，药品销售时的价格编号 | "SJ001" |
| patient_id | String | 患者ID，患者在系统中的唯一标识 | "P001" |
| his_con_ratio | String | HIS剂量换算比例，不同计量单位间的换算比例 | "1:10" |
| send_flag | Integer | 发送标志，处方数据的发送状态。0-未发送，1-已发送，2-已退药 | 1 |
| send_time | String | 发送时间，处方数据发送的时间 | "2025-01-01T11:00:00" |
| return_time | String | 返回时间，退药操作的时间 | "2025-01-01T11:05:00" |
| dept_id | String | 发药药房ID，发药科室的唯一标识 | "D001" |
| dept_name | String | 发药药房名称，发药科室的名称 | "内科药房" |
| window | String | 窗口号，发药窗口的编号 | "1号窗口" |
| taskIdDps | String | 药品追溯码任务ID，关联的扫码任务编号 | "123" |
| taskFgStatusDps | String | 药品追溯码任务状态，扫码任务的状态。0-待处理，1-已完成 | "1" |
| taskScanTimeDps | String | 药品追溯码任务创建时间，扫码任务的创建时间 | "2025-01-01T10:30:00" |
| fyyf | String | 发药药房ID，与dept_id相同，发药科室标识 | "D001" |
| drugCode | String | 药品编码，系统内部的药品唯一编码 | "YP001" |
| drugTracCodgs | List<String> | 药品追溯码列表，该药品关联的所有追溯码 | ["8611234567890123456789012345"] |
| dispCnt | String | 发药数量，实际发放的药品数量 | "1" |
| currNum | BigDecimal | 当前数量，当前库存或发药的精确数量 | 1.0 |
| tracCodgStore | Object | 追溯码存储信息，包含药品追溯码的详细信息 | 见下方tracCodgStore结构 |

**tracCodgStore对象结构**:

| 字段名 | 类型 | 说明 | 示例值 |
|--------|------|------|--------|
| drugCode | String | 药品编码 | "YP001" |
| drugTracCodgs | List<String> | 药品追溯码列表 | ["8611234567890123456789012345"] |
| dispCnt | String | 发药数量 | "1" |
| currNum | BigDecimal | 当前数量 | 1.0 |

**响应示例**:
```json
{
  "code": 200,
  "msg": "操作成功",
  "data": [
    {
      "med_list_codg": "A01AA01001001001001",
      "fixmedins_hilist_id": "HIS_001",
      "fixmedins_hilist_name": "阿司匹林肠溶片",
      "fixmedins_bchno": "BATCH_001",
      "prsc_dr_certno": "110101197001010001",
      "prsc_dr_name": "张医生",
      "phar_certno": "110101197002020002",
      "phar_name": "李药师",
      "phar_prac_cert_no": "PHAR20250001",
      "mdtrt_sn": "MDT2025010100001",
      "psn_name": "张三",
      "manu_lotnum": "20250101",
      "manu_date": "2025-01-01",
      "expy_end": "2027-01-01",
      "rx_flag": 1,
      "trdn_flag": 0,
      "rxno": "RX2025010100001",
      "rx_circ_flag": "0",
      "rtal_docno": "RETAIL001",
      "stoout_no": "OUT001",
      "bchno": "BATCH001",
      "sel_retn_cnt": "1",
      "min_sel_retn_cnt": "10",
      "selRetnUnit": "盒",
      "hisDosUnit": "片",
      "hisPacUnit": "盒",
      "sel_retn_time": "2025-01-01 11:00:00",
      "sel_retn_opter_name": "王药师",
      "mdtrt_setl_type": 1,
      "spec": "100mg*10片",
      "prodentp_name": "某某制药有限公司",
      "cfxh": "O250529003426",
      "cfmxxh": "1",
      "sjh": "SJ001",
      "patient_id": "P001",
      "his_con_ratio": "1:10",
      "send_flag": 1,
      "send_time": "2025-01-01T11:00:00",
      "return_time": null,
      "dept_id": "D001",
      "dept_name": "内科药房",
      "window": "1号窗口",
      "taskIdDps": "123",
      "taskFgStatusDps": "1",
      "taskScanTimeDps": "2025-01-01T10:30:00",
      "fyyf": "D001",
      "drugCode": "YP001",
      "drugTracCodgs": ["8611234567890123456789012345"],
      "dispCnt": "1",
      "currNum": 1.0,
      "tracCodgStore": {
        "drugCode": "YP001",
        "drugTracCodgs": ["8611234567890123456789012345"],
        "dispCnt": "1",
        "currNum": 1.0
      }
    }
  ]
}
```

### 2. 住院领药单查询

**接口描述**: 查询住院处方（领药单）信息，支持通过record_id、患者ID、病区名称、时间范围等条件查询住院发药明细

**URL**: `POST /api/fuYangZhongLiu/queryInpatientPrescription`

**认证**: 需要 `@SaasAuthorizationVerify` 认证

**请求参数** (InpatientPrescriptionQueryDto):

| 参数名 | 类型 | 必填 | 说明 | 取值范围/格式 | 示例值 |
|--------|------|------|------|-------------|--------|
| deptId | String | 否 | 发药科室ID，用于筛选特定科室的发药数据 | 长度不限，默认为空字符串 | "D001" |
| start_time | String | 否 | 开始时间，可以查询某一天或某几天的处方数据 | yyyy-MM-dd HH:mm:ss 格式，默认为空字符串 | "2025-01-01 00:00:00" |
| end_time | String | 否 | 结束时间，根据开始时间和结束时间来获取记录ID查询出科室的处方数据 | yyyy-MM-dd HH:mm:ss 格式，默认为空字符串 | "2025-01-31 23:59:59" |
| pat_ward_name | String | 否 | 患者病区名称，用于筛选特定病区的患者发药数据 | 长度不限，默认为空字符串 | "内科一病区" |
| record_id | String | 否 | 发药记录ID，这是最主要的查询字段，建议优先使用 | 长度不限，默认为空字符串 | "PHADISP2025052302672" |
| fg_dps | String | 否 | 发药单标记，用于区分发药和退药操作，建议必填 | 0-发药，1-退药，默认为"0" | "0" |
| patientId | String | 否 | 患者ID，患者在系统中的唯一标识 | 长度不限，默认为空字符串 | "P001" |
| inpatientNo | String | 否 | 住院号，患者住院时的唯一标识号 | 长度不限，默认为空字符串 | "ZY20250101001" |
| cfxh | String | 否 | 处方序号，住院处方的唯一标识 | 长度不限，默认为空字符串 | "I250529003426" |
| cydybz | String | 否 | 出院带药标志，标识是否为出院时带回的药品 | 0-否，1-是，默认为"0" | "0" |

**请求示例**:
```bash
curl -X POST "http://127.0.0.1:1168/api/fuYangZhongLiu/queryInpatientPrescription" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer <token>" \
  -d '{
    "deptId": "D001",
    "start_time": "2025-01-01 00:00:00",
    "end_time": "2025-01-31 23:59:59",
    "pat_ward_name": "内科一病区",
    "record_id": "PHADISP2025052302672",
    "fg_dps": "0",
    "patientId": "P001",
    "inpatientNo": "ZY20250101001",
    "cfxh": "I250529003426",
    "cydybz": "0"
  }'
```

**响应数据** (ApiResult<List<InpatientPrescriptionItem>>):

所有响应均遵循通用的ApiResult格式，data字段包含InpatientPrescriptionItem列表。

**InpatientPrescriptionItem字段说明**:

住院处方响应的字段结构与门诊处方基本相同，但包含以下住院特有字段：

| 字段名 | 类型 | 说明 | 示例值 |
|--------|------|------|--------|
| deptId | String | 科室ID，发药科室的唯一标识 | "D001" |
| pat_ward_name | String | 病区名称，患者所在病区的名称 | "内科一病区" |
| record_id | String | 记录ID，住院发药记录的唯一标识 | "PHADISP2025052302672" |
| record_detail_id | String | 发药明细ID，发药记录的详细条目ID | "DETAIL001" |
| pat_ward_id | String | 病区ID，患者所在病区的唯一标识 | "WARD001" |
| bed_no | String | 床位号，患者住院时的床位编号 | "101-1" |
| inpatient_no | String | 住院号，患者住院时的唯一标识号 | "ZY20250101001" |
| order_type | String | 医嘱类型，医嘱的分类（如长期医嘱、临时医嘱等） | "长期医嘱" |
| exec_time | String | 执行时间，医嘱执行的具体时间 | "2025-01-01T08:00:00" |
| dispense_no | String | 领药单号，住院领药单的编号 | "DISP20250101001" |
| his_drug_code | String | HIS系统中的药品唯一编码 | "HIS_YP001" |
| pat_in_hos_id | String | 患者住院唯一标识，患者本次住院的ID | "PAT_HOS_001" |
| order_id | String | 医嘱ID，医嘱的唯一标识 | "ORDER_001" |
| fg_dps | String | 发药单标记，0-发药，1-退药 | "0" |

**响应示例**:

```json
{
  "code": 200,
  "msg": "操作成功",
  "data": [
    {
      "med_list_codg": "医疗目录编码",
      "fixmedins_hilist_id": "定点医疗机构目录ID",
      "fixmedins_hilist_name": "定点医疗机构目录名称",
      "fixmedins_bchno": "定点医疗机构批次号",
      "prsc_dr_certno": "开方医师证件号码",
      "prsc_dr_name": "开方医师姓名",
      "phar_certno": "药师证件号码",
      "phar_name": "药师姓名",
      "phar_prac_cert_no": "药师执业证书编号",
      "mdtrt_sn": "就医流水号",
      "psn_name": "人员姓名",
      "manu_lotnum": "生产批号",
      "manu_date": "生产日期",
      "expy_end": "有效期截止",
      "rx_flag": 1,
      "trdn_flag": 1,
      "rxno": "处方号",
      "rx_circ_flag": "外购处方标志",
      "rtal_docno": "零售单据号",
      "stoout_no": "销售出库单据号",
      "bchno": "批次号",
      "sel_retn_cnt": "销售/退货数量",
      "min_sel_retn_cnt": "最小售退数量",
      "sel_retn_time": "销售/退货时间",
      "sel_retn_opter_name": "销售/退货经办人姓名",
      "mdtrt_setl_type": 1,
      "spec": "规格",
      "prodentp_name": "生产企业名称",
      "cfxh": "处方序号",
      "cfmxxh": "处方明细序号",
      "sjh": "收据号",
      "patient_id": "患者ID",
      "his_con_ratio": "HIS剂量换算比例",
      "send_flag": 1,
      "send_time": "2025-01-01T11:00:00",
      "return_time": "2025-01-01T11:05:00",
      "deptId": "D001",
      "pat_ward_name": "内科一病区",
      "record_id": "PHADISP2025052302672",
      "record_detail_id": "DETAIL001",
      "pat_ward_id": "WARD001",
      "bed_no": "101-1",
      "inpatient_no": "ZY20250101001",
      "order_type": "长期医嘱",
      "exec_time": "2025-01-01T08:00:00",
      "dispense_no": "DISP20250101001",
      "his_drug_code": "HIS_YP001",
      "pat_in_hos_id": "PAT_HOS_001",
      "order_id": "ORDER_001",
      "fg_dps": "0",
      "taskIdDps": "123",
      "taskFgStatusDps": "1",
      "taskScanTimeDps": "2025-01-01T10:30:00",
      "fyyf": "D001",
      "drugCode": "YP001",
      "drugTracCodgs": ["8611234567890123456789012345"],
      "dispCnt": "1",
      "currNum": 1.0,
      "tracCodgStore": {
        "drugCode": "YP001",
        "drugTracCodgs": ["8611234567890123456789012345"],
        "dispCnt": "1",
        "currNum": 1.0
      }
    }
  ]
}
```

**住院发药接口特点**:
1. **查询条件**: 主要通过 `record_id` 查询住院发药明细数据
2. **发药类型**: 在YsfStoDps表中sd_dps字段值设为1，表示住院发药
3. **差异字段**: 相比门诊发药，增加了病区信息（pat_ward_name）、出院带药标志（cydybz）等住院特有字段
4. **数据源**: 查询VIEW_ZYFYCF_FY视图，该视图包含住院发药的完整信息
5. **冗余存储**: 住院特有的差异字段会在nhsa_3505表中进行冗余存储

### 3. 药品追溯码上传

**接口描述**: 批量上传药品追溯码扫描数据，将本次采集的所有药品追溯码信息批量提交

**URL**: `POST /api/ysfCommon/uploadScans`

**认证**: 需要 `@SaasAuthorizationVerify` 认证

**请求参数** (TraceabilityUploadDto):

| 参数名 | 类型 | 必填 | 说明 | 取值范围/格式 | 示例值 |
|--------|------|------|------|-------------|--------|
| fgDps | String | 否 | 退药单标记，用于区分发药和退药操作 | 0: 发药, 1: 退药 | "0" |
| sdDps | String | 否 | 发药单类型，标识处方来源 | 1: 住院处方, 2: 门诊处方 | "2" |
| patWardId | String | 否 | 患者病区ID，住院处方时使用 | 长度不限 | "WARD001" |
| patWardName | String | 否 | 患者病区名称，住院处方时使用 | 长度不限 | "内科一病区" |
| window | String | 否 | 窗口号，发药窗口的标识 | 长度不限 | "1号窗口" |
| selRetnOpterId | String | 否 | 选择退药操作员ID，退药时的操作员标识 | 长度不限 | "OP001" |
| prescriptions | List<PrescriptionItem> | 是 | 处方列表，包含本次上传的所有处方信息 | 至少包含1个处方项 | 见下方PrescriptionItem结构 |

**PrescriptionItem 结构**:

| 参数名 | 类型 | 必填 | 说明 | 取值范围/格式 | 示例值 |
|--------|------|------|------|-------------|--------|
| outPresId | String | 是 | 处方ID，处方的唯一标识符 | 长度不限，必须唯一 | "O250529003426" |
| presCode | String | 否 | 处方编号，处方的业务编号 | 长度不限 | "RX001" |
| patId | String | 否 | 患者ID，患者的唯一标识 | 长度不限 | "P001" |
| patName | String | 否 | 患者姓名，患者的真实姓名 | 长度不限 | "张三" |
| cardNo | String | 否 | 就诊卡号，患者的就诊卡编号 | 长度不限 | "C001" |
| idDept | String | 否 | 发药科室ID，发药科室的唯一标识 | 长度不限 | "D001" |
| drugItems | List<DrugItem> | 是 | 药品明细列表，包含该处方的所有药品信息 | 至少包含1个药品项 | 见下方DrugItem结构 |

**DrugItem 结构**:

| 参数名 | 类型 | 必填 | 说明 | 取值范围/格式 | 示例值 |
|--------|------|------|------|-------------|--------|
| outPresdetailid | String | 是 | 处方明细ID，处方中药品条目的唯一标识 | 长度不限，必须唯一 | "D001" |
| drugCode | String | 否 | 药品编码，系统内部的药品唯一编码 | 长度不限 | "YP001" |
| drugName | String | 否 | 药品名称，药品的通用名称 | 长度不限 | "阿司匹林肠溶片" |
| spec | String | 否 | 药品规格，药品的规格描述 | 长度不限 | "100mg*10片" |
| prodentpName | String | 否 | 生产企业名称，药品生产厂家的名称 | 长度不限 | "某某制药有限公司" |
| quantity | Integer | 否 | 药品数量，药品的总数量 | 正整数 | 1 |
| unit | String | 否 | 药品单位，药品的计量单位 | 长度不限 | "盒" |
| price | Double | 否 | 药品单价，药品的单位价格 | 大于等于0的数值 | 12.5 |
| amount | Double | 否 | 药品总价，药品的总金额 | 大于等于0的数值 | 12.5 |
| drugtracinfoScanned | String | 是 | 扫描的追溯码，必填字段，多个追溯码用英文逗号分隔 | 长度不限，多个追溯码用逗号分隔 | "8611234567890123456789012345,8611234567890123456789012346" |
| minDoseCount | Integer | 否 | 最小制剂单位数量，最小剂量单位的数量 | 正整数 | 10 |
| minPackingName | String | 否 | 最小包装名称，最小包装单位的名称 | 长度不限 | "片" |
| trdnFlag | Integer | 否 | 拆零标识，标识药品是否拆零发放 | 0: 未拆零, 1: 拆零 | 0 |
| dispCnt | Integer | 否 | 发药数量，实际发放的药品数量 | 正整数 | 1 |
| cfxh | String | 否 | 处方号，处方的业务编号 | 长度不限 | "O250529003426" |
| cfmxxh | String | 否 | 处方明细号，处方明细的业务编号 | 长度不限 | "D001" |

**请求示例**:
```json
{
  "fgDps": "0",
  "sdDps": "2",
  "patWardId": "WARD001",
  "patWardName": "内科一病区",
  "window": "1号窗口",
  "selRetnOpterId": "OP001",
  "prescriptions": [
    {
      "outPresId": "O250529003426",
      "presCode": "RX001",
      "patId": "P001",
      "patName": "张三",
      "cardNo": "C001",
      "idDept": "D001",
      "drugItems": [
        {
          "outPresdetailid": "D001",
          "drugCode": "YP001",
          "drugName": "阿司匹林肠溶片",
          "spec": "100mg*10片",
          "prodentpName": "某某制药有限公司",
          "quantity": 1,
          "unit": "盒",
          "price": 12.5,
          "amount": 12.5,
          "drugtracinfoScanned": "8611234567890123456789012345,8611234567890123456789012346",
          "minDoseCount": 10,
          "minPackingName": "片",
          "trdnFlag": 0,
          "dispCnt": 1,
          "cfxh": "O250529003426",
          "cfmxxh": "D001"
        }
      ]
    }
  ]
}
```

**响应数据** (ApiResult<TraceabilityUploadResultVo>):

**TraceabilityUploadResultVo字段说明**:

| 字段名 | 类型 | 说明 | 示例值 |
|--------|------|------|--------|
| success | List<String> | 上传成功的处方ID列表，包含所有成功处理的处方标识 | ["O250529003426", "O250529003427"] |
| fail | List<Object> | 上传失败的处方ID列表，包含所有处理失败的处方标识 | ["O250529003428"] |
| failMessages | Map<String, String> | 失败处方的错误信息映射，key为处方ID，value为具体错误信息 | {"O250529003428": "追溯码格式不正确"} |

**响应示例**:
```json
{
  "code": 200,
  "msg": "操作成功",
  "data": {
    "success": ["O250529003426"],
    "fail": [],
    "failMessages": {}
  }
}
```

### 4. 取消扫码任务

**接口描述**: 根据任务ID取消未完成的扫码任务

**URL**: `POST /api/ysfCommon/cancel/{taskId}`

**认证**: 需要 `@SaasAuthorizationVerify` 认证

**路径参数**:

| 参数名 | 类型 | 必填 | 说明 | 取值范围/格式 | 示例值 |
|--------|------|------|------|-------------|--------|
| taskId | Long | 是 | 任务ID，扫码任务的唯一标识符，用于指定要取消的任务 | 大于0的整数，必填 | 123 |

**响应数据** (ApiResult<String>):

**响应字段说明**:
- 成功时返回操作成功的提示信息
- 失败时返回具体的错误信息

**响应示例**:
```json
{
  "code": 200,
  "msg": "操作成功",
  "data": "任务取消成功"
}
```

### 5. 查询发药单列表

**接口描述**: 根据条件查询发药单列表信息，支持分页

**URL**: `GET /api/ysfCommon/YsfStoDps/list`

**认证**: 需要 `@SaasAuthorizationVerify` 认证

**请求参数** (DispensingRecordQueryDto):

| 参数名 | 类型 | 必填 | 说明 | 取值范围/格式 | 示例值 |
|--------|------|------|------|-------------|--------|
| patientName | String | 否 | 患者姓名，用于按患者姓名筛选发药单 | 长度不限 | "张三" |
| prescriptionId | String | 否 | 处方号，可以是cfxh或outPresId，用于精确查找特定处方 | 长度不限 | "O250529003426" |
| dispensingStatus | String | 否 | 发药单状态，用于筛选特定状态的发药单 | 0: 待发药, 1: 已发药, 7: 部分退药, 8: 全部退药, 9: 作废 | "1" |
| deptId | String | 否 | 发药科室ID，用于筛选特定科室的发药单 | 长度不限 | "D001" |
| startTime | Date | 否 | 发药开始时间，时间范围查询的起始时间 | yyyy-MM-dd 格式 | "2025-01-01" |
| endTime | Date | 否 | 发药结束时间，时间范围查询的结束时间 | yyyy-MM-dd 格式 | "2025-01-31" |
| fgDps | String | 否 | 退药单标记，用于区分发药单和退药单 | 0: 发药, 1: 退药 | "0" |
| sdDps | String | 否 | 发药单类型，用于区分处方来源 | 1: 住院处方, 2: 门诊处方 | "2" |
| patWardId | String | 否 | 患者病区ID，住院处方时用于筛选特定病区 | 长度不限 | "WARD001" |
| pageNum | Integer | 否 | 页码，分页查询的页数 | 大于0的整数，默认为1 | 1 |
| pageSize | Integer | 否 | 每页数量，每页返回的记录数 | 1-100之间的整数，默认为10 | 10 |

**请求示例**:
```bash
curl -X GET "http://127.0.0.1:1168/api/ysfCommon/YsfStoDps/list?patientName=张三&pageNum=1&pageSize=10&fgDps=0&sdDps=2" \
  -H "Authorization: Bearer <token>"
```

**响应数据** (TableDataInfo):

基于YsfStoDps实体扩展的DispensingRecordVo，包含发药单基本信息和关联的任务状态。

**响应字段说明**:

| 字段名 | 类型 | 说明 | 示例值 |
|--------|------|------|--------|
| idDps | Long | 发药单主键，发药单的唯一标识符 | 1 |
| cfxh | String | 发药单号，处方序号 | "O250529003426" |
| sdDps | String | 发药单类型。1: 住院处方, 2: 门诊处方 | "2" |
| createTime | LocalDateTime | 创建时间，发药单的创建时间 | "2025-01-01T10:30:00" |
| sendTime | LocalDateTime | 发药时间，药品实际发放的时间 | "2025-01-01T11:00:00" |
| idPi | String | 患者ID，患者的唯一标识 | "P001" |
| patientId | String | 患者ID，与idPi相同，患者标识的备用字段 | "P001" |
| psnName | String | 患者姓名，患者的真实姓名 | "张三" |
| cardNo | String | 就诊卡号，患者的就诊卡编号 | "C001" |
| snBill | String | 票据号码，发药相关的票据编号 | "B001" |
| idStl | String | 结算主键，结算记录的唯一标识 | "S001" |
| idPres | String | 处方主键，处方的唯一标识 | "O250529003426" |
| pharPracCertNo | String | 药师执业资格证号 | "PHAR20250001" |
| prscDrName | String | 开单医生姓名，开具处方的医师姓名 | "李医生" |
| idDoc | String | 开单医生ID，医师的唯一标识 | "DOC001" |
| idSto | String | 仓储ID，药品库房的标识 | "STO001" |
| idStoWin | String | 发药窗口ID，发药窗口的标识 | "WIN001" |
| fgStatus | String | 发药单状态。0: 待发药, 1: 已发药, 7: 部分退药, 8: 全部退药, 9: 作废 | "1" |
| fgPrint | String | 打印标识，标识是否已打印 | "0" |
| idDept | String | 发药科室ID，发药科室的唯一标识 | "D001" |
| selRetnOpterName | String | 发药人姓名，处理发药的操作员姓名 | "王药师" |
| userCheck | String | 配药人，负责配药的人员 | "配药员" |
| fgDps | String | 退药单标记。0: 发药, 1: 退药 | "0" |
| oriIdDps | String | 原发药单主键，退药单关联的原发药单ID | null |
| idTask | Long | 扫码任务主键，关联的扫码任务ID | 123 |
| idOrg | String | 机构编号，开单机构的编号 | "H34152300217" |
| orgId | String | 医疗机构编码 | "H34152300217" |
| orgName | String | 医疗机构名称 | "阜阳肿瘤医院" |
| window | String | 窗口号，发药窗口的编号 | "1号窗口" |
| selRetnOpterId | String | 选择退回操作员ID | "OP001" |
| patWardId | String | 患者病区ID，住院处方时的病区标识 | "WARD001" |
| patWardName | String | 患者病区名称，住院处方时的病区名称 | "内科一病区" |
| dispensingFgStatus | String | 发药单状态，来自ysf_sto_dps表的fg_status字段 | "1" |
| taskStatus | String | 关联的最新任务状态，来自ysf_sto_tc_task表的fg_status字段 | "0" |
| taskId | Long | 关联的最新任务ID，来自ysf_sto_tc_task表的id_task字段 | 123 |

**响应示例**:
```json
{
  "code": 200,
  "msg": "查询成功",
  "total": 50,
  "rows": [
    {
      "idDps": 1,
      "cfxh": "O250529003426",
      "sdDps": "2",
      "createTime": "2025-01-01T10:30:00",
      "sendTime": "2025-01-01T11:00:00",
      "idPi": "P001",
      "patientId": "P001",
      "psnName": "张三",
      "cardNo": "C001",
      "snBill": "B001",
      "idStl": "S001",
      "idPres": "O250529003426",
      "pharPracCertNo": "PHAR20250001",
      "prscDrName": "李医生",
      "idDoc": "DOC001",
      "idSto": "STO001",
      "idStoWin": "WIN001",
      "fgStatus": "1",
      "fgPrint": "0",
      "idDept": "D001",
      "selRetnOpterName": "王药师",
      "userCheck": "配药员",
      "fgDps": "0",
      "oriIdDps": null,
      "idTask": 123,
      "idOrg": "H34152300217",
      "orgId": "H34152300217",
      "orgName": "阜阳肿瘤医院",
      "window": "1号窗口",
      "selRetnOpterId": "OP001",
      "patWardId": null,
      "patWardName": null,
      "dispensingFgStatus": "1",
      "taskStatus": "0",
      "taskId": 123
    }
  ]
}
```

### 6. 查询发药单明细列表

**接口描述**: 根据发药单ID查询发药单明细列表，支持按药品名称/编码、是否已采集等条件过滤

**URL**: `GET /api/ysfCommon/YsfStoDpsSub/list`

**认证**: 需要 `@SaasAuthorizationVerify` 认证

**请求参数** (DispensingRecordDetailQueryDto):

| 参数名 | 类型 | 必填 | 说明 | 取值范围/格式 | 示例值 |
|--------|------|------|------|-------------|--------|
| cfxh | String | 否* | 处方序号，处方的唯一标识 | 长度不限 | "O250529003426" |
| idDps | Long | 否* | 发药单ID，发药单的主键标识 | 大于0的整数 | 123 |
| patientName | String | 否 | 患者姓名，用于筛选特定患者的发药明细 | 长度不限 | "张三" |
| drugNameOrCode | String | 否 | 药品名称或编码，用于搜索特定药品的发药明细 | 长度不限 | "阿司匹林" |
| isCollected | String | 否 | 是否已采集，标识追溯码是否已扫描采集 | 0: 未采集, 1: 已采集 | "0" |
| pageNum | Integer | 否 | 页码，分页查询的页数 | 大于0的整数，默认为1 | 1 |
| pageSize | Integer | 否 | 每页数量，每页返回的记录数 | 1-100之间的整数，默认为10 | 10 |

**重要说明**: cfxh（处方序号）和 idDps（发药单ID）不能都为空，至少需要提供其中一个作为查询条件

**响应数据** (TableDataInfo):

返回DispensingRecordDetailVo列表，包含发药单明细信息和关联的扫码状态。

**响应字段说明**:

| 字段名 | 类型 | 说明 | 示例值 |
|--------|------|------|--------|
| id | Long | 明细ID，发药单明细的唯一标识符 | 1 |
| idDps | String | 发药单ID，关联的发药单标识 | "123" |
| cfxh | String | 处方号，处方的业务编号 | "O250529003426" |
| cfmxxh | String | 处方明细序号，处方中药品条目的序号 | "D001" |
| drugCode | String | 药品编码，系统内部的药品唯一编码 | "YP001" |
| naFee | String | 药品名称，药品的通用名称 | "阿司匹林肠溶片" |
| priceSale | BigDecimal | 单价，药品的销售单价 | 12.50 |
| selRetnCnt | Integer | 数量，发药或退药的数量 | 1 |
| amtTotal | BigDecimal | 金额，药品的总金额 | 12.50 |
| unitSale | String | 包装单位，药品的销售单位 | "盒" |
| unitSaleFactor | Integer | 包装系数，包装单位的换算系数 | 1 |
| drugtracinfo | String | 追溯码，该药品关联的追溯码信息 | "8611234567890123456789012345" |
| idSub | Long | 任务明细ID，关联的扫码任务明细标识 | 456 |
| fgScanned | String | 是否已扫码，标识追溯码是否已扫描。0: 未扫码, 1: 已扫码 | "1" |
| tracCnt | Integer | 已采集的追溯码数量，已成功扫描的追溯码数量 | 1 |
| createTime | LocalDateTime | 创建时间，记录的创建时间 | "2025-01-01T10:30:00" |
| updateTime | LocalDateTime | 更新时间，记录的最后更新时间 | "2025-01-01T11:00:00" |

**响应示例**:
```json
{
  "code": 200,
  "msg": "查询成功",
  "total": 10,
  "rows": [
    {
      "id": 1,
      "idDps": "123",
      "cfxh": "O250529003426",
      "cfmxxh": "D001",
      "drugCode": "YP001",
      "naFee": "阿司匹林肠溶片",
      "priceSale": 12.50,
      "selRetnCnt": 1,
      "amtTotal": 12.50,
      "unitSale": "盒",
      "unitSaleFactor": 1,
      "drugtracinfo": "8611234567890123456789012345",
      "idSub": 456,
      "fgScanned": "1",
      "tracCnt": 1,
      "createTime": "2025-01-01T10:30:00",
      "updateTime": "2025-01-01T11:00:00"
    }
  ]
}
```

### 7. 查询扫码任务列表

**接口描述**: 支持按任务状态、患者姓名、业务单号、任务类型、创建时间范围等查询

**URL**: `GET /api/ysfCommon/YsfStoTcTask/list`

**认证**: 需要 `@SaasAuthorizationVerify` 认证

**请求参数** (YsfStoTcTaskQueryDto):

| 参数名 | 类型 | 必填 | 说明 | 取值范围/格式 | 示例值 |
|--------|------|------|------|-------------|--------|
| taskStatus | String | 否 | 任务状态，用于筛选特定状态的扫码任务 | 0: 待处理, 1: 已完成, 2: 已取消 | "0" |
| remark | String | 否 | 患者姓名，任务备注中通常包含患者姓名信息 | 长度不限 | "张三" |
| bizCode | String | 否 | 业务单号，对应处方ID（outPresId），用于查找特定处方的扫码任务 | 长度不限 | "O250529003426" |
| taskType | String | 否 | 任务类型，扫码任务的分类标识 | 具体取值根据业务定义 | "1" |
| startTime | String | 否 | 创建开始时间，任务创建时间的起始范围 | yyyy-MM-dd HH:mm:ss 格式 | "2025-01-01 00:00:00" |
| endTime | String | 否 | 创建结束时间，任务创建时间的结束范围 | yyyy-MM-dd HH:mm:ss 格式 | "2025-01-31 23:59:59" |
| pageNum | Integer | 是 | 页码，分页查询的页数 | 大于0的整数，必填 | 1 |
| pageSize | Integer | 是 | 每页数量，每页返回的记录数 | 1-100之间的整数，必填 | 10 |

**响应数据** (TableDataInfo):

```json
{
  "code": 200,
  "msg": "查询成功",
  "total": 20,
  "rows": [
    {
      "idTask": 123,
      "fgStatus": "0",
      "remark": "张三的扫码任务",
      "cdBiz": "O250529003426",
      "sdTaskType": "1",
      "createTime": "2025-01-01 10:30:00",
      "updateTime": "2025-01-01 11:00:00"
    }
  ]
}
```

### 8. 查询扫码任务明细列表

**接口描述**: 根据任务ID查询扫码任务明细列表，支持按药品名称/编码、是否已扫码等条件过滤

**URL**: `GET /api/ysfCommon/YsfStoTcTaskSub/list`

**认证**: 需要 `@SaasAuthorizationVerify` 认证

**请求参数** (YsfStoTcTaskSubQueryDto):

| 参数名 | 类型 | 必填 | 说明 | 取值范围/格式 | 示例值 |
|--------|------|------|------|-------------|--------|
| idTask | Long | 是 | 扫码任务ID，扫码任务的主键标识，必填参数 | 大于0的整数，必填 | 123 |
| drugNameOrCode | String | 否 | 药品名称或编码，用于搜索特定药品的扫码明细 | 长度不限 | "阿司匹林" |
| isScanned | String | 否 | 是否已扫码，标识追溯码是否已成功扫描 | 0: 未扫码, 1: 已扫码 | "0" |
| traceCode | String | 否 | 追溯码，用于查找特定追溯码的扫码记录 | 长度不限，通常为20位数字字母组合 | "8611234567890123456789012345" |
| pageNum | Integer | 是 | 页码，分页查询的页数 | 大于0的整数，必填 | 1 |
| pageSize | Integer | 是 | 每页数量，每页返回的记录数 | 1-100之间的整数，必填 | 10 |

**响应数据** (TableDataInfo):

```json
{
  "code": 200,
  "msg": "查询成功",
  "total": 5,
  "rows": [
    {
      "idSub": 456,
      "idTask": "123",
      "drugCode": "YP001",
      "fgScanned": "1",
      "drugtracinfo": "8611234567890123456789012345",
      "scanUser": "操作员",
      "scanTime": "2025-01-01 11:00:00",
      "tracCnt": 1
    }
  ]
}
```

### 9. 查询单个追溯码及其生命周期

**接口描述**: 根据追溯码查询其基本信息和状态变更历史

**URL**: `GET /api/ysfCommon/YsfStoTc/getByDrugtracinfo/{drugtracinfo}`

**认证**: 需要 `@SaasAuthorizationVerify` 认证

**路径参数**:

| 参数名 | 类型 | 必填 | 说明 | 取值范围/格式 | 示例值 |
|--------|------|------|------|-------------|--------|
| drugtracinfo | String | 是 | 追溯码，药品的唯一追溯标识 | 长度不限，通常为20位数字字母组合，必填 | "8611234567890123456789012345" |

**响应数据** (ApiResult<TraceabilityCodeInfoVo>):

**TraceabilityCodeInfoVo字段说明**:

| 字段名 | 类型 | 说明 | 示例值 |
|--------|------|------|--------|
| traceabilityCodeInfo | YsfStoTc | 追溯码基本信息对象，包含追溯码的核心属性 | 见下方YsfStoTc结构 |
| hisDrugDict | HisDrugDict | 药品字典信息，包含药品的详细信息 | 见下方HisDrugDict结构 |
| statusHistory | List<YsfStoTcStatus> | 状态历史记录列表，按create_time升序排序，展示追溯码的生命周期 | 见下方YsfStoTcStatus结构 |
| medListCodg | String | 医疗目录编码，国家医保药品目录编码 | "A01AA01001001001001" |
| drugName | String | 药品名称，药品的通用名称 | "阿司匹林肠溶片" |
| spec | String | 药品规格，药品的规格描述 | "100mg*10片" |
| prodentpName | String | 生产企业名称，药品生产厂家 | "某某制药有限公司" |

**YsfStoTc（追溯码信息）字段说明**:

| 字段名 | 类型 | 说明 | 示例值 |
|--------|------|------|--------|
| idTc | Long | 追溯码主键，追溯码记录的唯一标识 | 1 |
| sdTc | String | 追溯码类型。1: 整箱码, 2: 商品追溯码 | "2" |
| sdTcManage | String | 追溯码管理类型 | "1" |
| drugCode | String | 药品编码，系统内部的药品唯一编码 | "YP001" |
| drugtracinfo | String | 追溯码，药品的唯一追溯标识 | "8611234567890123456789012345" |
| unitSaleFactor | Integer | 销售单位系数，单位换算系数 | 1 |
| unitTc | String | 追溯码单位，追溯码对应的计量单位 | "盒" |
| amountRem | BigDecimal | 剩余数量，追溯码对应的剩余数量 | 1.0 |
| fgActive | String | 是否激活，追溯码的激活状态。0: 未激活, 1: 已激活 | "1" |
| idOrg | String | 机构编号，追溯码所属机构 | "H34152300217" |
| manuLotnum | String | 生产批号，药品的生产批次号 | "20250101" |
| manuDate | LocalDateTime | 生产日期，药品的生产日期 | "2025-01-01T00:00:00" |
| expyEnd | LocalDateTime | 有效期截止，药品的有效期截止日期 | "2027-01-01T00:00:00" |

**YsfStoTcStatus（状态历史）字段说明**:

| 字段名 | 类型 | 说明 | 示例值 |
|--------|------|------|--------|
| id | Long | 状态记录主键，状态变更记录的唯一标识 | 1 |
| sdTcStatus | String | 业务类型，追溯码流转的业务操作类型 | "3001" |
| sdTcManage | String | 追溯码管理类型 | "1" |
| idBizOri | String | 原始业务主键，关联的业务记录标识 | "123" |
| idTc | Long | 追溯码主键，关联的追溯码记录 | 1 |
| cfmxxh | String | 处方明细序号，处方中药品条目的序号 | "D001" |
| drugCode | String | 药品编码，系统内部的药品唯一编码 | "YP001" |
| drugtracinfo | String | 追溯码，药品的唯一追溯标识 | "8611234567890123456789012345" |
| idDept | String | 科室ID，操作科室的标识 | "D001" |
| sdTc | String | 追溯码类型 | "2" |
| selRetnCnt | Integer | 售退数量，操作涉及的数量 | 1 |
| fgUp | String | 是否上传，数据是否已上传标识 | "0" |
| fgActive | String | 是否激活，记录的激活状态 | "1" |
| createTime | LocalDateTime | 创建时间，状态变更的时间 | "2025-01-01T11:00:00" |

**响应示例**:
```json
{
  "code": 200,
  "msg": "操作成功",
  "data": {
    "traceabilityCodeInfo": {
      "idTc": 1,
      "sdTc": "2",
      "sdTcManage": "1",
      "drugCode": "YP001",
      "drugtracinfo": "8611234567890123456789012345",
      "unitSaleFactor": 1,
      "unitTc": "盒",
      "amountRem": 1.0,
      "fgActive": "1",
      "idOrg": "H34152300217",
      "manuLotnum": "20250101",
      "manuDate": "2025-01-01T00:00:00",
      "expyEnd": "2027-01-01T00:00:00"
    },
    "hisDrugDict": null,
    "statusHistory": [
      {
        "id": 1,
        "sdTcStatus": "3001",
        "sdTcManage": "1",
        "idBizOri": "123",
        "idTc": 1,
        "cfmxxh": "D001",
        "drugCode": "YP001",
        "drugtracinfo": "8611234567890123456789012345",
        "idDept": "D001",
        "sdTc": "2",
        "selRetnCnt": 1,
        "fgUp": "0",
        "fgActive": "1",
        "createTime": "2025-01-01T11:00:00"
      }
    ],
    "medListCodg": "A01AA01001001001001",
    "drugName": "阿司匹林肠溶片",
    "spec": "100mg*10片",
    "prodentpName": "某某制药有限公司"
  }
}
```

### 10. 查询追溯码流转记录

**接口描述**: 根据条件查询追溯码流转记录列表

**URL**: `GET /api/ysfCommon/YsfStoTcStatus/list`

**认证**: 需要 `@SaasAuthorizationVerify` 认证

**请求参数** (YsfStoTcStatusQueryDto):

| 参数名 | 类型 | 必填 | 说明 | 取值范围/格式 | 示例值 |
|--------|------|------|------|-------------|--------|
| drugtracinfo | String | 是 | 追溯码，药品的唯一追溯标识，必填参数 | 长度不限，通常为20位数字字母组合，必填 | "8611234567890123456789012345" |
| sdTcStatus | String | 否 | 业务类型，追溯码流转的业务操作类型 | 3001: 门诊发药, 3002: 住院发药, 等其他业务类型 | "3001" |
| idSto | String | 否 | 库房ID，药品所在库房的标识 | 长度不限 | "S001" |
| drugCode | String | 否 | 商品编码，药品的系统内部编码 | 长度不限 | "YP001" |
| idBizOri | String | 否 | 原始业务主键，关联的业务记录主键 | 长度不限 | "123" |
| beginTime | Date | 否 | 开始时间，流转记录的起始时间范围 | yyyy-MM-dd 格式 | "2025-01-01" |
| endTime | Date | 否 | 结束时间，流转记录的结束时间范围 | yyyy-MM-dd 格式 | "2025-01-31" |
| pageNum | Integer | 是 | 页码，分页查询的页数 | 大于0的整数，必填 | 1 |
| pageSize | Integer | 是 | 每页数量，每页返回的记录数 | 1-100之间的整数，必填 | 10 |

**响应数据** (TableDataInfo):

```json
{
  "code": 200,
  "msg": "查询成功",
  "total": 15,
  "rows": [
    {
      "id": 1,
      "sdTcStatus": "3001",
      "sdTcStatusName": "门诊发药",
      "sdTcManage": "1",
      "idBizOri": "123",
      "idTc": 1,
      "cfmxxh": "D001",
      "drugCode": "YP001",
      "drugtracinfo": "8611234567890123456789012345",
      "idDept": "D001",
      "stoName": "库房-D001",
      "sdTc": "2",
      "selRetnCnt": 1,
      "fgUp": "0",
      "fgActive": "1",
      "createTime": "2025-01-01T11:00:00"
    }
  ]
}
```

### 11. 根据业务子ID查询发药明细

**接口描述**: 通过业务子ID（如发药单明细ID）直接查询关联的发药信息

**URL**: `GET /api/ysfCommon/dispensing/detailsByBizSubId`

**认证**: 需要 `@SaasAuthorizationVerify` 认证

**请求参数**:

| 参数名 | 类型 | 必填 | 说明 | 取值范围/格式 | 示例值 |
|--------|------|------|------|-------------|--------|
| bizSubId | String | 是 | 业务子ID，通常是发药单明细ID或处方明细ID，用于精确定位特定的发药明细记录 | 长度不限，必填 | "D001" |

**请求示例**:
```bash
curl -X GET "http://127.0.0.1:1168/api/ysfCommon/dispensing/detailsByBizSubId?bizSubId=D001" \
  -H "Authorization: Bearer <token>"
```

**响应数据** (ApiResult<DispensingDetailVo>):

**DispensingDetailVo字段说明**:

| 字段名 | 类型 | 说明 | 示例值 |
|--------|------|------|--------|
| bizSubId | String | 业务子ID，发药单明细或处方明细的标识 | "D001" |
| dispensingId | String | 发药单ID，发药单的唯一标识 | "123" |
| dispensingNo | String | 发药单号，发药单的业务编号 | "DPS123" |
| prescriptionNo | String | 处方号，处方的业务编号 | "RX001" |
| patientId | String | 患者ID，患者的唯一标识 | "P001" |
| patientName | String | 患者姓名，患者的真实姓名 | "张三" |
| drugCode | String | 商品编码，药品的系统内部编码 | "YP001" |
| drugName | String | 商品名称，药品的通用名称 | "阿司匹林肠溶片" |
| specification | String | 商品规格，药品的规格描述 | "100mg*10片" |
| unit | String | 单位，药品的计量单位 | "盒" |
| quantity | BigDecimal | 数量，发药的数量 | 1.0 |
| price | BigDecimal | 单价，药品的单位价格 | 12.50 |
| amount | BigDecimal | 总金额，药品的总价格 | 12.50 |
| dispensingTime | LocalDateTime | 发药时间，药品发放的具体时间 | "2025-01-01T11:00:00" |
| dispensingUser | String | 发药人，处理发药的操作员姓名 | "操作员" |
| storageId | String | 库房ID，药品所在库房的标识 | "D001" |
| storageName | String | 库房名称，药品所在库房的名称 | "内科药房" |
| tracedCount | Integer | 已追溯数量，已完成追溯码扫描的数量 | 1 |
| traceCodes | List<String> | 追溯码列表，该药品关联的所有追溯码 | ["8611234567890123456789012345"] |

**响应示例**:
```json
{
  "code": 200,
  "msg": "操作成功",
  "data": {
    "bizSubId": "D001",
    "dispensingId": "123",
    "dispensingNo": "DPS123",
    "prescriptionNo": "RX001",
    "patientId": "P001",
    "patientName": "张三",
    "drugCode": "YP001",
    "drugName": "阿司匹林肠溶片",
    "specification": "100mg*10片",
    "unit": "盒",
    "quantity": 1.0,
    "price": 12.50,
    "amount": 12.50,
    "dispensingTime": "2025-01-01T11:00:00",
    "dispensingUser": "操作员",
    "storageId": "D001",
    "storageName": "内科药房",
    "tracedCount": 1,
    "traceCodes": ["8611234567890123456789012345"]
  }
}
```

### 12. 退药上传

**接口描述**: 上传退药信息进行退药处理

**URL**: `POST /api/ysfCommon/return`

**认证**: 需要 `@SaasAuthorizationVerify` 认证

**请求参数** (TraceabilityUploadDto): 与第3个接口相同

**响应数据**:

```json
{
  "code": 200,
  "msg": "操作成功",
  "data": "退药处理成功"
}
```

### 13. 同步药品字典接口

**接口描述**: 从HIS系统同步药品字典数据到本地数据库

**URL**: `POST /api/fuYangZhongLiu/syncDrugDictionary`

**认证**: 无需认证

**请求参数**: 无

**请求示例**:
```bash
curl -X POST "http://127.0.0.1:1168/api/fuYangZhongLiu/syncDrugDictionary" \
  -H "Content-Type: application/json"
```

**响应数据**:

```json
{
  "code": 200,
  "msg": "操作成功",
  "data": "药品字典同步成功"
}
```

## 错误码说明

### 通用错误码

| 错误码 | 说明 |
|--------|------|
| 200 | 成功 |
| 400 | 请求参数错误 |
| 401 | 未授权 |
| 403 | 禁止访问 |
| 404 | 资源不存在 |
| 500 | 服务器内部错误 |

### 业务错误码

| 错误码 | 说明 |
|--------|------|
| 1001 | 处方序号不能为空 |
| 1002 | 处方明细ID不能为空 |
| 1003 | 追溯码不能为空 |
| 1004 | 追溯码已被使用 |
| 1005 | 未找到该追溯码信息 |
| 1006 | 未找到相关发药明细信息 |
| 1007 | 处方序号和发药单ID不能都为空 |
| 1008 | 扫码任务ID不能为空 |
| 1009 | 部分处方校验失败，所有数据未保存 |

## 认证说明

部分接口需要通过 `@SaasAuthorizationVerify` 认证，请在请求头中包含有效的授权信息：

```
Authorization: Bearer <your-token>
```

## 数据字典

### 发药单状态 (fg_status)

| 值 | 说明 |
|----|------|
| 0 | 待发药 |
| 1 | 已发药 |
| 2 | 已退药 |

### 任务状态 (fg_status)

| 值 | 说明 |
|----|------|
| 0 | 待处理 |
| 1 | 已完成 |
| 2 | 已取消 |

### 扫码状态 (fg_scanned)

| 值 | 说明 |
|----|------|
| 0 | 未扫码 |
| 1 | 已扫码 |

### 业务类型 (sd_tc_status)

| 值 | 说明 |
|----|------|
| 3001 | 门诊发药 |
| 3002 | 住院发药 |

### 追溯码类型 (sd_tc)

| 值 | 说明 |
|----|------|
| 1 | 整箱码 |
| 2 | 商品追溯码 |

## 定时任务说明

系统中配置了以下定时任务来处理后台业务：

### 1. 同步发药时间任务

- **任务名称**: `syncDispenseTimeTask`
- **执行周期**: 每5分钟执行一次
- **Cron表达式**: `0 0/5 * * * ?`
- **功能描述**: 定时同步阜阳肿瘤医院系统的发药时间数据，确保发药记录的时间准确性
- **执行时间**: 每小时的第0、5、10、15、20、25、30、35、40、45、50、55分钟执行

### 2. 上传3505数据到平台任务

- **任务名称**: `uploadDataToPlatformTask`
- **执行周期**: 每3小时执行一次
- **Cron表达式**: `0 0 */3 * * ?`
- **功能描述**: 定时将医保3505销售记录数据上传到医保平台，满足监管要求
- **执行时间**: 每天的0点、3点、6点、9点、12点、15点、18点、21点执行

### 3. 生成追溯码数据并上传SuperSet任务

- **任务名称**: `uploadTraceabilityDataToSuperSetTask`
- **执行周期**: 每2小时执行一次
- **Cron表达式**: `0 0 */2 * * ?`
- **功能描述**: 生成药品追溯码相关数据并上传到SuperSet平台进行数据分析和可视化
- **执行时间**: 每天的偶数小时点执行（0点、2点、4点、6点、8点、10点、12点、14点、16点、18点、20点、22点）

### 4. 同步HIS药品字典任务

- **任务名称**: `syncHisDrugDictionaryTask`
- **执行周期**: 每天下午1点20分执行一次
- **Cron表达式**: `0 20 13 * * ?`
- **功能描述**: 定时从HIS系统同步药品字典数据到本地数据库，确保药品信息的准确性和及时性
- **执行时间**: 每天下午13:20执行

### 任务执行时间规律

| 时间点 | 发药时间同步 | 3505数据上传 | SuperSet数据上传 | HIS药品字典同步 |
|--------|-------------|-------------|-----------------|----------------|
| 00:00 | ✓ | ✓ | ✓ | |
| 00:05 | ✓ | | | |
| 00:10 | ✓ | | | |
| 00:15 | ✓ | | | |
| 00:20 | ✓ | | | |
| 00:25 | ✓ | | | |
| 00:30 | ✓ | | | |
| 00:35 | ✓ | | | |
| 00:40 | ✓ | | | |
| 00:45 | ✓ | | | |
| 00:50 | ✓ | | | |
| 00:55 | ✓ | | | |
| 01:00 | ✓ | | | |
| 02:00 | ✓ | | ✓ | |
| 03:00 | ✓ | ✓ | | |
| 13:20 | ✓ | | | ✓ |

**注意事项**:
- 所有定时任务都配置了异常处理机制，失败时会记录错误日志
- 任务执行期间如果系统重启，会在下一个执行周期自动恢复
- 建议在业务低峰期监控任务执行状态，确保数据及时同步

## 注意事项

1. **时间格式**: 所有时间参数统一使用 `yyyy-MM-dd HH:mm:ss` 格式
2. **追溯码格式**: 多个追溯码用逗号分隔
3. **分页参数**: pageNum 从 1 开始，pageSize 建议不超过 100
4. **请求限制**: 追溯码上传接口建议单次上传不超过 50 个处方
5. **数据一致性**: 追溯码上传采用事务机制，确保数据一致性
6. **字符编码**: 请求和响应均使用 UTF-8 编码

## 联系方式

如有问题，请联系开发团队。 