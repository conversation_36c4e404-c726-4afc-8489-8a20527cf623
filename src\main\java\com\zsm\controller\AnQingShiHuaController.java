package com.zsm.controller;

import com.zsm.common.SaasAuthorizationVerify;
import com.zsm.model.ApiResult;
import com.zsm.model.dto.*;
import com.zsm.model.vo.*;
import com.zsm.his.impl.AnQingShiHuaService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.web.bind.annotation.*;

import jakarta.annotation.Resource;
import java.util.List;

/**
 * <p>
 * 安庆石化 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-02
 */
@Deprecated
@Tag(name = "安庆石化", description = "追溯码业务接口")
// @RestController
// @RequestMapping("/anQingShiHua")
public class AnQingShiHuaController {

    @Resource
    private AnQingShiHuaService anQingShiHuaService;

    /**
     * 查询门诊处方接口（默认查询）
     *
     * @return 门诊处方数据
     */
    @PostMapping("/queryOutpatientPrescription")
    @Operation(summary = "查询门诊处方")
    @SaasAuthorizationVerify
    public ApiResult<List<OutpatientPrescriptionResponseVo.PrescriptionItem>> queryOutpatientPrescription(@RequestBody OutpatientPrescriptionQueryDto queryDto) {
        return anQingShiHuaService.queryOutpatientPrescription(queryDto);
    }

    /**
     * 住院领药单接口
     *
     * @param queryDto 查询参数
     * @return 住院处方数据
     */
    @PostMapping("/queryInpatientPrescription")
    @Operation(summary = "住院领药单接口")
    @SaasAuthorizationVerify
    public ApiResult<List<InpatientPrescriptionResponseVo.InpatientPrescriptionItem>> queryInpatientPrescription(@RequestBody InpatientPrescriptionQueryDto queryDto) {
        return anQingShiHuaService.queryInpatientPrescription(queryDto);
    }


    /**
     * 同步药品字典接口
     *
     * @return 同步结果
     */
    @PostMapping("/syncDrugDictionary")
    @Operation(summary = "同步his药品字典", description = "从HIS系统同步药品字典数据到本地数据库")
    public ApiResult<String> syncDrugDictionary() {
        return anQingShiHuaService.syncDrugDictionary();
    }
}
