package com.zsm.mapper;

import com.zsm.entity.Nhsa3505;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * <p>
 * 3505销售记录报文表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-02
 */
@Mapper
public interface Nhsa3505Mapper extends BaseMapper<Nhsa3505> {
/**
     * 根据处方序号和药品code查询Nhsa3505
     * @param cfxh 处方序号
     * @param drugCode 药品code
     * @return Nhsa3505
     */
    Nhsa3505 getByCfxhandDrugCode(@Param("cfxh") String cfxh, @Param("drugCode") String drugCode);

    /**
     * 根据处方明细序号查询Nhsa3505
     * @param outPresdetailid 处方明细序号
     * @return Nhsa3505
     */
    Nhsa3505 getByBchno(@Param("outPresdetailid") String outPresdetailid);

    /**
     * 根据药品code查询Nhsa3505，限制hsa_sync_status='0'，只取第一条记录
     * @param drugCode 药品code (对应fixmedins_hilist_id字段)
     * @return Nhsa3505
     */
    Nhsa3505 getFirstByDrugCodeWithSyncStatus(@Param("drugCode") String drugCode);
}
