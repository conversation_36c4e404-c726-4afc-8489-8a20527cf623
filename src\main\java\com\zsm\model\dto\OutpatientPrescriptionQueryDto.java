package com.zsm.model.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 门诊处方查询请求DTO
 *
 * <AUTHOR>
 * @date 2025-06-02
 */
@Data
@Schema(description = "门诊处方查询请求参数")
public class OutpatientPrescriptionQueryDto {

    @Schema(description = "处方序号，多个用逗号分隔。对应his系统的唯一识别号", example = "O250529003426,O250529003503")
    private String cfxh;

    @Schema(description = "门诊/住院病历号")
    private String blh;

    @Schema(description = "身份证号")
    private String sfzh;

    @Schema(description = "患者ID，主要是用来确保内门诊和急诊药房通过患者id获取数据，获取可以发药的数据", example = "")
    private String patient_id;

    @Schema(description = "开始时间，yyyy-MM-dd HH:mm:ss，在不传处方号或者患者id的情况下，需要能够根据时间来获取到当天的发药数据，为了后续统计工作服务", example = "")
    private String startTime;

    @Schema(description = "结束时间，yyyy-MM-dd HH:mm:ss", example = "")
    private String endTime;

    @Schema(description = "发药单标记ID，发药单标记（0：返药；1：退药）", example = "0")
    private String fg_dps;

    @Schema(description = "发送标识，发送标识（0：未发送；1：已发送；2：已退药）", example = "1")
    private String send_flag;

    /**
     * 默认构造函数
     */
    public OutpatientPrescriptionQueryDto() {
        // 设置默认值
        this.patient_id = "";
        this.startTime = "";
        this.endTime = "";
        this.fg_dps = "0";
        this.send_flag = "1";
    }

    /**
     * 带参数构造函数
     */
    public OutpatientPrescriptionQueryDto(String cfxh) {
        this();
        this.cfxh = cfxh;
    }
} 