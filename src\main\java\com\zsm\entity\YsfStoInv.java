package com.zsm.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;

/**
 * 仓储库存记录实体类
 * 
 * <AUTHOR>
 * @since 2025-06-02
 */
@Data
@TableName("ysf_sto_inv")
@Schema(description = "仓储库存记录")
public class YsfStoInv implements Serializable {

    private static final long serialVersionUID = 1L;


    /**
     * 药品库存主键
     */
    @Schema(description = "药品库存主键")
    @TableId(value = "id_sto_inv", type = IdType.AUTO)
    private Long idStoInv;

    /**
     * 商品编码
     */
    @Schema(description = "商品编码")
    @Size(max = 100, message = "商品编码长度不能超过100个字符")
    @TableField("drug_code")
    private String drugCode;

    /**
     * 库存数量;包装单位为hi_sto_med中的unit_sale
     */
    @Schema(description = "库存数量;包装单位为hi_sto_med中的unit_sale")
    @TableField("amount")
    private Integer amount;

    /**
     * 零售价格;hi_sto_med中每1(unit_sale)的价格
     */
    @Schema(description = "零售价格;hi_sto_med中每1(unit_sale)的价格")
    @TableField("price_sale")
    private BigDecimal priceSale;

    /**
     * 进货价格
     */
    @Schema(description = "进货价格")
    @TableField("price_pur")
    private BigDecimal pricePur;

    /**
     * 药品批次
     */
    @Schema(description = "药品批次")
    @Size(max = 100, message = "药品批次长度不能超过100个字符")
    @TableField("manu_lotnum")
    private String manuLotnum;

    /**
     * 药品效期
     */
    @Schema(description = "药品效期")
    @TableField("expy_end")
    private LocalDateTime expyEnd;

    /**
     * 有效标识;0: 失效, 1:有效
     */
    @Schema(description = "有效标识;0: 失效, 1:有效")
    @Size(max = 100, message = "有效标识;0: 失效, 1:有效长度不能超过100个字符")
    @TableField("fg_active")
    private String fgActive;

    /**
     * 备注
     */
    @Schema(description = "备注")
    @Size(max = 100, message = "备注长度不能超过100个字符")
    @TableField("memo")
    private String memo;

    /**
     * 仓储主键
     */
    @Schema(description = "仓储主键")
    @Size(max = 100, message = "仓储主键长度不能超过100个字符")
    @TableField("id_sto")
    private String idSto;

    /**
     * 机构编号
     */
    @Schema(description = "机构编号")
    @Size(max = 100, message = "机构编号长度不能超过100个字符")
    @TableField("id_org")
    private String idOrg;

    /**
     * 医疗机构编码
     */
    @Schema(description = "医疗机构编码")
    @Size(max = 100, message = "医疗机构编码长度不能超过100个字符")
    @TableField("org_id")
    private String orgId;

    /**
     * 医疗机构名称
     */
    @Schema(description = "医疗机构名称")
    @NotBlank(message = "医疗机构名称不能为空")
    @Size(max = 30, message = "医疗机构名称长度不能超过30个字符")
    @TableField("org_name")
    private String orgName;

    /**
     * 乐观锁
     */
    @Schema(description = "乐观锁")
    @Size(max = 100, message = "乐观锁长度不能超过100个字符")
    @TableField("revision")
    private String revision;

    /**
     * 创建者
     */
    @Schema(description = "创建者")
    @Size(max = 100, message = "创建者长度不能超过100个字符")
    @TableField("create_by")
    private String createBy;

    /**
     * 创建时间
     */
    @Schema(description = "创建时间")
    @TableField("create_time")
    private LocalDateTime createTime;

    /**
     * 更新者
     */
    @Schema(description = "更新者")
    @Size(max = 100, message = "更新者长度不能超过100个字符")
    @TableField("update_by")
    private String updateBy;

    /**
     * 更新时间
     */
    @Schema(description = "更新时间")
    @TableField("update_time")
    private LocalDateTime updateTime;

    /**
     * 备注
     */
    @Schema(description = "备注")
    @Size(max = 500, message = "备注长度不能超过500个字符")
    @TableField("remark")
    private String remark;

    /**
     * 删除标志（0代表存在 1代表删除）
     */
    @Schema(description = "删除标志（0代表存在 1代表删除）")
    @Size(max = 100, message = "删除标志（0代表存在 1代表删除）长度不能超过100个字符")
    @TableField("del_flag")
    private String delFlag;
} 
