package com.zsm.his;


import com.zsm.model.vo.DispensingDetailVo;

/**
 * 发药服务接口
 *
 * <AUTHOR>
 * @date 2025-06-10
 */
public interface DispensingService {

    /**
     * 根据业务子ID查询发药明细
     *
     * @param bizSubId 业务子ID，通常是发药单明细ID或处方明细ID
     * @return 发药明细信息
     */
    DispensingDetailVo getDetailByBizSubId(String bizSubId);

    /**
     * 同步阜阳人医发药时间
     */
    void syncDispenseTime();

    /**
     * 同步安庆石化发药时间
     */
    void syncAnQingShiHuaDispenseTime();

    /**
     * 同步阜阳肿瘤发药时间
     */
    void syncFuYangZhongLiuDispenseTime();
} 