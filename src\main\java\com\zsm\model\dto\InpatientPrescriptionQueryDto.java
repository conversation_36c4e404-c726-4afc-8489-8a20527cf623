package com.zsm.model.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 住院处方查询请求DTO
 *
 * <AUTHOR>
 * @date 2025-06-02
 */
@Data
@Schema(description = "住院处方查询请求参数")
public class InpatientPrescriptionQueryDto {

    @Schema(description = "发药科室id", example = "")
    private String deptId;

    @Schema(description = "开始时间（可以查询某某一天或某某几天的处方数据）", example = "yyyy-MM-ddHH:mm:ss")
    private String start_time;

    @Schema(description = "结束时间（根据这2个条件来获取记录id查询出科室的处方总是数据）", example = "yyyy-MM-ddHH:mm:ss")
    private String end_time;

    @Schema(description = "患者病区名称", example = "")
    private String pat_ward_name;

    @Schema(description = "发药记录id", example = "PHADISP2025052302672")
    private String record_id;

    @Schema(description = "发药单标记（0-发药；1-退药，建议必填，但这议提供以过，非发药的都选择数据）", example = "0")
    private String fg_dps;
    
    @Schema(description = "患者ID", example = "")
    private String patientId;
    
    @Schema(description = "住院号", example = "")
    private String inpatientNo;
    
    @Schema(description = "处方序号", example = "")
    private String cfxh;
    
    @Schema(description = "出院带药标志（0-否；1-是）", example = "")
    private String cydybz;

    /**
     * 默认构造函数
     */
    public InpatientPrescriptionQueryDto() {
        // 设置默认值
        this.deptId = "";
        this.start_time = "";
        this.end_time = "";
        this.pat_ward_name = "";
        this.record_id = "";
        this.fg_dps = "0";
        this.patientId = "";
        this.inpatientNo = "";
        this.cfxh = "";
        this.cydybz = "0";
    }

    /**
     * 带参数构造函数
     */
    public InpatientPrescriptionQueryDto(String recordId) {
        this();
        this.record_id = recordId;
    }
} 