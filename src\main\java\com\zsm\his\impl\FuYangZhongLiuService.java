package com.zsm.his.impl;

import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.zsm.common.SaasAuthorizationVerifyAspect;
import com.zsm.constant.DrugDictAccountConstant;
import com.zsm.constant.NhsaAccountConstant;
import com.zsm.entity.ViewYsfMzfycf;
import com.zsm.entity.ViewYsfYpcdmlk;
import com.zsm.entity.ViewYsfZyfycf;
import com.zsm.entity.YsfStoTcTask;
import com.zsm.his.HisPrescriptionService;
import com.zsm.mapper.YsfStoTcTaskMapper;
import com.zsm.model.ApiResult;
import com.zsm.model.dto.InpatientPrescriptionQueryDto;
import com.zsm.model.dto.OutpatientPrescriptionQueryDto;
import com.zsm.model.enums.TaskStatusEnum;
import com.zsm.model.saas.request.*;
import com.zsm.model.saas.response.*;
import com.zsm.model.vo.InpatientPrescriptionResponseVo;
import com.zsm.model.vo.OutpatientPrescriptionResponseVo;
import com.zsm.model.vo.SaasUserInfoResponse;
import com.zsm.service.Nhsa3505Service;
import com.zsm.service.ViewYsfMzfycfService;
import com.zsm.service.ViewYsfYpcdmlkService;
import com.zsm.service.ViewYsfZyfycfService;
import com.zsm.utils.DataCleanUtil;
import com.zsm.utils.SaasHttpUtil;
import com.zsm.utils.SpringUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import jakarta.annotation.Resource;
import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 阜阳肿瘤业务服务实现类
 *
 * <AUTHOR>
 * @since 2025-06-25
 */
@Slf4j
@Service()
public class FuYangZhongLiuService implements HisPrescriptionService {

    @Resource
    private ViewYsfZyfycfService viewYsfZyfycfService;
    @Resource
    private ViewYsfMzfycfService viewYsfMzfycfService;
    @Resource
    private ViewYsfYpcdmlkService viewYsfYpcdmlkService;
    @Resource
    private YsfStoTcTaskMapper ysfStoTcTaskMapper;
    @Resource
    private Nhsa3505Service nhsa3505Service;


    @Override
    public ApiResult<List<OutpatientPrescriptionResponseVo.PrescriptionItem>> queryOutpatientPrescription(OutpatientPrescriptionQueryDto queryDto) {
        try {
            log.info("开始查询门诊处方信息，参数: {}", queryDto);

            if (queryDto == null) {
                queryDto = new OutpatientPrescriptionQueryDto();
            }

            // SaasUserInfoResponse userInfo = SaasAuthorizationVerifyAspect.userInfoThreadLocal.get();
            // String fyyfId = userInfo.getUser().getFyyfId().trim();

            final List<ViewYsfMzfycf> entityList = SpringUtils.getAopProxy(this).getYsfMzDrugDetailList(queryDto);

            // List<ViewYsfMzfycf> newList = new ArrayList<>();
            // if ("405".equals(fyyfId) || "406".equals(fyyfId)) {
            //     entityList.forEach(item -> {
            //         if (item.getFyyf().trim().equals(fyyfId)) {
            //             newList.add(item);
            //         }
            //     });
            // } else {
            //     newList.addAll(entityList);
            // }
            log.info("根据当前用户药房id过滤后的数据，共{}条，his视图数据详情：{}", entityList.size(), JSONUtil.toJsonStr(entityList));

            List<OutpatientPrescriptionResponseVo.PrescriptionItem> voList = entityList.stream()
                    .map(this::convertYsfToPrescriptionItem).collect(Collectors.toList());

            log.info("门诊处方查询成功，返回{}条数据", voList.size());

            if (!voList.isEmpty()) {
                this.handleTracCodgStoreCydy(voList);

                this.filterAndAttachTaskForOutpatient(voList);

                nhsa3505Service.save3505Async(voList);
            }

            return ApiResult.success(voList);

        } catch (Exception e) {
            String errorMsg = "查询门诊处方异常: " + e.getMessage();
            log.error(errorMsg, e);
            return ApiResult.error(errorMsg);
        }
    }

    @DS("his")
    public List<ViewYsfMzfycf> getYsfMzDrugDetailList(OutpatientPrescriptionQueryDto queryDto) {
        log.info("使用数据源：his，开始查询阜阳肿瘤门诊药品明细");

        String startTime = queryDto.getStartTime();
        String endTime = queryDto.getEndTime();

        if (StringUtils.isEmpty(startTime) && StringUtils.isEmpty(endTime)) {
            DateTime yesterday = DateUtil.yesterday();
            startTime = DateUtil.format(yesterday, "yyyy-MM-dd");

            DateTime tomorrow = DateUtil.offsetDay(DateUtil.date(), 1);
            endTime = DateUtil.format(tomorrow, "yyyy-MM-dd");

            log.info("使用默认时间范围：{} 到 {}", startTime, endTime);
        }

        LambdaQueryWrapper<ViewYsfMzfycf> queryWrapper = new LambdaQueryWrapper<>();

        if (StringUtils.isNotEmpty(queryDto.getPatient_id())) {
            queryWrapper.eq(ViewYsfMzfycf::getPatid, queryDto.getPatient_id());
        }
        if (StringUtils.isNotEmpty(queryDto.getBlh())) {
            queryWrapper.eq(ViewYsfMzfycf::getBlh, queryDto.getBlh());
        }
        if (StringUtils.isNotEmpty(queryDto.getSfzh())) {
            queryWrapper.eq(ViewYsfMzfycf::getSfzh, queryDto.getSfzh());
        }
        if (StringUtils.isNotEmpty(startTime)) {
            queryWrapper.ge(ViewYsfMzfycf::getSelRetnTime, startTime);
        }
        if (StringUtils.isNotEmpty(endTime)) {
            queryWrapper.lt(ViewYsfMzfycf::getSelRetnTime, endTime);
        }
        if (StringUtils.isNotEmpty(queryDto.getCfxh())) {
            String[] cfxhArray = queryDto.getCfxh().split(",");
            if (cfxhArray.length == 1) {
                queryWrapper.eq(ViewYsfMzfycf::getCfxh, cfxhArray[0].trim());
            } else {
                List<String> cfxhList = Arrays.stream(cfxhArray).map(String::trim).collect(Collectors.toList());
                queryWrapper.in(ViewYsfMzfycf::getCfxh, cfxhList);
            }
        }
        // 根据帐号的药房id过滤
        final SaasUserInfoResponse saasUserInfoResponse = SaasAuthorizationVerifyAspect.userInfoThreadLocal.get();
        if (saasUserInfoResponse != null && saasUserInfoResponse.getUser().getFyyfId() != null) {
            queryWrapper.eq(ViewYsfMzfycf::getFyyf, saasUserInfoResponse.getUser().getFyyfId());
        }

        try {
            List<ViewYsfMzfycf> result = viewYsfMzfycfService.list(queryWrapper);
            final List<ViewYsfMzfycf> viewYsfMzfycfs = DataCleanUtil.cleanViewData(result);
            log.info("getYsfMzDrugDetailList - 查询完成，返回{}条记录", result.size());
            log.debug("getYsfMzDrugDetailList - 查询完成，返回数据详情：{}", JSONUtil.toJsonStr(viewYsfMzfycfs));
            return viewYsfMzfycfs;
        } catch (Exception e) {
            log.error("getYsfMzDrugDetailList - 查询异常：{}", e.getMessage(), e);
            throw e;
        }
    }


    private Integer safeParseInteger(String value) {
        if (StringUtils.isEmpty(value)) {
            return 0;
        }

        try {
            if (value.contains(".")) {
                Double doubleValue = Double.valueOf(value);
                return doubleValue.intValue();
            } else {
                return Integer.valueOf(value);
            }
        } catch (NumberFormatException e) {
            log.warn("无法将字符串 '{}' 转换为整数，使用默认值0", value);
            return 0;
        }
    }


    private void handleTracCodgStoreCydy(List<OutpatientPrescriptionResponseVo.PrescriptionItem> zsmViewList) {
        SaasUserInfoResponse userInfo = SaasAuthorizationVerifyAspect.userInfoThreadLocal.get();

        if (userInfo == null) {
            log.warn("SaaS用户信息为空，跳过追溯码库存处理");
            return;
        }

        List<QueryTracDrugRequest> queryTracDrugRequestList = zsmViewList.stream()
                .filter(i -> StringUtils.isNotEmpty(i.getMin_sel_retn_cnt()) && StringUtils.isNotEmpty(i.getCfmxxh()))
                .map(i -> QueryTracDrugRequest.builder().cfxh(i.getCfxh()).cfmxxh(i.getCfmxxh())
                        .drugCode(i.getFixmedins_hilist_id()).dispCnt(safeParseInteger(i.getMin_sel_retn_cnt())).build())
                .collect(Collectors.toList());
        Map<String, GetTracCodgStoreDataResponse> tracCodgStoreMap = new HashMap<>();

        try {
            Map<String, QueryTracDrugResponse> queryTracDrugMap = SaasHttpUtil.queryTracDrugYzMap(userInfo.getAuthorization(), queryTracDrugRequestList);

            List<GetTracCodgStoreDataRequest> dataList = new ArrayList<>();
            for (OutpatientPrescriptionResponseVo.PrescriptionItem i : zsmViewList) {
                if (queryTracDrugMap.containsKey(i.getCfmxxh())) {
                    QueryTracDrugResponse queryTracDrug = queryTracDrugMap.get(i.getCfmxxh());
                    i.setTrdn_flag(Integer.valueOf(queryTracDrug.getIsTrac()));
                    i.setHis_con_ratio(String.valueOf(queryTracDrug.getConRatio()));
                }
                if (i.getTrdn_flag() != null && i.getTrdn_flag() == 1 && StringUtils.isNotEmpty(i.getMin_sel_retn_cnt())) {
                    GetTracCodgStoreDataRequest build = GetTracCodgStoreDataRequest.builder().cfxh(i.getCfxh())
                            .cfmxxh(i.getCfmxxh()).dispCnt(safeParseInteger(i.getMin_sel_retn_cnt()))
                            .drugCode(i.getFixmedins_hilist_id()).build();
                    dataList.add(build);
                }
            }

            if (!ObjectUtils.isEmpty(dataList)) {
                List<GetTracCodgStoreDataResponse> tracCodgStore = SaasHttpUtil.getTracCodgStore(userInfo.getAuthorization(), GetTracCodgStoreRequest.builder()
                        .dataList(dataList).build());
                tracCodgStoreMap = tracCodgStore.stream()
                        .collect(Collectors.toMap(GetTracCodgStoreDataResponse::getCfmxxh, Function.identity(), (o, n) -> n));
            }
        } catch (Exception e) {
            log.error("调用SaaS平台接口处理追溯码库存信息失败", e);
        }

        for (OutpatientPrescriptionResponseVo.PrescriptionItem i : zsmViewList) {
            if (i.getTrdn_flag() != null && i.getTrdn_flag() == 1) {
                if (tracCodgStoreMap.containsKey(i.getCfmxxh())) {
                    GetTracCodgStoreDataResponse tracCodgStoreData = tracCodgStoreMap.get(i.getCfmxxh());
                    i.setDrugCode(tracCodgStoreData.getDrugCode());
                    i.setDrugTracCodgs(tracCodgStoreData.getDrugTracCodgs());
                    i.setTracCodgStore(tracCodgStoreData);
                    i.setDispCnt(tracCodgStoreData.getDispCnt());
                    i.setCurrNum(tracCodgStoreData.getCurrNum());
                }
            }
        }
    }

    /**
     * 过滤已完成任务并为门诊处方项附加最近一次待处理/已过期的任务信息
     *
     * @param voList 门诊处方项目集合
     */
    private void filterAndAttachTaskForOutpatient(List<OutpatientPrescriptionResponseVo.PrescriptionItem> voList) {
        Iterator<OutpatientPrescriptionResponseVo.PrescriptionItem> iterator = voList.iterator();
        while (iterator.hasNext()) {
            OutpatientPrescriptionResponseVo.PrescriptionItem dispenseInfo = iterator.next();
            String outPresId = dispenseInfo.getCfxh();
            if (StringUtils.isEmpty(outPresId)) {
                continue;
            }

            LambdaQueryWrapper<YsfStoTcTask> completedTaskQuery = new LambdaQueryWrapper<>();
            completedTaskQuery.eq(YsfStoTcTask::getCdBiz, outPresId)
                    .eq(YsfStoTcTask::getFgStatus, TaskStatusEnum.COMPLETED.getCode())
                    .eq(YsfStoTcTask::getDelFlag, "0");
            Long completedTaskCount = ysfStoTcTaskMapper.selectCount(completedTaskQuery);

            if (completedTaskCount > 0) {
                log.info("存在已完成的任务，过滤掉该处方数据，处方号：{}", outPresId);
                iterator.remove();
                continue;
            }

            LambdaQueryWrapper<YsfStoTcTask> latestTaskQuery = new LambdaQueryWrapper<>();
            latestTaskQuery.eq(YsfStoTcTask::getCdBiz, outPresId)
                    .in(YsfStoTcTask::getFgStatus, Arrays.asList(TaskStatusEnum.PENDING.getCode(), TaskStatusEnum.EXPIRED.getCode()))
                    .eq(YsfStoTcTask::getDelFlag, "0")
                    .orderByDesc(YsfStoTcTask::getIdTask).last("limit 1");

            YsfStoTcTask latestTask = ysfStoTcTaskMapper.selectOne(latestTaskQuery);

            if (latestTask != null) {
                dispenseInfo.setTaskIdDps(latestTask.getIdTask() != null ? latestTask.getIdTask().toString() : null);
                dispenseInfo.setTaskFgStatusDps(latestTask.getFgStatus());
                dispenseInfo.setTaskScanTimeDps(latestTask.getCreateTime() != null ? latestTask.getCreateTime()
                        .format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")) : null);
            }
        }
    }

    /**
     * his视图实体转换为门诊处方项目
     *
     * @param e e
     * @return {@link OutpatientPrescriptionResponseVo.PrescriptionItem }
     */
    private OutpatientPrescriptionResponseVo.PrescriptionItem convertYsfToPrescriptionItem(ViewYsfMzfycf e) {
        OutpatientPrescriptionResponseVo.PrescriptionItem item = new OutpatientPrescriptionResponseVo.PrescriptionItem();

        item.setMed_list_codg(e.getMedListCodg());
        item.setFixmedins_hilist_id(e.getFixmedinsHilistId());
        item.setFixmedins_hilist_name(e.getFixmedinsHilistName());
        item.setFixmedins_bchno(e.getFixmedinsBchno());

        item.setPrsc_dr_name(e.getPrscDrName());
        item.setPhar_name(e.getPharName());
        item.setPhar_prac_cert_no(e.getPharPracCertNo());

        item.setMdtrt_sn(e.getMdtrtSn());
        item.setPsn_name(e.getPsnName());

        item.setManu_lotnum(e.getManuLotnum());
        // 安全设置生产日期，防止格式异常
        if (StringUtils.isNotEmpty(e.getManuDate())) {
            try {
                // 简单验证日期格式是否正常（包含数字且不是无效格式）
                String dateStr = e.getManuDate().trim();
                if (dateStr.matches(".*\\d{4}.*\\d{1,2}.*\\d{1,2}.*")) {
                    item.setManu_date(dateStr);
                } else {
                    log.warn("生产日期格式异常，跳过设置: {}", dateStr);
                }
            } catch (Exception ex) {
                log.warn("生产日期处理异常，跳过设置: {}, 错误: {}", e.getManuDate(), ex.getMessage());
            }
        }
        item.setExpy_end(e.getExpyEnd());

        if (e.getRxFlag() != null) {
            item.setRx_flag(e.getRxFlag());
        }
        if (StringUtils.isNotEmpty(e.getTrdnFlag())) {
            try {
                item.setTrdn_flag(Integer.valueOf(e.getTrdnFlag()));
            } catch (NumberFormatException ignored) {
            }
        }

        if (e.getRtalDocno() != null) {
            item.setRtal_docno(String.valueOf(e.getRtalDocno()));
        }

        if (e.getSelRetnCnt() != null) {
            item.setSel_retn_cnt(e.getSelRetnCnt().toPlainString());
        }
        if (e.getMinSelRetnCnt() != null) {
            item.setMin_sel_retn_cnt(e.getMinSelRetnCnt().toPlainString());
        }

        item.setSel_retn_time(e.getSelRetnTime());
        item.setSel_retn_opter_name(e.getSelRetnOpterName());

        if (StringUtils.isNotEmpty(e.getMdtrtSetlType())) {
            try {
                item.setMdtrt_setl_type(Integer.valueOf(e.getMdtrtSetlType()));
            } catch (NumberFormatException ignored) {
            }
        }

        item.setSpec(e.getSpec());
        item.setProdentp_name(e.getProdentpName());

        if (e.getCfxh() != null) {
            item.setCfxh(String.valueOf(e.getCfxh()));
        }
        if (e.getCfmxxh() != null) {
            item.setCfmxxh(String.valueOf(e.getCfmxxh()));
        }

        item.setSjh(e.getSjh());
        item.setPatient_id(e.getPatid());
        item.setFyyf(e.getFyyf());

        item.setSend_time(e.getSendTime());
        if (StringUtils.isNotEmpty(e.getSendTime())) {
            item.setSend_flag(1);
        } else {
            item.setSend_flag(0);
        }

        if (item.getTrdn_flag() == null) {
            item.setTrdn_flag(0);
        }

        item.setHis_con_ratio(e.getHisConRatio());

        if (!StringUtils.isNotEmpty(item.getBchno()) && e.getCfmxxh() != null) {
            item.setBchno(String.valueOf(e.getCfmxxh()));
        }

        if (!StringUtils.isNotEmpty(item.getRxno()) && e.getCfxh() != null) {
            item.setRxno("RX" + e.getCfxh());
        }

        return item;
    }


    @Override
    public ApiResult<List<InpatientPrescriptionResponseVo.InpatientPrescriptionItem>> queryInpatientPrescription(InpatientPrescriptionQueryDto queryDto) {
        try {
            log.info("开始查询住院处方信息，参数: {}", queryDto);

            if (queryDto == null) {
                queryDto = new InpatientPrescriptionQueryDto();
            }

            final List<ViewYsfZyfycf> newList = SpringUtils.getAopProxy(this).getYsfZyDrugDetailList(queryDto);

            log.info("查询到的住院药品数据，共{}条，his视图数据详情：{}", newList.size(),JSONUtil.toJsonStr(newList));

            List<InpatientPrescriptionResponseVo.InpatientPrescriptionItem> voList = newList.stream()
                    .map(this::convertYsfToInpatientPrescriptionItem).collect(Collectors.toList());

            log.info("住院处方查询成功，返回{}条数据,转换类型后的数据:{}", voList.size(),JSONUtil.toJsonStr(voList));

            if (!voList.isEmpty()) {
                this.handleTracCodgStoreZydy(voList);

                this.filterAndAttachTaskForInpatient(voList);

                nhsa3505Service.save3505ZyAsync(voList);
            }

            return ApiResult.success(voList);

        } catch (Exception e) {
            String errorMsg = "查询住院处方异常: " + e.getMessage();
            log.error(errorMsg, e);
            return ApiResult.error(errorMsg);
        }
    }

    @DS("his")
    public List<ViewYsfZyfycf> getYsfZyDrugDetailList(InpatientPrescriptionQueryDto queryDto) {
        log.info("使用数据源：his，开始查询阜阳肿瘤住院药品明细");

        // String startTime = queryDto.getStart_time();
        // String endTime = queryDto.getEnd_time();
        //
        // if (StringUtils.isEmpty(startTime) && StringUtils.isEmpty(endTime)) {
        //     DateTime yesterday = DateUtil.yesterday();
        //     startTime = DateUtil.format(yesterday, "yyyy-MM-dd");
        //
        //     DateTime tomorrow = DateUtil.offsetDay(DateUtil.date(), 1);
        //     endTime = DateUtil.format(tomorrow, "yyyy-MM-dd");
        //
        //     log.info("getYsfZyDrugDetailList - 使用默认时间范围：{} 到 {}", startTime, endTime);
        // }

        LambdaQueryWrapper<ViewYsfZyfycf> queryWrapper = new LambdaQueryWrapper<>();

        if (StringUtils.isNotEmpty(queryDto.getPatientId())) {
            queryWrapper.eq(ViewYsfZyfycf::getPatientId, queryDto.getPatientId());
        }
        // if (StringUtils.isNotEmpty(startTime)) {
        //     queryWrapper.ge(ViewYsfZyfycf::getSelRetnTime, startTime);
        // }
        // if (StringUtils.isNotEmpty(endTime)) {
        //     queryWrapper.lt(ViewYsfZyfycf::getSelRetnTime, endTime);
        // }
        if (StringUtils.isNotEmpty(queryDto.getRecord_id())) {
            queryWrapper.eq(ViewYsfZyfycf::getRecordId, queryDto.getRecord_id());
        }
        if (StringUtils.isNotEmpty(queryDto.getCfxh())) {
            queryWrapper.eq(ViewYsfZyfycf::getCfxh, queryDto.getCfxh());
        }
        if (StringUtils.isNotEmpty(queryDto.getDeptId())) {
            queryWrapper.eq(ViewYsfZyfycf::getFyyf, queryDto.getDeptId());
        }

        try {
            List<ViewYsfZyfycf> result = viewYsfZyfycfService.list(queryWrapper);
            final List<ViewYsfZyfycf> viewYsfZyfycfs = DataCleanUtil.cleanViewData(result);
            log.info("getYsfZyDrugDetailList - 查询完成，返回{}条记录", result.size());
            return viewYsfZyfycfs;
        } catch (Exception e) {
            log.error("getYsfZyDrugDetailList - 查询异常：{}", e.getMessage(), e);
            throw e;
        }
    }

    private void handleTracCodgStoreZydy(List<InpatientPrescriptionResponseVo.InpatientPrescriptionItem> zsmViewList) {
        SaasUserInfoResponse userInfo = SaasAuthorizationVerifyAspect.userInfoThreadLocal.get();

        if (userInfo == null) {
            log.warn("SaaS用户信息为空，跳过追溯码库存处理");
            return;
        }

        List<QueryTracDrugRequest> queryTracDrugRequestList = zsmViewList.stream()
                .filter(i -> StringUtils.isNotEmpty(i.getMin_sel_retn_cnt()) && StringUtils.isNotEmpty(i.getCfmxxh()))
                .map(i -> QueryTracDrugRequest.builder().cfxh(i.getCfxh()).cfmxxh(i.getCfmxxh())
                        .drugCode(i.getFixmedins_hilist_id()).dispCnt(safeParseInteger(i.getMin_sel_retn_cnt())).build())
                .collect(Collectors.toList());
        Map<String, GetTracCodgStoreDataResponse> tracCodgStoreMap = new HashMap<>();

        try {
            Map<String, QueryTracDrugResponse> queryTracDrugMap = SaasHttpUtil.queryTracDrugYzMap(userInfo.getAuthorization(), queryTracDrugRequestList);

            List<GetTracCodgStoreDataRequest> dataList = new ArrayList<>();
            for (InpatientPrescriptionResponseVo.InpatientPrescriptionItem i : zsmViewList) {
                if (queryTracDrugMap.containsKey(i.getCfmxxh())) {
                    QueryTracDrugResponse queryTracDrug = queryTracDrugMap.get(i.getCfmxxh());
                    i.setTrdn_flag(Integer.valueOf(queryTracDrug.getIsTrac()));
                    i.setHis_con_ratio(String.valueOf(queryTracDrug.getConRatio()));
                }
                if (i.getTrdn_flag() != null && i.getTrdn_flag() == 1 && StringUtils.isNotEmpty(i.getMin_sel_retn_cnt())) {
                    GetTracCodgStoreDataRequest build = GetTracCodgStoreDataRequest.builder().cfxh(i.getCfxh())
                            .cfmxxh(i.getCfmxxh()).dispCnt(safeParseInteger(i.getMin_sel_retn_cnt()))
                            .drugCode(i.getFixmedins_hilist_id()).build();
                    dataList.add(build);
                }
            }

            if (!ObjectUtils.isEmpty(dataList)) {
                List<GetTracCodgStoreDataResponse> tracCodgStore = SaasHttpUtil.getTracCodgStore(userInfo.getAuthorization(), GetTracCodgStoreRequest.builder()
                        .dataList(dataList).build());
                tracCodgStoreMap = tracCodgStore.stream()
                        .collect(Collectors.toMap(GetTracCodgStoreDataResponse::getCfmxxh, Function.identity(), (o, n) -> n));
            }
        } catch (Exception e) {
            log.error("调用SaaS平台接口处理追溯码库存信息失败", e);
        }

        for (InpatientPrescriptionResponseVo.InpatientPrescriptionItem i : zsmViewList) {
            if (i.getTrdn_flag() != null && i.getTrdn_flag() == 1) {
                if (tracCodgStoreMap.containsKey(i.getCfmxxh())) {
                    GetTracCodgStoreDataResponse tracCodgStoreData = tracCodgStoreMap.get(i.getCfmxxh());
                    i.setDrugCode(tracCodgStoreData.getDrugCode());
                    i.setDrugTracCodgs(tracCodgStoreData.getDrugTracCodgs());
                    i.setTracCodgStore(tracCodgStoreData);
                    i.setDispCnt(tracCodgStoreData.getDispCnt());
                    i.setCurrNum(tracCodgStoreData.getCurrNum());
                }
            }
        }
    }

    /**
     * 过滤已完成任务并为住院处方项附加最近一次待处理/已过期的任务信息
     *
     * @param voList 住院处方项目集合
     */
    private void filterAndAttachTaskForInpatient(List<InpatientPrescriptionResponseVo.InpatientPrescriptionItem> voList) {
        Iterator<InpatientPrescriptionResponseVo.InpatientPrescriptionItem> iterator = voList.iterator();
        while (iterator.hasNext()) {
            InpatientPrescriptionResponseVo.InpatientPrescriptionItem dispenseInfo = iterator.next();
            String inPresId = dispenseInfo.getCfxh();
            if (StringUtils.isEmpty(inPresId)) {
                continue;
            }

            LambdaQueryWrapper<YsfStoTcTask> completedTaskQuery = new LambdaQueryWrapper<>();
            completedTaskQuery.eq(YsfStoTcTask::getCdBiz, inPresId)
                    .eq(YsfStoTcTask::getFgStatus, TaskStatusEnum.COMPLETED.getCode())
                    .eq(YsfStoTcTask::getDelFlag, "0");
            Long completedTaskCount = ysfStoTcTaskMapper.selectCount(completedTaskQuery);

            if (completedTaskCount > 0) {
                log.info("存在已完成的任务，过滤掉该处方数据，处方号：{}", inPresId);
                iterator.remove();
                continue;
            }

            LambdaQueryWrapper<YsfStoTcTask> latestTaskQuery = new LambdaQueryWrapper<>();
            latestTaskQuery.eq(YsfStoTcTask::getCdBiz, inPresId)
                    .in(YsfStoTcTask::getFgStatus, Arrays.asList(TaskStatusEnum.PENDING.getCode(), TaskStatusEnum.EXPIRED.getCode()))
                    .eq(YsfStoTcTask::getDelFlag, "0")
                    .orderByDesc(YsfStoTcTask::getIdTask).last("limit 1");

            YsfStoTcTask latestTask = ysfStoTcTaskMapper.selectOne(latestTaskQuery);

            if (latestTask != null) {
                dispenseInfo.setTaskIdDps(latestTask.getIdTask() != null ? latestTask.getIdTask().toString() : null);
                dispenseInfo.setTaskFgStatusDps(latestTask.getFgStatus());
                dispenseInfo.setTaskScanTimeDps(latestTask.getCreateTime() != null ? latestTask.getCreateTime()
                        .format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")) : null);
            }
        }
    }

    /**
     * his视图实体转换为住院处方项目
     *
     * @param e e
     * @return {@link InpatientPrescriptionResponseVo.InpatientPrescriptionItem }
     */
    private InpatientPrescriptionResponseVo.InpatientPrescriptionItem convertYsfToInpatientPrescriptionItem(ViewYsfZyfycf e) {
        InpatientPrescriptionResponseVo.InpatientPrescriptionItem item = new InpatientPrescriptionResponseVo.InpatientPrescriptionItem();

        item.setMed_list_codg(e.getMedListCodg());
        item.setFixmedins_hilist_id(String.valueOf(e.getFixmedinsHilistId()));
        item.setFixmedins_hilist_name(e.getFixmedinsHilistName());
        item.setFixmedins_bchno(e.getFixmedinsBchno());

        item.setPrsc_dr_certno(e.getPrscDrCertno());
        item.setPrsc_dr_name(e.getPrscDrName());
        item.setPhar_certno(e.getPharCertno());
        item.setPhar_name(e.getPharName());
        item.setPhar_prac_cert_no(e.getPharPracCertNo());

        item.setMdtrt_sn(e.getMdtrtSn());
        item.setPsn_name(e.getPsnName());

        item.setManu_lotnum(e.getManuLotnum());

        // 安全设置生产日期，防止格式异常
        if (StringUtils.isNotEmpty(e.getManuDate())) {
            try {
                // 简单验证日期格式是否正常（包含数字且不是无效格式）
                String dateStr = e.getManuDate().trim();
                if (dateStr.matches(".*\\d{4}.*\\d{1,2}.*\\d{1,2}.*")) {
                    item.setManu_date(dateStr);
                } else {
                    log.warn("生产日期格式异常，跳过设置: {}", dateStr);
                }
            } catch (Exception ex) {
                log.warn("生产日期处理异常，跳过设置: {}, 错误: {}", e.getManuDate(), ex.getMessage());
            }
        }

        item.setExpy_end(e.getExpyEnd());

        if (StringUtils.isNotEmpty(e.getRxFlag())) {
            try {
                item.setRx_flag(Integer.valueOf(e.getRxFlag()));
            } catch (NumberFormatException ignored) {
            }
        }
        if (StringUtils.isNotEmpty(e.getTrdnFlag())) {
            try {
                item.setTrdn_flag(Integer.valueOf(e.getTrdnFlag()));
            } catch (NumberFormatException ignored) {
            }
        }

        if (e.getRxno() != null) {
            item.setRxno(e.getRxno());
        }
        item.setRx_circ_flag(e.getRxCircFlag());
        item.setRtal_docno(e.getRtalDocno());
        item.setStoout_no(e.getStooutNo());
        item.setBchno(e.getBchno());

        if (e.getSelRetnCnt() != null) {
            item.setSel_retn_cnt(e.getSelRetnCnt().toPlainString());
        }
        if (e.getMinSelRetnCnt() != null) {
            item.setMin_sel_retn_cnt(e.getMinSelRetnCnt().toPlainString());
        }

        if (e.getSelRetnTime() != null) {
            item.setSel_retn_time(e.getSelRetnTime());
        }
        item.setSel_retn_opter_name(e.getSelRetnOpterName());

        if (StringUtils.isNotEmpty(e.getMdtrtSetlType())) {
            try {
                item.setMdtrt_setl_type(Integer.valueOf(e.getMdtrtSetlType()));
            } catch (NumberFormatException ignored) {
            }
        }

        item.setSpec(e.getSpec());
        item.setProdentp_name(e.getProdentpName());

        if (e.getCfxh() != null) {
            item.setCfxh(String.valueOf(e.getCfxh()));
        }
        if (e.getCfmxxh() != null) {
            item.setCfmxxh(String.valueOf(e.getCfmxxh()));
        }

        if (e.getSjh() != null) {
            item.setSjh(String.valueOf(e.getSjh()));
        }
        item.setPatient_id(e.getPatientId());
        item.setFyyf(e.getFyyf());

        if (item.getTrdn_flag() == null) {
            item.setTrdn_flag(0);
        }

        if (e.getHisConRatio() != null) {
            item.setHis_con_ratio(e.getHisConRatio());
        } else {
            item.setHis_con_ratio("1");
        }

        if (!StringUtils.isNotEmpty(item.getBchno()) && e.getCfmxxh() != null) {
            item.setBchno(String.valueOf(e.getCfmxxh()));
        }

        if (!StringUtils.isNotEmpty(item.getRxno()) && e.getRxno() != null) {
            item.setRxno(String.valueOf(e.getRxno()));
        }

        // 新增字段赋值
        // HIS系统中的药品唯一编码
        if (e.getFixmedinsHilistId() != null) {
            item.setHis_drug_code(String.valueOf(e.getFixmedinsHilistId()));
        }

        // 发药单标记：根据出院带药标志判断，0-发药，1-退药
        if (e.getCydybz() != null && e.getCydybz() == 1) {
            item.setFg_dps("1"); // 出院带药可能视为特殊发药类型
        } else {
            item.setFg_dps("0"); // 默认为发药
        }

        // 其他新增字段设置默认值或null
        // 这些字段在ViewYsfZyfycf中没有对应源数据，需要根据业务需求设置
        item.setRecord_detail_id(null); // 发药明细id - 需要业务系统提供
        item.setPat_ward_id(null);      // 病区id - 需要业务系统提供
        item.setPat_in_hos_id(null);    // 患者住院唯一标识 - 需要业务系统提供
        item.setOrder_id(null);         // 医嘱id - 需要业务系统提供

        // 时间字段处理
        item.setSend_time(e.getSelRetnTime());   // 发送时间 - 可根据业务需求设置
        item.setReturn_time(null); // 返回时间 - 可根据业务需求设置

        item.setSend_flag(0);

        return item;
    }


    @Override
    public ApiResult<String> syncDrugDictionary() {
        log.info("开始同步HIS药品字典到DMH平台");

        try {
            AccessTokenReponse userInfo = SaasHttpUtil.getAccessToken(DrugDictAccountConstant.user, DrugDictAccountConstant.password);
            if (userInfo.getReturnCode() != 0) {
                log.error("获取DMH平台AccessToken失败，返回码：{}，返回信息：{}", userInfo.getReturnCode(), userInfo.getReturnMsg());
                return ApiResult.error("获取DMH平台AccessToken失败");
            }

            List<ViewYsfYpcdmlk> allYsfDrugList = SpringUtils.getAopProxy(this).getYsfDrugDictList();
            if (allYsfDrugList.isEmpty()) {
                log.warn("从HIS系统未获取到任何药品数据");
                return ApiResult.success("未获取到药品数据，同步完成");
            }

            log.info("从HIS系统共获取到{}条药品数据", allYsfDrugList.size());

            List<HisDrugInfoSaasApiData> convertedDrugList = convertYsfHisDrugsToDmhFormat(allYsfDrugList);
            log.info("成功转换{}条药品数据", convertedDrugList.size());

            int batchSize = 200;
            int totalBatches = (int) Math.ceil((double) convertedDrugList.size() / batchSize);
            int uploadedCount = 0;

            for (int i = 0; i < totalBatches; i++) {
                int fromIndex = i * batchSize;
                int toIndex = Math.min((i + 1) * batchSize, convertedDrugList.size());
                List<HisDrugInfoSaasApiData> batchData = convertedDrugList.subList(fromIndex, toIndex);

                boolean batchSuccess = uploadDrugBatchToDmh(batchData, i + 1, totalBatches, userInfo);
                if (batchSuccess) {
                    uploadedCount += batchData.size();
                    log.info("第{}/{}批药品数据上传成功，当前批次{}条，累计上传{}条", i + 1, totalBatches, batchData.size(), uploadedCount);
                } else {
                    log.error("第{}/{}批药品数据上传失败", i + 1, totalBatches);
                    return ApiResult.error(String.format("第%d批药品数据上传失败，已成功上传%d条", i + 1, uploadedCount));
                }

                if (i < totalBatches - 1) {
                    try {
                        Thread.sleep(1000);
                    } catch (InterruptedException e) {
                        Thread.currentThread().interrupt();
                        log.warn("同步过程中断");
                        break;
                    }
                }
            }

            String successMessage = String.format("药品字典同步完成！共处理%d条数据，成功上传%d条", convertedDrugList.size(), uploadedCount);
            log.info(successMessage);
            return ApiResult.success(successMessage);

        } catch (Exception e) {
            log.error("同步药品字典异常", e);
            return ApiResult.error("同步药品字典失败：" + e.getMessage());
        }
    }

    @DS("his")
    public List<ViewYsfYpcdmlk> getYsfDrugDictList() {
        return DataCleanUtil.cleanViewData(viewYsfYpcdmlkService.list());
    }

    private List<HisDrugInfoSaasApiData> convertYsfHisDrugsToDmhFormat(List<ViewYsfYpcdmlk> hisDrugList) {
        log.info("开始转换阜阳肿瘤药品数据格式，共{}条待转换", hisDrugList.size());

        List<HisDrugInfoSaasApiData> convertedList = new ArrayList<>();
        int convertedCount = 0;
        int skippedCount = 0;

        for (ViewYsfYpcdmlk hisItem : hisDrugList) {
            try {
                HisDrugInfoSaasApiData dmhItem = new HisDrugInfoSaasApiData();

                if (hisItem.getIdm() != null) {
                    dmhItem.setHisDrugId(String.valueOf(hisItem.getIdm()));
                } else {
                    log.warn("药品ID为空，跳过该药品：{}", JSONUtil.toJsonStr(hisItem));
                    skippedCount++;
                    continue;
                }

                dmhItem.setHisDrugName(hisItem.getYpmc());
                dmhItem.setHisDrugCountryCode(hisItem.getDydmSgsyb());
                dmhItem.setHisDrugCountryName(hisItem.getYpmc());
                dmhItem.setHisDrugSpec(hisItem.getYpgg());

                if (hisItem.getYkxs() != null) {
                    dmhItem.setHisPac(hisItem.getYkxs().toPlainString());
                } else {
                    dmhItem.setHisPac("1");
                }

                dmhItem.setHisPackUnit(hisItem.getJhdw());
                dmhItem.setHisDrugManufacturerCode(hisItem.getCjdm());
                dmhItem.setHisDrugManufacturerName(hisItem.getCjmc());
                dmhItem.setHisPurchaseUnit(hisItem.getJhdw());
                if (StringUtils.isNotEmpty(hisItem.getYpfj())) {
                    try {
                        dmhItem.setHisPurchasePrice(new BigDecimal(hisItem.getYpfj()));
                    } catch (NumberFormatException e) {
                        dmhItem.setHisPurchasePrice(BigDecimal.ZERO);
                    }
                } else {
                    dmhItem.setHisPurchasePrice(BigDecimal.ZERO);
                }
                dmhItem.setHisDoseForm(null);
                dmhItem.setHisApprovalNum(hisItem.getPzwh());
                dmhItem.setHisDosUnit(hisItem.getZxdw());
                dmhItem.setHisPacUnit(hisItem.getJhdw());

                if (hisItem.getYkxs() != null) {
                    dmhItem.setHisConRatio(hisItem.getYkxs());
                } else {
                    dmhItem.setHisConRatio(BigDecimal.ONE);
                }

                dmhItem.setDelFlag(0);

                dmhItem.setHisEnterpriseCode(null);
                dmhItem.setHisEnterpriseName(null);
                dmhItem.setWholeQuantity(null);
                dmhItem.setHisDiscRate(null);
                dmhItem.setMemo(null);

                convertedList.add(dmhItem);
                convertedCount++;

                if (convertedCount % 1000 == 0) {
                    log.info("药品数据转换进度：已转换{}条", convertedCount);
                }

            } catch (Exception e) {
                log.error("转换药品数据异常，跳过该药品：idm={}, ypmc={}, 错误信息：{}", hisItem.getIdm(), hisItem.getYpmc(), e.getMessage());
                skippedCount++;

                if (skippedCount > 0 && skippedCount % 100 == 0) {
                    log.warn("已连续跳过{}条记录，请检查数据质量", skippedCount);
                }
            }
        }

        log.info("药品数据转换完成！总计处理：{}条，成功转换：{}条，跳过：{}条", hisDrugList.size(), convertedCount, skippedCount);

        if (skippedCount > convertedCount * 0.1) {
            log.warn("跳过记录数量较多({})，占总数的{:.2f}%，建议检查源数据质量", skippedCount, (double) skippedCount / hisDrugList.size() * 100);
        }

        return convertedList;
    }

    private boolean uploadDrugBatchToDmh(List<HisDrugInfoSaasApiData> batchData, int batchIndex, int totalBatches, AccessTokenReponse userInfo) {
        try {
            HisDrugInfoSaasRequest request = new HisDrugInfoSaasRequest();
            request.setMedicalCode(NhsaAccountConstant.getNhsaAccount().getMedicalCode());
            request.setMedicalName(NhsaAccountConstant.getNhsaAccount().getMedicalName());
            request.setDataList(batchData);

            String timestamp = new SimpleDateFormat("yyyyMMddHHmmss").format(new Date());
            String requestID = String.format("DRUG-SYNC-%s-B%03d", timestamp, batchIndex);
            request.setRequestID(requestID);

            log.info("开始上传第{}/{}批药品数据到DMH平台，批次大小：{}，requestID：{}", batchIndex, totalBatches, batchData.size(), requestID);

            SaasCommmonListResponse<UploadHisDrugInfoResponse> response = SaasHttpUtil.uploadHisDrugInfo(userInfo.getAuthorization(), request);

            if (Objects.equals(response.getReturnCode(), 0)) {
                log.info("第{}/{}批药品数据上传成功，requestID：{}", batchIndex, totalBatches, requestID);
                return true;
            } else {
                log.error("第{}/{}批药品数据上传失败，requestID：{}，响应：{}", batchIndex, totalBatches, requestID, JSONUtil.toJsonStr(response));
                return false;
            }

        } catch (Exception e) {
            log.error("第{}/{}批药品数据上传异常", batchIndex, totalBatches, e);
            return false;
        }
    }
}