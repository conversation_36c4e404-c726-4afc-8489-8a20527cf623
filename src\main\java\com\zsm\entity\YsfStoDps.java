package com.zsm.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;
import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 发药单实体类
 * 
 * <AUTHOR>
 * @since 2025-06-02
 */
@Data
@TableName("ysf_sto_dps")
@Schema(description = "发药单")
public class YsfStoDps implements Serializable {

    private static final long serialVersionUID = 1L;


    /**
     * 发药单主键
     */
    @Schema(description = "发药单主键")
    @TableId(value = "id_dps", type = IdType.AUTO)
    private Long idDps;

    /**
     * 发药单号
     */
    @Schema(description = "发药单号")
    @Size(max = 100, message = "发药单号长度不能超过100个字符")
    @TableField("cfxh")
    private String cfxh;

    /**
     * 发药单类型;1: 住院处方, 2:门诊处方
     */
    @Schema(description = "发药单类型;1: 住院处方, 2:门诊处方")
    @Size(max = 100, message = "发药单类型;1: 住院处方, 2:门诊处方")
    @TableField("sd_dps")
    private String sdDps;

    /**
     * 发药时间
     */
    @Schema(description = "发药时间")
    @TableField("send_time")
    private LocalDateTime sendTime;

    /**
     * 患者ID
     */
    @Schema(description = "患者ID")
    @Size(max = 100, message = "患者ID长度不能超过100个字符")
    @TableField("id_pi")
    private String idPi;

    /**
     * 患者ID
     */
    @Schema(description = "患者ID")
    @Size(max = 100, message = "患者ID长度不能超过100个字符")
    @TableField("patient_id")
    private String patientId;

    /**
     * 患者姓名
     */
    @Schema(description = "患者姓名")
    @NotBlank(message = "患者姓名不能为空")
    @Size(max = 30, message = "患者姓名长度不能超过30个字符")
    @TableField("psn_name")
    private String psnName;

    /**
     * 就诊卡号
     */
    @Schema(description = "就诊卡号")
    @Size(max = 100, message = "就诊卡号长度不能超过100个字符")
    @TableField("card_no")
    private String cardNo;

    /**
     * 票据号码
     */
    @Schema(description = "票据号码")
    @Size(max = 100, message = "票据号码长度不能超过100个字符")
    @TableField("sn_bill")
    private String snBill;

    /**
     * 结算主键
     */
    @Schema(description = "结算主键")
    @Size(max = 100, message = "结算主键长度不能超过100个字符")
    @TableField("id_stl")
    private String idStl;

    /**
     * 处方主键
     */
    @Schema(description = "处方主键")
    @Size(max = 100, message = "处方主键长度不能超过100个字符")
    @TableField("id_pres")
    private String idPres;

    /**
     * 药师执业资格证号
     */
    @Schema(description = "药师执业资格证号")
    @Size(max = 100, message = "药师执业资格证号长度不能超过100个字符")
    @TableField("phar_prac_cert_no")
    private String pharPracCertNo;

    /**
     * 开单医生姓名
     */
    @Schema(description = "开单医生姓名")
    @NotBlank(message = "开单医生姓名不能为空")
    @Size(max = 30, message = "开单医生姓名长度不能超过30个字符")
    @TableField("prsc_dr_name")
    private String prscDrName;

    /**
     * 开单医生;医疗场景
     */
    @Schema(description = "开单医生;医疗场景")
    @Size(max = 100, message = "开单医生;医疗场景长度不能超过100个字符")
    @TableField("id_doc")
    private String idDoc;

    /**
     * 仓储ID
     */
    @Schema(description = "仓储ID")
    @Size(max = 100, message = "仓储ID长度不能超过100个字符")
    @TableField("id_sto")
    private String idSto;

    /**
     * 发药窗口ID
     */
    @Schema(description = "发药窗口ID")
    @Size(max = 100, message = "发药窗口ID长度不能超过100个字符")
    @TableField("id_sto_win")
    private String idStoWin;

    /**
     * 发药单状态;发药单 0:待发药,1:已发药,7:部分退药，8:全部退药，9:作废  退药单:8:已退药未退费,9:已退费
     */
    @Schema(description = "发药单状态;发药单 0:待发药,1:已发药,7:部分退药，8:全部退药，9:作废  退药单:8:已退药未退费,9:已退费")
    @Size(max = 100, message = "发药单状态;发药单 0:待发药,1:已发药,7:部分退药，8:全部退药，9:作废  退药单:8:已退药未退费,9:已退费长度不能超过100个字符")
    @TableField("fg_status")
    private String fgStatus;

    /**
     * 打印标识
     */
    @Schema(description = "打印标识")
    @Size(max = 100, message = "打印标识长度不能超过100个字符")
    @TableField("fg_print")
    private String fgPrint;

    /**
     * 发药科室id，也就是发药药房的id，由此关联出是门诊药房还是住院药房
     */
    @Schema(description = "发药科室id，也就是发药药房的id，由此关联出是门诊药房还是住院药房")
    @Size(max = 100, message = "发药科室id，也就是发药药房的id，由此关联出是门诊药房还是住院药房长度不能超过100个字符")
    @TableField("id_dept")
    private String idDept;

    /**
     * 发药人;退药时作为退药人
     */
    @Schema(description = "发药人;退药时作为退药人")
    @NotBlank(message = "发药人;退药时作为退药人不能为空")
    @Size(max = 30, message = "发药人;退药时作为退药人长度不能超过30个字符")
    @TableField("sel_retn_opter_name")
    private String selRetnOpterName;

    /**
     * 配药人
     */
    @Schema(description = "配药人")
    @Size(max = 100, message = "配药人长度不能超过100个字符")
    @TableField("user_check")
    private String userCheck;

    /**
     * 退药单标记;0: 发药, 1: 退药
     */
    @Schema(description = "退药单标记;0: 发药, 1: 退药")
    @Size(max = 100, message = "退药单标记;0: 发药, 1: 退药长度不能超过100个字符")
    @TableField("fg_dps")
    private String fgDps;

    /**
     * 原发药单主键;用于退药单，原发药单id
     */
    @Schema(description = "原发药单主键;用于退药单，原发药单id")
    @Size(max = 100, message = "原发药单主键;用于退药单，原发药单id长度不能超过100个字符")
    @TableField("ori_id_dps")
    private String oriIdDps;

    /**
     * 扫码任务主键
     */
    @Schema(description = "扫码任务主键")
    @TableField("id_task")
    private Long idTask;

    /**
     * 机构编号;开单机构
     */
    @Schema(description = "机构编号;开单机构")
    @Size(max = 100, message = "机构编号;开单机构长度不能超过100个字符")
    @TableField("id_org")
    private String idOrg;

    /**
     * 医疗机构编码
     */
    @Schema(description = "医疗机构编码")
    @Size(max = 100, message = "医疗机构编码长度不能超过100个字符")
    @TableField("org_id")
    private String orgId;

    /**
     * 医疗机构名称
     */
    @Schema(description = "医疗机构名称")
    @NotBlank(message = "医疗机构名称不能为空")
    @Size(max = 30, message = "医疗机构名称长度不能超过30个字符")
    @TableField("org_name")
    private String orgName;

    /**
     * 乐观锁
     */
    @Schema(description = "乐观锁")
    @Size(max = 100, message = "乐观锁长度不能超过100个字符")
    @TableField("revision")
    private String revision;

    /**
     * 创建者
     */
    @Schema(description = "创建者")
    @Size(max = 100, message = "创建者长度不能超过100个字符")
    @TableField("create_by")
    private String createBy;

    /**
     * 创建时间
     */
    @Schema(description = "创建时间")
    @TableField("create_time")
    private LocalDateTime createTime;

    /**
     * 更新者
     */
    @Schema(description = "更新者")
    @Size(max = 100, message = "更新者长度不能超过100个字符")
    @TableField("update_by")
    private String updateBy;

    /**
     * 更新时间
     */
    @Schema(description = "更新时间")
    @TableField("update_time")
    private LocalDateTime updateTime;

    /**
     * 备注
     */
    @Schema(description = "备注")
    @Size(max = 500, message = "备注长度不能超过500个字符")
    @TableField("remark")
    private String remark;

    /**
     * 删除标志（0代表存在 1代表删除）
     */
    @Schema(description = "删除标志（0代表存在 1代表删除）")
    @Size(max = 100, message = "删除标志（0代表存在 1代表删除）长度不能超过100个字符")
    @TableField("del_flag")
    private String delFlag;

    /**
     * 窗口
     */
    @Schema(description = "窗口")
    @TableField("`window`")
    private String window;

    /**
     * 选择退回操作员ID
     */
    @Schema(description = "选择退回操作员ID")
    @TableField("sel_retn_opter_id")
    private String selRetnOpterId;

    /**
     * 患者病区ID
     */
    @Schema(description = "患者病区ID")
    @Size(max = 100, message = "患者病区ID长度不能超过100个字符")
    @TableField("pat_ward_id")
    private String patWardId;

    /**
     * 患者病区名称
     */
    @Schema(description = "患者病区名称")
    @Size(max = 100, message = "患者病区名称长度不能超过100个字符")
    @TableField("pat_ward_name")
    private String patWardName;

} 
