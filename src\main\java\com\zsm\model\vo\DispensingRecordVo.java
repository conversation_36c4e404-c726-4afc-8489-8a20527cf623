package com.zsm.model.vo;

import com.zsm.entity.YsfStoDps;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 发药单列表响应VO
 *
 * <AUTHOR>
 * @date 2025/06/03
 */
@Data
@Schema(description = "发药单列表响应VO")
public class DispensingRecordVo extends YsfStoDps {

    @Schema(description = "发药单状态 (fg_status from ysf_sto_dps)")
    private String dispensingFgStatus;

    @Schema(description = "关联的最新任务状态 (fg_status from ysf_sto_tc_task)")
    private String taskStatus;

    @Schema(description = "关联的最新任务ID (id_task from ysf_sto_tc_task)")
    private Long taskId;

} 