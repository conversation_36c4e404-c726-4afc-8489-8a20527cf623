package com.zsm.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.math.BigDecimal;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 霍邱1院 门诊发药处方视图实体类
 * 
 * <AUTHOR>
 * @since 2025-06-10
 */
@Data
@TableName("VIEW_MZFYCF")
@Schema(description = "门诊发药处方视图")
public class ViewMzfycf implements Serializable {

    private static final long serialVersionUID = 1L;


    /**
     * 患者id
     */
    @Schema(description = "患者id")
    @TableField("patid")
    private String patid;

    /**
     * 规格
     */
    @Schema(description = "规格")
    @TableField("spec")
    private String spec;

    /**
     * 生产企业名称
     */
    @Schema(description = "生产企业名称")
    @TableField("prodentp_name")
    private String prodentpName;

    /**
     * 医疗目录编码
     */
    @Schema(description = "医疗目录编码")
    @TableField("med_list_codg")
    private String medListCodg;

    /**
     * 定点医药机构目录编号
     */
    @Schema(description = "定点医药机构目录编号")
    @TableField("fixmedins_hilist_id")
    private String fixmedinsHilistId;

    /**
     * 定点医药机构目录名称
     */
    @Schema(description = "定点医药机构目录名称")
    @TableField("fixmedins_hilist_name")
    private String fixmedinsHilistName;

    /**
     * 处方序号
     */
    @Schema(description = "处方序号")
    @TableField("cfxh")
    private String cfxh;

    /**
     * 处方明细序号
     */
    @Schema(description = "处方明细序号")
    @TableField("cfmxxh")
    private String cfmxxh;

    /**
     * 定点医药机构批次流水号
     */
    @Schema(description = "定点医药机构批次流水号")
    @TableField("fixmedins_bchno")
    private String fixmedinsBchno;

    /**
     * 开方医师姓名
     */
    @Schema(description = "开方医师姓名")
    @TableField("prsc_dr_name")
    private String prscDrName;

    /**
     * 药师姓名
     */
    @Schema(description = "药师姓名")
    @TableField("phar_name")
    private String pharName;

    /**
     * 药师执业资格证号
     */
    @Schema(description = "药师执业资格证号")
    @TableField("phar_prac_cert_no")
    private String pharPracCertNo;

    /**
     * 处方药标志
     */
    @Schema(description = "处方药标志")
    @TableField("rx_flag")
    private Integer rxFlag;

    /**
     * 拆零标志
     */
    @Schema(description = "拆零标志")
    @TableField("trdn_flag")
    private String trdnFlag;

    /**
     * 零售单据号
     */
    @Schema(description = "零售单据号")
    @TableField("rtal_docno")
    private String rtalDocno;

    /**
     * 销售/退货数量
     */
    @Schema(description = "销售/退货数量")
    @TableField("sel_retn_cnt")
    private BigDecimal selRetnCnt;
    /**
     * 销售/退货单位（药品级别）,比如:盒
     * 本次发药对应的数量单位。 取该药品的实际发放单位（比如说 2 盒，单位是盒；5 支，单位是支；30 粒，单位是粒）。建议必填。
     */
    @Schema(description = "销售/退货单位（药品级别）,比如:盒")
    @TableField("sel_retn_unit")
    private String selRetnUnit;


    /**
     * 最小单位销售数量
     * 如果本次发的是2盒，一盒24粒，这个就是48，如果本次发了2支，那么就是2
     */
    @Schema(description = "最小单位销售数量")
    @TableField("min_sel_retn_cnt")
    private String minSelRetnCnt;

    /**
     * 转换比
     * 制剂单位和包装单位的转换比，规格如果是20粒/盒，那么这个就是20 (建议必填)
     */
    @Schema(description = "转换比")
    @TableField("his_con_ratio")
    private BigDecimal hisConRatio;

    /**
     * his的剂量单位
     * 规格如果是20粒/盒，那么这个就是粒 (建议必填)
     */
    // @Schema(description = "his的剂量单位")
    // @TableField("his_dos_unit")
    // private String hisDosUnit;
    /**
     * his的剂量单位
     * 规格如果是20粒/盒，那么这个就是盒 (建议必填)
     */
    // @Schema(description = "his的剂量单位")
    // @TableField("his_pac_unit")
    // private String hisPacUnit;


    /**
     * 销售/退货时间
     */
    @Schema(description = "销售/退货时间")
    @TableField("sel_retn_time")
    private String selRetnTime;

    /**
     * 销售/退货经办人姓名
     */
    @Schema(description = "销售/退货经办人姓名")
    @TableField("sel_retn_opter_name")
    private String selRetnOpterName;

    /**
     * 就诊结算类型
     */
    @Schema(description = "就诊结算类型")
    @TableField("MDTRT_SETL_TYPE")
    private String mdtrtSetlType;

    /**
     * 生产批号
     */
    @Schema(description = "生产批号")
    @TableField("manu_lotnum")
    private String manuLotnum;

    /**
     * 生产日期
     */
    @Schema(description = "生产日期")
    @TableField("manu_date")
    private String manuDate;

    /**
     * 有效期止
     */
    @Schema(description = "有效期止")
    @TableField("expy_end")
    private String expyEnd;

    /**
     * 就医流水号
     */
    @Schema(description = "就医流水号")
    @TableField("mdtrt_sn")
    private String mdtrtSn;

    /**
     * 收据号
     */
    @Schema(description = "收据号")
    @TableField("sjh")
    private String sjh;

    /**
     * 人员姓名
     */
    @Schema(description = "人员姓名")
    @TableField("psn_name")
    private String psnName;

    /**
     * 
     */
    @Schema(description = "")
    @TableField("hjxh")
    private String hjxh;

    /**
     * 发药时间
     */
    @Schema(description = "发药时间")
    @TableField("send_time")
    private String sendTime;

    /**
     * 发药药房id
     */
    @Schema(description = "发药药房id")
    @TableField("fyyf")
    private String fyyf;

    /**
     * 发药药房名称
     */
    @Schema(description = "发药药房名称")
    @TableField("fyyfName")
    private String fyyfName;

    /**
     * 窗口号
     */
    @Schema(description = "窗口号")
    @TableField("windows")
    private String windows;
    /**
     * 门诊/住院病历号-唯一号
     */
    @Schema(description = "门诊/住院病历号-唯一号")
    @TableField("blh")
    private String blh;
} 
