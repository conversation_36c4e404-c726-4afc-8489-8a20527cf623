package com.zsm.utils;

import com.zsm.entity.ViewYsfMzfycf;
import com.zsm.entity.ViewYsfYpcdmlk;
import com.zsm.entity.ViewYsfYpzd;
import com.zsm.entity.ViewYsfZyfycf;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 数据清洗工具使用示例
 * 展示如何在服务类中使用DataCleanUtil进行数据清洗
 * 
 * <AUTHOR>
 * @date 2025/07/01
 */
@Slf4j
@Component
public class DataCleanUtilExample {

    /**
     * 示例1：清洗门诊发药处方数据
     * 在查询门诊处方数据后进行清洗
     */
    public List<ViewYsfMzfycf> cleanOutpatientPrescriptionData(List<ViewYsfMzfycf> prescriptionList) {
        log.info("开始清洗门诊处方数据，记录数：{}", prescriptionList != null ? prescriptionList.size() : 0);
        
        // 使用数据清洗工具清洗数据
        List<ViewYsfMzfycf> cleanedList = DataCleanUtil.cleanViewData(prescriptionList);
        
        log.info("门诊处方数据清洗完成");
        return cleanedList;
    }

    /**
     * 示例2：清洗住院发药处方数据
     * 在查询住院处方数据后进行清洗
     */
    public List<ViewYsfZyfycf> cleanInpatientPrescriptionData(List<ViewYsfZyfycf> prescriptionList) {
        log.info("开始清洗住院处方数据，记录数：{}", prescriptionList != null ? prescriptionList.size() : 0);
        
        // 使用数据清洗工具清洗数据
        List<ViewYsfZyfycf> cleanedList = DataCleanUtil.cleanViewData(prescriptionList);
        
        log.info("住院处方数据清洗完成");
        return cleanedList;
    }

    /**
     * 示例3：清洗药品字典数据
     * 在查询药品字典数据后进行清洗
     */
    public List<ViewYsfYpcdmlk> cleanDrugDictionaryData(List<ViewYsfYpcdmlk> drugList) {
        log.info("开始清洗药品字典数据，记录数：{}", drugList != null ? drugList.size() : 0);
        
        // 使用数据清洗工具清洗数据
        List<ViewYsfYpcdmlk> cleanedList = DataCleanUtil.cleanViewData(drugList);
        
        log.info("药品字典数据清洗完成");
        return cleanedList;
    }

    /**
     * 示例4：清洗药品字典数据（另一种）
     */
    public List<ViewYsfYpzd> cleanDrugData(List<ViewYsfYpzd> drugList) {
        log.info("开始清洗药品数据，记录数：{}", drugList != null ? drugList.size() : 0);
        
        // 使用数据清洗工具清洗数据
        List<ViewYsfYpzd> cleanedList = DataCleanUtil.cleanViewData(drugList);
        
        log.info("药品数据清洗完成");
        return cleanedList;
    }

    /**
     * 示例5：在服务方法中集成数据清洗
     * 展示如何在实际的业务方法中使用
     */
    public List<ViewYsfMzfycf> queryAndCleanOutpatientPrescription(String patientId, String startTime, String endTime) {
        // 1. 查询原始数据（这里模拟查询过程）
        // List<ViewYsfMzfycf> rawData = mapper.selectByCondition(patientId, startTime, endTime);
        
        // 2. 进行数据清洗
        // List<ViewYsfMzfycf> cleanedData = DataCleanUtil.cleanViewData(rawData);
        
        // 3. 返回清洗后的数据
        // return cleanedData;
        
        // 实际使用时，可以链式调用：
        // return DataCleanUtil.cleanViewData(mapper.selectByCondition(patientId, startTime, endTime));
        
        return null; // 示例代码
    }

    /**
     * 示例6：自定义清洗逻辑
     * 展示如何结合自定义清洗规则
     */
    public List<ViewYsfMzfycf> customCleanPrescriptionData(List<ViewYsfMzfycf> prescriptionList) {
        // 使用自定义清洗函数
        return DataCleanUtil.cleanList(prescriptionList, prescription -> {
            // 先使用通用清洗
            DataCleanUtil.cleanObject(prescription);
            
            // 再进行自定义清洗逻辑
            if (prescription.getMdtrtSetlType() != null) {
                // 特殊字段的处理逻辑
                String setlType = prescription.getMdtrtSetlType().trim();
                if ("-".equals(setlType) || "".equals(setlType)) {
                    prescription.setMdtrtSetlType(null);
                }
            }
            
            // 处理日期字段的特殊情况
            if (prescription.getManuDate() != null && prescription.getManuDate().contains("  -  ")) {
                prescription.setManuDate(null);
            }
            
            return prescription;
        });
    }

    /**
     * 示例7：单个对象清洗
     * 在某些场景下只需要清洗单个对象
     */
    public ViewYsfMzfycf cleanSinglePrescription(ViewYsfMzfycf prescription) {
        log.debug("清洗单个处方对象");
        return DataCleanUtil.cleanObject(prescription);
    }

    /**
     * 示例8：性能监控示例
     * 展示如何监控清洗性能
     */
    public List<ViewYsfMzfycf> cleanWithPerformanceMonitoring(List<ViewYsfMzfycf> prescriptionList) {
        long startTime = System.currentTimeMillis();
        
        // 执行清洗
        List<ViewYsfMzfycf> cleanedList = DataCleanUtil.cleanViewData(prescriptionList);
        
        long endTime = System.currentTimeMillis();
        long duration = endTime - startTime;
        
        // 记录性能指标
        log.info("数据清洗性能统计 - 记录数：{}，耗时：{}ms，平均每条：{}ms", 
                cleanedList.size(), duration, 
                cleanedList.size() > 0 ? (double) duration / cleanedList.size() : 0);
        
        // 输出缓存统计
        log.debug("缓存统计：{}", DataCleanUtil.getCacheStats());
        
        return cleanedList;
    }
}