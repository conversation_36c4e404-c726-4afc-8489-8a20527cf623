package com.zsm.his.impl;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.zsm.constant.NhsaAccountConstant;
import com.zsm.entity.ViewYsfZyfycf;
import com.zsm.entity.Nhsa3505;
import com.zsm.his.InpatientTraceabilityService;
import com.zsm.mapper.ViewYsfZyfycfMapper;
import com.zsm.model.ApiResult;
import com.zsm.model.domain.DateRange;
import com.zsm.model.enums.SdDpsEnum;
import com.zsm.model.nhsa.request.fsi5204.Selinfo5204;
import com.zsm.model.nhsa.response.Fsi5204Response;
import com.zsm.model.nhsa.response.NhsaCityResponse;
import com.zsm.model.saas.request.ConfirmDispDrugDataRequest;
import com.zsm.model.saas.request.ConfirmDispDrugRequest;
import com.zsm.model.saas.request.GetTracCodgStoreDataRequest;
import com.zsm.model.saas.request.QueryTracDrugRequest;
import com.zsm.model.vo.InPatientDispenseDetailBindScatteredVo;
import com.zsm.his.impl.FuYangZhongLiuService;
import com.zsm.model.dto.InpatientPrescriptionQueryDto;
import com.zsm.model.vo.InpatientPrescriptionResponseVo;
import com.zsm.model.vo.InpatientSettlementVo;
import com.zsm.service.Nhsa3505Service;
import com.zsm.service.TokenCacheService;
import com.zsm.service.ViewYsfZyfycfService;
import com.zsm.utils.DateRangeUtil;

import com.zsm.utils.NhsaHttpUtil;
import com.zsm.utils.NhsaRetryUtil;
import com.zsm.utils.SaasHttpUtil;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 阜阳肿瘤住院发药追溯码实现层
 *
 * 需求：从 VIEW_YSF_ZYFYCF 视图获取当日（或传入范围内）有发药数据的医保就诊患者，
 * 提取 mdtrt_sn 与 psn_no（若可用）调用 5204，完成追溯码赋码与上传。
 *
 * 按照 HangChuangService.processWeeklyInpatientTraceability 的业务流程实现。
 *
 * <AUTHOR>
 * @date 2025/8/19 下午9:49
 */
@Slf4j
@Service
public class InpatientTraceabilityServiceImpl implements InpatientTraceabilityService {

	@Resource
	private ViewYsfZyfycfService viewYsfZyfycfService;
	@Resource
	private ViewYsfZyfycfMapper viewYsfZyfycfMapper;
	@Resource
	private Nhsa3505Service nhsa3505Service;
	@Resource
	private TokenCacheService tokenCacheService;
	@Resource
	private NhsaRetryUtil nhsaRetryUtil;
	@Resource
	private FuYangZhongLiuService fuYangZhongLiuService;

	@Override
	public ApiResult<String> processWeeklyInpatientTraceability(String startDate, String endDate) {
		// 计算当前统计周范围（上周日-本周六），仅用于日志与过滤5204明细
		DateRange weeklyRange = DateRangeUtil.calculateCurrentWeekRange();
		log.info("开始处理住院患者药品追溯码补录（视图拉取患者维度），统计周期：{} 至 {}，请求范围：{} 至 {}",
				weeklyRange.getStartDate(), weeklyRange.getEndDate(), startDate, endDate);

		try {
			// Step 1: 从视图查询指定日期范围内有发药记录的医保住院患者列表，并去重
			List<InpatientSettlementVo> inpatients = queryInpatientsFromView(DateUtil.today(), DateUtil.today());
			if (inpatients.isEmpty()) {
				log.info("指定时间范围内视图无符合条件的住院患者");
				return ApiResult.success("指定时间范围内无住院患者");
			}

			// Step 2: 批量处理住院患者的追溯码补录与上传
			return batchProcessInpatientTraceability(inpatients, weeklyRange);

		} catch (Exception e) {
			log.error("处理住院患者药品追溯码补录异常", e);
			return ApiResult.error("处理失败: " + e.getMessage());
		}
	}

	/**
	 * 从 VIEW_YSF_ZYFYCF 视图查询指定时间范围内有发药记录的医保就诊患者并去重
	 * - 仅保留 mdtrt_setl_type = '1'（医保就诊）
	 * - 时间范围基于 sel_retn_time 字段
	 * - 去重维度：pat_in_hos_id（住院号）
	 */
    @DS("his")
	private List<InpatientSettlementVo> queryInpatientsFromView(String startDate, String endDate) {
		try {
			log.info("从视图查询住院患者（发药记录）列表，时间范围：{} - {}", startDate, endDate);

			// 构建时间参数
			String startDateTime = StrUtil.isNotBlank(startDate) ? startDate + " 00:00:00" : null;
			String endDateTime = StrUtil.isNotBlank(endDate) ? endDate + " 23:59:59" : null;

			// 直接使用SQL在数据库层面进行去重查询，提升性能
			List<InpatientSettlementVo> result = viewYsfZyfycfMapper.selectDistinctInpatients(startDateTime, endDateTime);
			
			if (result == null) {
				result = new ArrayList<>();
			}

			log.info("视图查询完成，去重后患者数：{}", result.size());
			return result;
		} catch (Exception e) {
			log.error("从视图查询住院患者列表异常", e);
			return new ArrayList<>();
		}
	}



	/**
	 * 批量处理住院患者的追溯码补录
	 */
	private ApiResult<String> batchProcessInpatientTraceability(List<InpatientSettlementVo> inpatients, DateRange weeklyRange) {
		int successPatients = 0;
		int failedPatients = 0;
		StringBuilder errorMessages = new StringBuilder();
		int totalPatients = inpatients.size();
		log.info("开始批量处理住院患者的追溯码补录，共 {} 位患者", totalPatients);

		for (InpatientSettlementVo patient : inpatients) {
			try {
				log.info("开始处理住院患者：{} (住院号：{})", patient.getPatientName(), patient.getPatInHosId());
				boolean result = processInpatientWithWeeklyRange(patient, weeklyRange);
				if (result) {
					successPatients++;
					log.info("患者 {} 处理成功", patient.getPatientName());
				} else {
					failedPatients++;
					log.warn("患者 {} 处理失败", patient.getPatientName());
					errorMessages.append("患者").append(patient.getPatientName()).append("处理失败; ");
				}
			} catch (Exception e) {
				failedPatients++;
				log.error("处理患者 {} 时发生异常", patient.getPatientName(), e);
				errorMessages.append("患者").append(patient.getPatientName()).append("异常: ").append(e.getMessage()).append("; ");
			}
			int processedCount = successPatients + failedPatients;
			int remainingCount = totalPatients - processedCount;
			log.info("进度更新: 已处理 {}/{} 位患者，剩余 {} 位患者 (成功: {}，失败: {})",
					processedCount, totalPatients, remainingCount, successPatients, failedPatients);
		}

		String resultMessage = String.format("处理完成，成功: %d, 失败: %d", successPatients, failedPatients);
		if (errorMessages.length() > 0) {
			resultMessage += "，错误详情: " + errorMessages;
		}
		return ApiResult.success(resultMessage);
	}

	/**
	 * 处理单个住院患者的追溯码补录（使用周时间范围）
	 */
	private boolean processInpatientWithWeeklyRange(InpatientSettlementVo patient, DateRange weeklyRange) {
		String patientName = patient.getPatientName();
		try {
			// 5204费用明细（按周范围过滤）
			List<Fsi5204Response> feeDetails = queryFeeDetails(patient);
			log.info("患者 {} 查询到5204费用明细 {} 条", patientName, feeDetails.size());
			feeDetails = feeDetails.stream()
					.filter(item -> isWithinDateRange(item.getFeeOcurTime(), weeklyRange.getStartDate(), weeklyRange.getEndDate()))
					.collect(Collectors.toList());
			log.info("患者 {} 在当前统计周期内过滤后的5204费用明细 {} 条", patientName, feeDetails.size());
			if (feeDetails.isEmpty()) {
				log.info("患者 {} 无5204费用明细,跳过处理", patientName);
				return true;
			}

			// 住院发药明细（按住院号查询）改为调用业务服务住院处方接口
			InpatientPrescriptionQueryDto queryDto = new InpatientPrescriptionQueryDto();
			queryDto.setPatientId(patient.getPatInHosId());
			ApiResult<java.util.List<InpatientPrescriptionResponseVo.InpatientPrescriptionItem>> queryResult =
					fuYangZhongLiuService.queryInpatientPrescription(queryDto);
			java.util.List<InpatientPrescriptionResponseVo.InpatientPrescriptionItem> patientDispenseDetails =
					(queryResult != null && queryResult.getData() != null) ? queryResult.getData() : new java.util.ArrayList<>();
			log.info("患者 {} 查询到住院发药明细 {} 条", patientName, patientDispenseDetails.size());
			if (patientDispenseDetails.isEmpty()) {
				log.info("患者 {} 无住院发药明细,跳过处理", patientName);
				return true;
			}

			patientDispenseDetails = filterInpatientDispensingDataByInpatientItems(feeDetails, patientDispenseDetails);

			// 明细增量保存至 nhsa_3505（住院处方项异步保存）
			try {
				nhsa3505Service.save3505ZyAsync(patientDispenseDetails);
				log.info("患者 {} 同步住院发药明细到3505表，数据量：{} 条", patientName, patientDispenseDetails.size());
			} catch (Exception e) {
				log.error("患者 {} 保存住院发药明细到3505表时发生异常", patientName, e);
			}

			// 查询当前患者在3505中的数据
			List<Nhsa3505> existing3505Data = queryExisting3505Data(patient);
			log.info("患者 {} 已存在3505数据 {} 条", patientName, existing3505Data.size());

			// 识别未同步且在5204中的记录
			List<Nhsa3505> unSyncedData = identifyUnSyncedDataWithFeeDetails(feeDetails, existing3505Data);
			if (unSyncedData.isEmpty()) {
				log.info("患者 {} 无需要处理的未同步数据", patientName);
				return true;
			}

			// 追溯码获取、赋值与确认
			boolean traceCodeResult = processTraceCodeAssignmentByInpatientItems(unSyncedData, patientDispenseDetails, patientName);
			if (!traceCodeResult) {
				log.error("患者 {} 处理追溯码赋值失败", patientName);
				return false;
			}

			// 上传
			return processUnSyncedDataUpload(unSyncedData, patient);
		} catch (Exception e) {
			log.error("处理患者 {} 时发生异常", patientName, e);
			return false;
		}
	}

	/**
	 * 查询5204费用明细（支持仅用 mdtrt_sn 查询）
	 */
	private List<Fsi5204Response> queryFeeDetails(InpatientSettlementVo patient) {
		try {
			log.info("开始查询患者 {} 的5204费用明细", patient.getPatientName());
			Selinfo5204 selinfo5204 = new Selinfo5204();
			if (StrUtil.isNotBlank(patient.getPsnNo()) && StrUtil.isNotBlank(patient.getMdtrtSn())) {
				selinfo5204.setPsn_no(patient.getPsnNo());
				selinfo5204.setMdtrt_id(patient.getMdtrtSn());
			}else{
				return new ArrayList<>();
			}
			NhsaCityResponse<List<Fsi5204Response>> response = nhsaRetryUtil.executeWithRetry(NhsaAccountConstant.getNhsaAccount(),
					currentSignNo -> NhsaHttpUtil.fsi5204(currentSignNo, selinfo5204, NhsaAccountConstant.getNhsaAccount()));
			return response.getBody().getOutput().stream()
					.filter(item -> "09".equals(item.getMedChrgitmType()))
					.collect(Collectors.toList());
		} catch (Exception e) {
			log.error("查询患者 {} 的5204费用明细异常", patient.getPatientName(), e);
			return new ArrayList<>();
		}
	}

	/**
	 * 通过5204.rx_drord_no 与 住院发药数据（id_fee）进行关联比对，筛选需要处理的数据
	 */
	private List<InPatientDispenseDetailBindScatteredVo> filterInpatientDispensingData(List<Fsi5204Response> feeDetails,
																 List<InPatientDispenseDetailBindScatteredVo> existing3505Data) {
		log.info("开始识别需要赋码上传的未同步数据，5204明细{}条，住院处方明细数据{}条",
				feeDetails.size(), existing3505Data.size());
		Set<String> feeDetailRxNos = feeDetails.stream()
				.map(Fsi5204Response::getRxDrordNo)
				.filter(Objects::nonNull)
				.collect(Collectors.toSet());
		List<InPatientDispenseDetailBindScatteredVo> unSyncedData = existing3505Data.stream()
				.filter(data -> feeDetailRxNos.contains(data.getIdFee()))
				.collect(Collectors.toList());
		log.info("识别到需要处理的3505未同步数据{}条", unSyncedData.size());
		return unSyncedData;
	}

	/**
	 * 通过5204.rx_drord_no 与 住院处方数据（cfmxxh）进行关联比对，筛选需要处理的数据
	 */
	private java.util.List<InpatientPrescriptionResponseVo.InpatientPrescriptionItem> filterInpatientDispensingDataByInpatientItems(
			java.util.List<Fsi5204Response> feeDetails,
			java.util.List<InpatientPrescriptionResponseVo.InpatientPrescriptionItem> inpatientItems) {
		log.info("开始识别需要赋码上传的未同步数据，5204明细{}条，住院处方明细数据{}条",
				feeDetails.size(), inpatientItems.size());
		java.util.Set<String> feeDetailRxNos = feeDetails.stream()
				.map(Fsi5204Response::getRxDrordNo)
				.filter(java.util.Objects::nonNull)
				.collect(java.util.stream.Collectors.toSet());
		java.util.List<InpatientPrescriptionResponseVo.InpatientPrescriptionItem> filtered = inpatientItems.stream()
				.filter(item -> feeDetailRxNos.contains(item.getCfmxxh()))
				.collect(java.util.stream.Collectors.toList());
		log.info("识别到需要处理的住院处方数据{}条", filtered.size());
		return filtered;
	}

	/**
	 * 从VIEW_YSF_ZYFYCF视图查询住院发药明细（按住院号）
	 */
	@DS("his")
	private List<InPatientDispenseDetailBindScatteredVo> queryPatientDispenseDetailsFromView(InpatientSettlementVo patient) {
		try {
			log.info("开始从VIEW_YSF_ZYFYCF视图查询患者 {} 的住院发药明细", patient.getPatientName());
			
			// 构建查询条件：按住院号查询，只查医保患者
			LambdaQueryWrapper<ViewYsfZyfycf> queryWrapper = new LambdaQueryWrapper<>();
			queryWrapper.eq(ViewYsfZyfycf::getPatientId, patient.getPatInHosId())
					.eq(ViewYsfZyfycf::getMdtrtSetlType, "1")  // 医保就诊
					.isNotNull(ViewYsfZyfycf::getRxno)
					.ne(ViewYsfZyfycf::getRxno, "")
					.orderBy(true, true, ViewYsfZyfycf::getSelRetnTime);
			
			// 查询视图数据
			List<ViewYsfZyfycf> viewDataList = viewYsfZyfycfService.list(queryWrapper);
			
			// 转换为InPatientDispenseDetailBindScatteredVo
			List<InPatientDispenseDetailBindScatteredVo> resultList = viewDataList.stream()
					.map(this::convertViewToDispenseDetail)
					.filter(Objects::nonNull)
					.collect(Collectors.toList());
			
			log.info("从视图查询患者 {} 住院发药明细成功，返回{}条数据", patient.getPatientName(), resultList.size());
			return resultList;
		} catch (Exception e) {
			log.error("查询患者 {} 发药明细视图异常", patient.getPatientName(), e);
			return new ArrayList<>();
		}
	}

	/**
	 * 将VIEW_YSF_ZYFYCF视图数据转换为InPatientDispenseDetailBindScatteredVo
	 */
	private InPatientDispenseDetailBindScatteredVo convertViewToDispenseDetail(ViewYsfZyfycf viewData) {
		try {
			InPatientDispenseDetailBindScatteredVo detail = new InPatientDispenseDetailBindScatteredVo();
			
			// 基本患者信息
			detail.setPsnName(viewData.getPsnName());
			detail.setPsnNo(viewData.getPsnNo());
			detail.setMdtrtSn(viewData.getMdtrtSn());
			detail.setPatInHosId(viewData.getPatientId());
			detail.setMdtrtSetlType(viewData.getMdtrtSetlType());
			
			// 药品信息
			detail.setMedListCodg(viewData.getMedListCodg());
			detail.setFixmedinsHilistId(String.valueOf(viewData.getFixmedinsHilistId()));
			detail.setFixmedinsHilistName(viewData.getFixmedinsHilistName());
			detail.setFixmedinsBchno(viewData.getFixmedinsBchno());
			detail.setSpec(viewData.getSpec());
			detail.setProdentpName(viewData.getProdentpName());
			
			// 医师和药师信息
			detail.setPrscDrCertno(viewData.getPrscDrCertno());
			detail.setPrscDrName(viewData.getPrscDrName());
			detail.setPharCertno(viewData.getPharCertno());
			detail.setPharName(viewData.getPharName());
			detail.setPharPracCertNo(viewData.getPharPracCertNo());
			
			// 生产信息
			detail.setManuLotnum(viewData.getManuLotnum());
			detail.setManuDate(viewData.getManuDate());
			detail.setExpyEnd(viewData.getExpyEnd());
			
			// 处方和发药信息
			detail.setRxFlag(viewData.getRxFlag());
			detail.setTrdnFlag(viewData.getTrdnFlag());
			detail.setRtalDocno(viewData.getRtalDocno());
			detail.setStooutNo(viewData.getStooutNo());
			detail.setBchno(viewData.getBchno());
			
			// 销售数量和时间
			if (viewData.getSelRetnCnt() != null) {
				detail.setSelRetnCnt(viewData.getSelRetnCnt().intValue());
			}
			detail.setSelRetnTime(viewData.getSelRetnTime());
			
			// 处方序号信息
			if (viewData.getCfxh() != null) {
				detail.setCfxh(String.valueOf(viewData.getCfxh()));
			}
			if (viewData.getCfmxxh() != null) {
				detail.setCfmxxh(String.valueOf(viewData.getCfmxxh()));
			}
			
			// 发药药房
			detail.setFyyf(viewData.getFyyf());
			
			// HIS转换比
			detail.setHisConRatio(viewData.getHisConRatio());
			
			// 其他字段设置默认值或映射
			if (viewData.getRecordId() != null) {
				detail.setRecordId(String.valueOf(viewData.getRecordId()));
			}
			
			// 费用ID使用fixmedinsBchno作为标识
			detail.setIdFee(viewData.getFixmedinsBchno());
			
			// HIS药品编码使用fixmedinsHilistId
			detail.setHisDrugCode(String.valueOf(viewData.getFixmedinsHilistId()));
			
			return detail;
		} catch (Exception e) {
			log.error("转换VIEW_YSF_ZYFYCF数据异常: {}", e.getMessage(), e);
			return null;
		}
	}

	/**
	 * 查询已存在的3505业务数据（当前医疗机构 + 患者维度）
	 */
	private List<Nhsa3505> queryExisting3505Data(InpatientSettlementVo patient) {
		String patientName = patient.getPatientName();
		try {
			log.info("开始查询患者 {} 的已存在3505数据", patientName);
			LambdaQueryWrapper<Nhsa3505> queryWrapper = new LambdaQueryWrapper<>();
			queryWrapper.eq(Nhsa3505::getMedicalCode, NhsaAccountConstant.getNhsaAccount().getMedicalCode());
			if (StrUtil.isNotBlank(patient.getMdtrtSn())) {
				queryWrapper.eq(Nhsa3505::getMdtrtSn, patient.getMdtrtSn());
			} else {
				queryWrapper.eq(Nhsa3505::getPatientId, patient.getPatInHosId());
			}
			queryWrapper.eq(Nhsa3505::getSdDps, SdDpsEnum.INPATIENT);
			List<Nhsa3505> existingData = nhsa3505Service.list(queryWrapper);
			log.info("患者 {} 查询到已存在3505数据 {} 条", patientName, existingData.size());
			return existingData;
		} catch (Exception e) {
			log.error("查询患者 {} 已存在3505数据异常", patientName, e);
			return new ArrayList<>();
		}
	}

	/**
	 * 识别需要赋码上传的未同步数据：5204.rx_drord_no ↔ 3505.feedetl_sn
	 */
	private List<Nhsa3505> identifyUnSyncedDataWithFeeDetails(List<Fsi5204Response> feeDetails, List<Nhsa3505> existing3505Data) {
		log.info("开始识别需要赋码上传的未同步数据，5204明细{}条，3505数据{}条",
				feeDetails.size(), existing3505Data.size());
		Set<String> feeDetailRxNos = feeDetails.stream()
				.map(Fsi5204Response::getRxDrordNo)
				.filter(Objects::nonNull)
				.collect(Collectors.toSet());
		List<Nhsa3505> unSyncedData = existing3505Data.stream()
				.filter(data -> "0".equals(data.getHsaSyncStatus()))
				.filter(data -> feeDetailRxNos.contains(data.getFeedetlSn()))
				.collect(Collectors.toList());
		log.info("识别到需要处理的3505未同步数据{}条", unSyncedData.size());
		return unSyncedData;
	}

	/**
	 * 处理追溯码赋值和拆零确认
	 */
	private boolean processTraceCodeAssignment(List<Nhsa3505> unSyncedData,
											  List<InPatientDispenseDetailBindScatteredVo> patientDispenseDetails,
											  String patientName) {
		try {
			log.info("患者 {} 开始处理追溯码赋值和拆零确认，数据量：{}", patientName, unSyncedData.size());
			Set<String> unSyncedBchnoSet = unSyncedData.stream()
					.filter(data -> StrUtil.isBlank(data.getDrugTracInfo()))
					.map(Nhsa3505::getFixmedinsBchno)
					.filter(Objects::nonNull)
					.collect(Collectors.toSet());
			List<InPatientDispenseDetailBindScatteredVo> filteredDispenseDetails = patientDispenseDetails.stream()
					.filter(detail -> unSyncedBchnoSet.contains(detail.getFixmedinsBchno()))
					.collect(Collectors.toList());
			if (filteredDispenseDetails.isEmpty()) {
				log.info("患者 {} 无匹配的配药详情数据", patientName);
				return true;
			}
			Map<String, List<InPatientDispenseDetailBindScatteredVo>> fyyfGroupMap = filteredDispenseDetails.stream()
					.filter(detail -> StrUtil.isNotBlank(detail.getFyyf()))
					.collect(Collectors.groupingBy(InPatientDispenseDetailBindScatteredVo::getFyyf));
			if (fyyfGroupMap.isEmpty()) {
				log.warn("患者 {} 的配药详情中没有有效的fyyf字段", patientName);
				return false;
			}
			int totalAssignedCount = 0;
			for (Map.Entry<String, List<InPatientDispenseDetailBindScatteredVo>> entry : fyyfGroupMap.entrySet()) {
				String fyyf = entry.getKey();
				List<InPatientDispenseDetailBindScatteredVo> groupDetails = entry.getValue();
				String token = getTokenByFyyf(fyyf);
				if (StrUtil.isBlank(token)) {
					log.error("患者 {} 药房 {} 获取token失败，跳过该分组", patientName, fyyf);
					continue;
				}
				List<InPatientDispenseDetailBindScatteredVo> enrichedGroupDetails = handleTracCodgStoreForInpatient(groupDetails, token);
				int groupAssignedCount = assignTraceCodeToEmptyRecords(unSyncedData, enrichedGroupDetails, patientName);
				boolean confirmResult = confirmDispenseData(enrichedGroupDetails, token);
				if (!confirmResult) {
					log.error("患者 {} 药房 {} 确认拆零信息失败", patientName, fyyf);
				}
				totalAssignedCount += groupAssignedCount;
			}
			if (totalAssignedCount > 0) {
				nhsa3505Service.updateBatchById(unSyncedData);
				log.info("患者 {} 更新数据库记录完成，总共更新 {} 条记录", patientName, totalAssignedCount);
			}
			return true;
		} catch (Exception e) {
			log.error("患者 {} 处理追溯码赋值和拆零确认时发生异常", patientName, e);
			return false;
		}
	}

	/**
	 * 处理追溯码赋值和拆零确认（住院处方项）
	 */
	private boolean processTraceCodeAssignmentByInpatientItems(java.util.List<Nhsa3505> unSyncedData,
															  java.util.List<InpatientPrescriptionResponseVo.InpatientPrescriptionItem> patientDispenseDetails,
															  String patientName) {
		try {
			log.info("患者 {} 开始处理追溯码赋值和拆零确认（新VO），数据量：{}", patientName, unSyncedData.size());
			java.util.Set<String> unSyncedBchnoSet = unSyncedData.stream()
					.filter(data -> StrUtil.isBlank(data.getDrugTracInfo()))
					.map(Nhsa3505::getFixmedinsBchno)
					.filter(java.util.Objects::nonNull)
					.collect(java.util.stream.Collectors.toSet());
			java.util.List<InpatientPrescriptionResponseVo.InpatientPrescriptionItem> filteredDispenseDetails = patientDispenseDetails.stream()
					.filter(detail -> unSyncedBchnoSet.contains(detail.getFixmedins_bchno()))
					.collect(java.util.stream.Collectors.toList());
			if (filteredDispenseDetails.isEmpty()) {
				log.info("患者 {} 无匹配的配药详情数据（新VO）", patientName);
				return true;
			}
			java.util.Map<String, java.util.List<InpatientPrescriptionResponseVo.InpatientPrescriptionItem>> fyyfGroupMap = filteredDispenseDetails.stream()
					.filter(detail -> StrUtil.isNotBlank(detail.getFyyf()))
					.collect(java.util.stream.Collectors.groupingBy(InpatientPrescriptionResponseVo.InpatientPrescriptionItem::getFyyf));
			if (fyyfGroupMap.isEmpty()) {
				log.warn("患者 {} 的配药详情中没有有效的fyyf字段（新VO）", patientName);
				return false;
			}
			int totalAssignedCount = 0;
			for (java.util.Map.Entry<String, java.util.List<InpatientPrescriptionResponseVo.InpatientPrescriptionItem>> entry : fyyfGroupMap.entrySet()) {
				String fyyf = entry.getKey();
				java.util.List<InpatientPrescriptionResponseVo.InpatientPrescriptionItem> groupDetails = entry.getValue();
				String token = getTokenByFyyf(fyyf);
				if (StrUtil.isBlank(token)) {
					log.error("患者 {} 药房 {} 获取token失败，跳过该分组（新VO）", patientName, fyyf);
					continue;
				}
				java.util.List<InpatientPrescriptionResponseVo.InpatientPrescriptionItem> enrichedGroupDetails = handleTracCodgStoreForInpatientItems(groupDetails, token);
				int groupAssignedCount = assignTraceCodeToEmptyRecordsByInpatientItems(unSyncedData, enrichedGroupDetails, patientName);
				boolean confirmResult = confirmDispenseDataByInpatientItems(enrichedGroupDetails, token);
				if (!confirmResult) {
					log.error("患者 {} 药房 {} 确认拆零信息失败（新VO）", patientName, fyyf);
				}
				totalAssignedCount += groupAssignedCount;
			}
			if (totalAssignedCount > 0) {
				nhsa3505Service.updateBatchById(unSyncedData);
				log.info("患者 {} 更新数据库记录完成（新VO），总共更新 {} 条记录", patientName, totalAssignedCount);
			}
			return true;
		} catch (Exception e) {
			log.error("患者 {} 处理追溯码赋值和拆零确认（新VO）时发生异常", patientName, e);
			return false;
		}
	}

	/**
	 * 查询、补全拆零追溯码库存信息（住院处方项）
	 */
	private java.util.List<InpatientPrescriptionResponseVo.InpatientPrescriptionItem> handleTracCodgStoreForInpatientItems(
			java.util.List<InpatientPrescriptionResponseVo.InpatientPrescriptionItem> extendedList, String token) {
		try {
			java.util.List<QueryTracDrugRequest> queryTracDrugRequestList = extendedList.stream()
					.filter(item -> org.apache.commons.lang3.StringUtils.isNotEmpty(item.getCfmxxh()) && org.apache.commons.lang3.StringUtils.isNotEmpty(item.getMin_sel_retn_cnt()))
					.map(item -> QueryTracDrugRequest.builder()
							.cfxh(item.getCfxh())
							.cfmxxh(item.getCfmxxh())
							.drugCode(item.getFixmedins_hilist_id())
							.dispCnt(tryParseInt(item.getMin_sel_retn_cnt()))
							.build())
					.collect(java.util.stream.Collectors.toList());
			if (queryTracDrugRequestList.isEmpty()) {
				return extendedList;
			}
			java.util.Map<String, com.zsm.model.saas.response.QueryTracDrugResponse> queryTracDrugMap = SaasHttpUtil.queryTracDrugYzMap(token, queryTracDrugRequestList);
			java.util.List<GetTracCodgStoreDataRequest> tracDataList = new java.util.ArrayList<>();
			for (InpatientPrescriptionResponseVo.InpatientPrescriptionItem item : extendedList) {
				if (queryTracDrugMap.containsKey(item.getCfmxxh())) {
					com.zsm.model.saas.response.QueryTracDrugResponse queryTracDrug = queryTracDrugMap.get(item.getCfmxxh());
					item.setTrdn_flag(Integer.valueOf(queryTracDrug.getIsTrac()));
					item.setHis_con_ratio(String.valueOf(queryTracDrug.getConRatio()));
					if ("1".equals(queryTracDrug.getIsTrac())) {
						GetTracCodgStoreDataRequest build = GetTracCodgStoreDataRequest.builder()
								.cfxh(item.getCfxh())
								.cfmxxh(item.getCfmxxh())
								.dispCnt(tryParseInt(item.getMin_sel_retn_cnt()))
								.drugCode(item.getFixmedins_hilist_id())
								.build();
						tracDataList.add(build);
					}
				}
			}
			java.util.Map<String, com.zsm.model.saas.response.GetTracCodgStoreDataResponse> tracCodgStoreMap = new java.util.HashMap<>();
			if (!tracDataList.isEmpty()) {
				java.util.List<com.zsm.model.saas.response.GetTracCodgStoreDataResponse> tracCodgStore = SaasHttpUtil.getTracCodgStore(token, com.zsm.model.saas.request.GetTracCodgStoreRequest.builder().dataList(tracDataList).build());
				tracCodgStoreMap = tracCodgStore.stream().collect(java.util.stream.Collectors.toMap(com.zsm.model.saas.response.GetTracCodgStoreDataResponse::getCfmxxh, java.util.function.Function.identity(), (o, n) -> n));
			}
			for (InpatientPrescriptionResponseVo.InpatientPrescriptionItem item : extendedList) {
				if (item.getTrdn_flag() != null && item.getTrdn_flag() == 1) {
					if (tracCodgStoreMap.containsKey(item.getCfmxxh())) {
						com.zsm.model.saas.response.GetTracCodgStoreDataResponse tracCodgStoreData = tracCodgStoreMap.get(item.getCfmxxh());
						item.setDrugCode(tracCodgStoreData.getDrugCode());
						item.setDrugTracCodgs(tracCodgStoreData.getDrugTracCodgs());
						item.setDispCnt(tracCodgStoreData.getDispCnt());
						item.setCurrNum(tracCodgStoreData.getCurrNum());
						item.setTracCodgStore(tracCodgStoreData);
					}
				}
			}
			return extendedList;
		} catch (Exception e) {
			log.error("处理住院药品追溯码库存信息异常（新VO）", e);
			return extendedList;
		}
	}

	/**
	 * 对drugProdBarc为空的记录赋值追溯码（住院处方项）
	 */
	private int assignTraceCodeToEmptyRecordsByInpatientItems(java.util.List<Nhsa3505> unSyncedData,
															  java.util.List<InpatientPrescriptionResponseVo.InpatientPrescriptionItem> enrichedDispenseDetails,
															  String patientName) {
		int assignedCount = 0;
		java.util.Map<String, InpatientPrescriptionResponseVo.InpatientPrescriptionItem> enrichedMap = enrichedDispenseDetails.stream()
				.filter(detail -> org.apache.commons.lang3.StringUtils.isNotEmpty(detail.getFixmedins_bchno()))
				.collect(java.util.stream.Collectors.toMap(InpatientPrescriptionResponseVo.InpatientPrescriptionItem::getFixmedins_bchno, java.util.function.Function.identity(), (existing, replacement) -> replacement));
		for (Nhsa3505 nhsa3505 : unSyncedData) {
			try {
				String fixmedinsBchno = nhsa3505.getFixmedinsBchno();
				if (org.apache.commons.lang3.StringUtils.isNotEmpty(fixmedinsBchno) && enrichedMap.containsKey(fixmedinsBchno)) {
					InpatientPrescriptionResponseVo.InpatientPrescriptionItem enrichedDetail = enrichedMap.get(fixmedinsBchno);
					if (enrichedDetail.getDrugTracCodgs() != null && !enrichedDetail.getDrugTracCodgs().isEmpty()) {
						nhsa3505.setDrugTracInfo(String.join(",", enrichedDetail.getDrugTracCodgs()));
						nhsa3505.setTrdnFlag(enrichedDetail.getTrdn_flag() != null ? String.valueOf(enrichedDetail.getTrdn_flag()) : null);
						nhsa3505.setInvCnt(enrichedDetail.getCurrNum());
						nhsa3505.setRemark("当日有明细自动赋码上传");
						assignedCount++;
						log.debug("患者 {} 为记录 {} (fixmedinsBchno: {}) 赋值追溯码（新VO）", patientName, nhsa3505.getId(), fixmedinsBchno);
					} else {
						nhsa3505.setInvCnt(java.math.BigDecimal.ZERO);
						nhsa3505.setRemark("没有赋码");
					}
				}
			} catch (Exception e) {
				log.error("患者 {} 为记录 {} 赋值追溯码（新VO）时发生异常", patientName, nhsa3505.getId(), e);
			}
		}
		log.info("患者 {} 追溯码赋值完成（新VO），共处理 {} 条记录，实际赋值 {} 条", patientName, unSyncedData.size(), assignedCount);
		return assignedCount;
	}

	/**
	 * 确认拆零信息（SaaS）（住院处方项）
	 */
	private boolean confirmDispenseDataByInpatientItems(java.util.List<InpatientPrescriptionResponseVo.InpatientPrescriptionItem> processedDataList, String token) {
		try {
			java.util.List<InpatientPrescriptionResponseVo.InpatientPrescriptionItem> confirmList = processedDataList.stream()
					.filter(item -> item.getTrdn_flag() != null && item.getTrdn_flag() == 1)
					.collect(java.util.stream.Collectors.toList());
			if (confirmList.isEmpty()) {
				return true;
			}
			java.util.List<ConfirmDispDrugDataRequest> requestDataList = new java.util.ArrayList<>();
			for (InpatientPrescriptionResponseVo.InpatientPrescriptionItem record : confirmList) {
				ConfirmDispDrugDataRequest request = new ConfirmDispDrugDataRequest();
				request.setDrugCode(record.getHis_drug_code());
				request.setDispCnt(tryParseInt(record.getMin_sel_retn_cnt()));
				request.setCfxh(record.getCfxh());
				request.setCfmxxh(record.getCfmxxh());
				requestDataList.add(request);
			}
			if (!requestDataList.isEmpty()) {
				ConfirmDispDrugRequest confirmRequest = new ConfirmDispDrugRequest();
				confirmRequest.setDataList(requestDataList);
				SaasHttpUtil.confirmDispDrug(token, confirmRequest);
			}
			return true;
		} catch (Exception e) {
			log.error("确认拆零信息（新VO）时发生异常", e);
			return false;
		}
	}

	private Integer tryParseInt(String value) {
		try {
			return org.apache.commons.lang3.StringUtils.isNotEmpty(value) ? Integer.valueOf(value) : 0;
		} catch (Exception e) {
			return 0;
		}
	}

	/**
	 * 根据发药药房ID(fyyf)获取对应账号的访问token（从缓存获取，失败则重新获取）
	 */
	private String getTokenByFyyf(String fyyf) {
		try {
			log.info("开始根据发药药房ID获取token，fyyf: {}", fyyf);
			com.zsm.constant.SyncAccountEnum accountEnum = com.zsm.constant.SyncAccountEnum.findByFyyf(fyyf);
			if (accountEnum == null) {
				log.error("未找到发药药房ID对应的账号配置，fyyf: {}", fyyf);
				return null;
			}
			com.zsm.model.saas.response.AccessTokenReponse cachedToken = tokenCacheService.getTokenFromCache(accountEnum.getUsername());
			if (cachedToken != null && cachedToken.getReturnCode() == 0 && StrUtil.isNotBlank(cachedToken.getAuthorization())) {
				log.info("从缓存中获取到有效token，账号: {}", accountEnum.getUsername());
				return cachedToken.getAuthorization();
			}
			com.zsm.model.saas.response.AccessTokenReponse newToken = SaasHttpUtil.getAccessToken(accountEnum.getUsername(), accountEnum.getPassword());
			if (newToken == null || newToken.getReturnCode() != 0 || StrUtil.isBlank(newToken.getAuthorization())) {
				log.error("获取token失败，账号: {}，响应为空或无效", accountEnum.getUsername());
				return null;
			}
			tokenCacheService.cacheToken(accountEnum.getUsername(), newToken);
			return newToken.getAuthorization();
		} catch (Exception e) {
			log.error("根据发药药房ID获取token异常，fyyf: {}", fyyf, e);
			return null;
		}
	}

	/**
	 * 查询、补全拆零追溯码库存信息
	 */
	private List<InPatientDispenseDetailBindScatteredVo> handleTracCodgStoreForInpatient(List<InPatientDispenseDetailBindScatteredVo> extendedList, String token) {
		try {
			List<QueryTracDrugRequest> queryTracDrugRequestList = extendedList.stream()
					.filter(item -> StringUtils.isNotEmpty(item.getCfmxxh()) && item.getSelRetnCnt() != null)
					.map(item -> QueryTracDrugRequest.builder()
							.cfxh(item.getCfxh())
							.cfmxxh(item.getCfmxxh())
							.drugCode(item.getFixmedinsHilistId())
							.dispCnt(item.getSelRetnCnt())
							.build())
					.collect(Collectors.toList());
			if (queryTracDrugRequestList.isEmpty()) {
				return extendedList;
			}
			Map<String, com.zsm.model.saas.response.QueryTracDrugResponse> queryTracDrugMap = SaasHttpUtil.queryTracDrugYzMap(token, queryTracDrugRequestList);
			List<GetTracCodgStoreDataRequest> tracDataList = new ArrayList<>();
			for (InPatientDispenseDetailBindScatteredVo item : extendedList) {
				if (queryTracDrugMap.containsKey(item.getCfmxxh())) {
					com.zsm.model.saas.response.QueryTracDrugResponse queryTracDrug = queryTracDrugMap.get(item.getCfmxxh());
					item.setTrdnFlag(queryTracDrug.getIsTrac());
					item.setHisConRatio(String.valueOf(queryTracDrug.getConRatio()));
					if ("1".equals(queryTracDrug.getIsTrac())) {
						GetTracCodgStoreDataRequest build = GetTracCodgStoreDataRequest.builder()
								.cfxh(item.getCfxh())
								.cfmxxh(item.getCfmxxh())
								.dispCnt(item.getSelRetnCnt() != null ? item.getSelRetnCnt() : 0)
								.drugCode(item.getFixmedinsHilistId())
								.build();
						tracDataList.add(build);
					}
				}
			}
			Map<String, com.zsm.model.saas.response.GetTracCodgStoreDataResponse> tracCodgStoreMap = new HashMap<>();
			if (!tracDataList.isEmpty()) {
				List<com.zsm.model.saas.response.GetTracCodgStoreDataResponse> tracCodgStore = SaasHttpUtil.getTracCodgStore(token, com.zsm.model.saas.request.GetTracCodgStoreRequest.builder().dataList(tracDataList).build());
				tracCodgStoreMap = tracCodgStore.stream().collect(Collectors.toMap(com.zsm.model.saas.response.GetTracCodgStoreDataResponse::getCfmxxh, Function.identity(), (o, n) -> n));
			}
			for (InPatientDispenseDetailBindScatteredVo item : extendedList) {
				if ("1".equals(item.getTrdnFlag()) && tracCodgStoreMap.containsKey(item.getCfmxxh())) {
					com.zsm.model.saas.response.GetTracCodgStoreDataResponse tracCodgStoreData = tracCodgStoreMap.get(item.getCfmxxh());
					item.setDrugCode(tracCodgStoreData.getDrugCode());
					item.setDrugTracCodgs(tracCodgStoreData.getDrugTracCodgs());
					item.setDispCnt(tracCodgStoreData.getDispCnt());
					item.setCurrNum(tracCodgStoreData.getCurrNum());
					item.setTracCodgStore(tracCodgStoreData);
				}
			}
			return extendedList;
		} catch (Exception e) {
			log.error("处理住院药品追溯码库存信息异常", e);
			return extendedList;
		}
	}

	/**
	 * 对drugProdBarc为空的记录赋值追溯码
	 */
	private int assignTraceCodeToEmptyRecords(List<Nhsa3505> unSyncedData,
											  List<InPatientDispenseDetailBindScatteredVo> enrichedDispenseDetails,
											  String patientName) {
		int assignedCount = 0;
		Map<String, InPatientDispenseDetailBindScatteredVo> enrichedMap = enrichedDispenseDetails.stream()
				.filter(detail -> StringUtils.isNotEmpty(detail.getFixmedinsBchno()))
				.collect(Collectors.toMap(InPatientDispenseDetailBindScatteredVo::getFixmedinsBchno, Function.identity(), (existing, replacement) -> replacement));
		for (Nhsa3505 nhsa3505 : unSyncedData) {
			try {
				String fixmedinsBchno = nhsa3505.getFixmedinsBchno();
				if (StringUtils.isNotEmpty(fixmedinsBchno) && enrichedMap.containsKey(fixmedinsBchno)) {
					InPatientDispenseDetailBindScatteredVo enrichedDetail = enrichedMap.get(fixmedinsBchno);
					if (enrichedDetail.getDrugTracCodgs() != null && !enrichedDetail.getDrugTracCodgs().isEmpty()) {
						nhsa3505.setDrugTracInfo(String.join(",", enrichedDetail.getDrugTracCodgs()));
						nhsa3505.setTrdnFlag(enrichedDetail.getTrdnFlag());
						nhsa3505.setInvCnt(enrichedDetail.getCurrNum());
						nhsa3505.setRemark("当日有明细自动赋码上传");
						assignedCount++;
						log.debug("患者 {} 为记录 {} (fixmedinsBchno: {}) 赋值追溯码", patientName, nhsa3505.getId(), fixmedinsBchno);
					} else {
						nhsa3505.setInvCnt(BigDecimal.ZERO);
						nhsa3505.setRemark("没有赋码");
					}
				}
			} catch (Exception e) {
				log.error("患者 {} 为记录 {} 赋值追溯码时发生异常", patientName, nhsa3505.getId(), e);
			}
		}
		log.info("患者 {} 追溯码赋值完成，共处理 {} 条记录，实际赋值 {} 条", patientName, unSyncedData.size(), assignedCount);
		return assignedCount;
	}

	/**
	 * 确认拆零信息（SaaS）
	 */
	private boolean confirmDispenseData(List<InPatientDispenseDetailBindScatteredVo> processedDataList, String token) {
		try {
			List<InPatientDispenseDetailBindScatteredVo> confirmList = processedDataList.stream()
					.filter(item -> "1".equals(item.getTrdnFlag()))
					.collect(Collectors.toList());
			if (confirmList.isEmpty()) {
				return true;
			}
			List<ConfirmDispDrugDataRequest> requestDataList = new ArrayList<>();
			for (InPatientDispenseDetailBindScatteredVo record : confirmList) {
				ConfirmDispDrugDataRequest request = new ConfirmDispDrugDataRequest();
				request.setDrugCode(record.getHisDrugCode());
				request.setDispCnt(record.getSelRetnCnt());
				request.setCfxh(record.getCfxh());
				request.setCfmxxh(record.getCfmxxh());
				requestDataList.add(request);
			}
			if (!requestDataList.isEmpty()) {
				ConfirmDispDrugRequest confirmRequest = new ConfirmDispDrugRequest();
				confirmRequest.setDataList(requestDataList);
				SaasHttpUtil.confirmDispDrug(token, confirmRequest);
			}
			return true;
		} catch (Exception e) {
			log.error("确认拆零信息时发生异常", e);
			return false;
		}
	}

	/**
	 * 上传未同步数据
	 */
	private boolean processUnSyncedDataUpload(List<Nhsa3505> unSyncedData, InpatientSettlementVo patient) {
		String patientName = patient.getPatientName();
		try {
			final List<Nhsa3505> collect = unSyncedData.stream()
					.filter(item -> StrUtil.isNotBlank(item.getDrugTracInfo()))
					.collect(Collectors.toList());
			log.info("开始处理患者 {} 的未同步数据上传，数量：{}", patientName, unSyncedData.size());
			ApiResult<String> result = nhsa3505Service.processUpload(collect, false);
			if (result.getCode() != 200) {
				log.error("患者 {} 上传3505数据失败：{}", patientName, result.getMsg());
				return false;
			}
			log.info("患者 {} 的3505数据上传完成", patientName);
			return true;
		} catch (Exception e) {
			log.error("上传患者 {} 的3505数据时发生异常", patientName, e);
			return false;
		}
	}

	/**
	 * 判断5204费用发生时间是否在指定范围内
	 */
	private boolean isWithinDateRange(LocalDateTime feeTime, String startDate, String endDate) {
		if (feeTime == null) return false;
		LocalDate feeDate = feeTime.toLocalDate();
		LocalDate start = LocalDate.parse(startDate);
		LocalDate end = LocalDate.parse(endDate);
		return !feeDate.isBefore(start) && !feeDate.isAfter(end);
	}
}
