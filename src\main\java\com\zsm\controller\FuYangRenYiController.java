package com.zsm.controller;

import com.zsm.common.SaasAuthorizationVerify;
import com.zsm.model.ApiResult;
import com.zsm.model.dto.*;
import com.zsm.model.vo.*;
import com.zsm.his.impl.FuYangRenYiService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.web.bind.annotation.*;

import jakarta.annotation.Resource;
import java.util.List;

/**
 * <p>
 * 阜阳人医 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-02
 */
@Deprecated
@Tag(name = "阜阳人医", description = "门诊发药业务接口")
// @RestController
// @RequestMapping("/fuYangRenYi")
public class FuYangRenYiController {

    @Resource
    private FuYangRenYiService fuYangRenYiService;

    /**
     * 查询门诊处方接口（默认查询）
     *
     * @return 门诊处方数据
     */
    @PostMapping("/queryOutpatientPrescription")
    @Operation(summary = "查询门诊处方")
    @SaasAuthorizationVerify
    public ApiResult<List<OutpatientPrescriptionResponseVo.PrescriptionItem>> queryOutpatientPrescription(@RequestBody OutpatientPrescriptionQueryDto queryDto) {
        return fuYangRenYiService.queryOutpatientPrescription(queryDto);
    }

    /**
     * 住院领药单接口
     *
     * @param queryDto 查询参数
     * @return 住院处方数据
     */
    @PostMapping("/queryInpatientPrescription")
    @Operation(summary = "住院领药单接口")
    public ApiResult<List<InpatientPrescriptionResponseVo.InpatientPrescriptionItem>> queryInpatientPrescription(@RequestBody InpatientPrescriptionQueryDto queryDto) {
        return fuYangRenYiService.queryInpatientPrescription(queryDto);
    }

}
