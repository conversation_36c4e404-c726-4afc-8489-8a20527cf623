package com.zsm.model.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.Date;
import java.util.List;

/**
 * 发药明细VO
 *
 * <AUTHOR>
 * @date 2025-05-16
 */
@Data
@Schema(description = "发药明细VO")
public class DispensingDetailVo implements Serializable {

    private static final long serialVersionUID = 1L;

    @Schema(description = "业务子ID，通常是发药单明细ID或处方明细ID")
    private String bizSubId;

    @Schema(description = "发药单ID")
    private String dispensingId;

    @Schema(description = "发药单号")
    private String dispensingNo;

    @Schema(description = "处方号")
    private String prescriptionNo;

    @Schema(description = "患者ID")
    private String patientId;

    @Schema(description = "患者姓名")
    private String patientName;

    @Schema(description = "商品编码")
    private String drugCode;

    @Schema(description = "商品名称")
    private String drugName;

    @Schema(description = "商品规格")
    private String specification;

    @Schema(description = "单位")
    private String unit;

    @Schema(description = "数量")
    private BigDecimal quantity;

    @Schema(description = "单价")
    private BigDecimal price;

    @Schema(description = "总金额")
    private BigDecimal amount;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Schema(description = "发药时间")
    private LocalDateTime dispensingTime;

    @Schema(description = "发药人")
    private String dispensingUser;

    @Schema(description = "库房ID")
    private String storageId;

    @Schema(description = "库房名称")
    private String storageName;

    @Schema(description = "已追溯数量")
    private Integer tracedCount;

    @Schema(description = "追溯码列表")
    private List<String> traceCodes;
} 