package com.zsm.his.impl;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.zsm.common.exception.BusinessException;
import com.zsm.entity.*;
import com.zsm.his.DispensingService;
import com.zsm.mapper.YsfStoTcStatusMapper;
import com.zsm.mapper.YsfStoTcTaskMapper;
import com.zsm.model.ApiResult;
import com.zsm.model.dto.InpatientPrescriptionQueryDto;
import com.zsm.model.dto.OutpatientPrescriptionQueryDto;
import com.zsm.model.enums.TaskStatusEnum;
import com.zsm.model.vo.DispensingDetailVo;
import com.zsm.model.vo.OutpatientPrescriptionResponseVo;
import com.zsm.model.vo.YsfStoDpsTaskVo;
import com.zsm.service.ViewMzfycfService;
import com.zsm.service.ViewZyfycfFyService;
import com.zsm.service.YsfStoDpsService;
import com.zsm.service.YsfStoTcTaskService;
import com.zsm.utils.SoapUtil;
import com.zsm.utils.SpringUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import jakarta.annotation.Resource;
import java.math.BigDecimal;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 发药服务实现类
 *
 * <AUTHOR>
 * @date 2025-06-10
 */
@Slf4j
@Service
public class DispensingServiceImpl implements DispensingService {

    @Resource
    private YsfStoTcStatusMapper ysfStoTcStatusMapper;
    @Resource
    private YsfStoTcTaskMapper ysfStoTcTaskMapper;
    @Resource
    private YsfStoTcTaskService ysfStoTcTaskService;
    @Resource
    private YsfStoDpsService ysfStoDpsService;
    @Resource
    private ViewMzfycfService viewMzfycfService;
    @Resource
    private ViewZyfycfFyService viewZyfycfFyService;
    @Resource
    private FuYangZhongLiuService fuYangZhongLiuService;

    @Override
    public DispensingDetailVo getDetailByBizSubId(String bizSubId) {
        // 查询与业务子ID关联的追溯码记录
        LambdaQueryWrapper<YsfStoTcStatus> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(YsfStoTcStatus::getCfmxxh, bizSubId)
                .in(YsfStoTcStatus::getSdTcStatus, "3001", "3002"); // 门诊和住院发药类型

        List<YsfStoTcStatus> statusList = ysfStoTcStatusMapper.selectList(queryWrapper);

        if (statusList.isEmpty()) {
            return null;
        }

        // 构建发药明细信息
        DispensingDetailVo detailVo = new DispensingDetailVo();

        // 从第一条记录获取基本信息
        YsfStoTcStatus firstStatus = statusList.get(0);
        detailVo.setBizSubId(bizSubId);
        detailVo.setDispensingId(firstStatus.getIdBizOri()); // 主单ID通常在id_biz_ori字段
        // 这里模拟填充其他数据，实际应从数据库获取
        detailVo.setDispensingNo("DPS" + firstStatus.getIdBizOri());
        detailVo.setPrescriptionNo("RX" + (System.currentTimeMillis() % 10000));
        detailVo.setPatientId("PT" + (System.currentTimeMillis() % 10000));
        detailVo.setPatientName("患者" + bizSubId.substring(Math.max(0, bizSubId.length() - 4)));
        detailVo.setDrugCode(firstStatus.getDrugCode());
        detailVo.setDrugName("药品-" + firstStatus.getDrugCode());
        detailVo.setSpecification("规格-标准");
        detailVo.setUnit("盒");

        // 计算总数量（可能多条追溯码对应同一个明细）
        int totalQuantity = statusList.stream()
                .mapToInt(YsfStoTcStatus::getSelRetnCnt)
                .sum();
        detailVo.setQuantity(new BigDecimal(totalQuantity));

        detailVo.setPrice(new BigDecimal("10.50"));
        detailVo.setAmount(detailVo.getPrice().multiply(detailVo.getQuantity()));
        detailVo.setDispensingTime(firstStatus.getCreateTime());
        detailVo.setDispensingUser(firstStatus.getCreateBy());
        detailVo.setStorageId(firstStatus.getIdDept());
        detailVo.setStorageName("药房-" + firstStatus.getIdDept());

        // 设置追溯数量和追溯码
        detailVo.setTracedCount(statusList.size());
        List<String> traceCodes = statusList.stream()
                .map(YsfStoTcStatus::getDrugtracinfo)
                .collect(Collectors.toList());
        detailVo.setTraceCodes(traceCodes);

        return detailVo;
    }

    @Override
    public void syncDispenseTime() {

        // 查询任务
        List<YsfStoDpsTaskVo> taskList = ysfStoTcTaskMapper.selectDpsInfoByPendingTask();
        log.info("获取到待同步的任务数:{},详细数据:{}", taskList.size(), JSONUtil.toJsonStr(taskList));
        for (YsfStoDpsTaskVo task : taskList) {
            String cfxh = task.getCfxh();
            // 判断任务是否在8小时内
            if (task.getCreateTime()
                    .getTime() > DateUtil.offsetHour(DateUtil.date(), 8)
                    .getTime()) {
                log.info("任务创建时间超过8小时, task: {}", task);
                // 修改任务状态
                ysfStoTcTaskService.lambdaUpdate()
                        .eq(YsfStoTcTask::getCdBiz, cfxh)
                        .eq(YsfStoTcTask::getDelFlag, "0")
                        .set(YsfStoTcTask::getFgStatus, TaskStatusEnum.EXPIRED.getCode())
                        .update();
                continue;
            }

            // 调用远程东华接口获取处方信息
            OutpatientPrescriptionQueryDto queryDto = new OutpatientPrescriptionQueryDto();
            queryDto.setCfxh(cfxh);
            queryDto.setSend_flag("1"); // 只查询已发药的处方

            ApiResult soapResult = SoapUtil.callSoapServiceWithParams("MES0271", queryDto);

            if (!soapResult.isSuccess()) {
                log.error("SOAP服务调用失败: {}", soapResult.getMsg());
                throw new BusinessException(soapResult.getMsg());
            }

            // 解析响应数据
            Map<String, Object> responseData = (Map<String, Object>) soapResult.getData();
            OutpatientPrescriptionResponseVo prescriptionResponse = SoapUtil.parseOutpatientResponseData(responseData);
            final List<OutpatientPrescriptionResponseVo.PrescriptionItem> dataList = prescriptionResponse.getDataList();


            if (dataList != null && !dataList.isEmpty()) {
                for (OutpatientPrescriptionResponseVo.PrescriptionItem drugDispenseInfo : dataList) {
                    if (drugDispenseInfo.getCfxh()
                            .equals(cfxh) && ObjectUtil.isNotNull(drugDispenseInfo.getSend_time())) {
                        String sendTime = drugDispenseInfo.getSend_time();

                        // 修改发药表的发药时间,发药状态
                        ysfStoDpsService.lambdaUpdate()
                                .eq(YsfStoDps::getCfxh, cfxh)
                                .eq(YsfStoDps::getDelFlag, "0")
                                .set(YsfStoDps::getSendTime, sendTime)
                                .set(YsfStoDps::getFgStatus, TaskStatusEnum.COMPLETED.getCode())
                                .update();

                        // 修改任务表的任务状态
                        ysfStoTcTaskService.lambdaUpdate()
                                .eq(YsfStoTcTask::getCdBiz, cfxh)
                                .eq(YsfStoTcTask::getDelFlag, "0")
                                .set(YsfStoTcTask::getFgStatus, TaskStatusEnum.COMPLETED.getCode())
                                .update();
                    }
                }
            }
        }
    }

    @Override
    public void syncAnQingShiHuaDispenseTime() {
        // 查询任务
        List<YsfStoDpsTaskVo> taskList = ysfStoTcTaskMapper.selectDpsInfoByPendingTask();
        log.info("获取到待同步的任务数:{},详细数据:{}", taskList.size(), JSONUtil.toJsonStr(taskList));
        for (YsfStoDpsTaskVo task : taskList) {
            String cfxh = task.getCfxh();
            // 判断任务是否在8小时内
            if (task.getCreateTime()
                    .getTime() > DateUtil.offsetHour(DateUtil.date(), 8)
                    .getTime()) {
                log.info("任务创建时间超过8小时, task: {}", task);
                // 修改任务状态
                ysfStoTcTaskService.lambdaUpdate()
                        .eq(YsfStoTcTask::getCdBiz, cfxh)
                        .eq(YsfStoTcTask::getDelFlag, "0")
                        .set(YsfStoTcTask::getFgStatus, TaskStatusEnum.EXPIRED.getCode())
                        .update();
                continue;
            }

            // 根据发药单类型区分处理住院和门诊处方
            String sdDps = task.getSdDps();
            if ("1".equals(sdDps)) {
                // 住院处方
                syncInpatientDispenseTime(cfxh);
            } else {
                // 门诊处方
                syncOutpatientDispenseTime(cfxh);
            }
        }
    }


    /**
     * 同步门诊处方发药时间
     *
     * @param cfxh 处方序号
     */
    private void syncOutpatientDispenseTime(String cfxh) {
        // 调用his获取门诊处方发药信息
        final List<ViewMzfycf> mzDrugDetailList = SpringUtils.getAopProxy(this).getMzDrugDetailList(cfxh);
        log.info("从视图查询门诊处方：{}的发药信息，获取到的结果: {}", cfxh, JSONUtil.toJsonStr(mzDrugDetailList));
        if (mzDrugDetailList.isEmpty()) {
            return;
        }

        for (ViewMzfycf drugDispenseInfo : mzDrugDetailList) {
            if (drugDispenseInfo.getCfxh().toString().equals(cfxh) && ObjectUtil.isNotNull(drugDispenseInfo.getSelRetnTime())) {
                String sendTime = drugDispenseInfo.getSelRetnTime();

                // 修改发药表的发药时间,发药状态
                ysfStoDpsService.lambdaUpdate()
                        .eq(YsfStoDps::getCfxh, cfxh)
                        .eq(YsfStoDps::getDelFlag, "0")
                        .set(YsfStoDps::getSendTime, sendTime)
                        .set(YsfStoDps::getFgStatus, TaskStatusEnum.COMPLETED.getCode())
                        .update();

                // 修改任务表的任务状态
                ysfStoTcTaskService.lambdaUpdate()
                        .eq(YsfStoTcTask::getCdBiz, cfxh)
                        .eq(YsfStoTcTask::getDelFlag, "0")
                        .set(YsfStoTcTask::getFgStatus, TaskStatusEnum.COMPLETED.getCode())
                        .update();
            }
        }
    }

    /**
     * 同步住院处方发药时间
     *
     * @param cfxh 处方序号
     */
    private void syncInpatientDispenseTime(String cfxh) {
        // 调用his获取住院处方发药信息
        final List<ViewZyfycfFy> zyDrugDetailList = SpringUtils.getAopProxy(this).getZyDrugDetailList(cfxh);
        log.info("从视图查询住院处方：{}的发药信息，获取到的结果: {}", cfxh, JSONUtil.toJsonStr(zyDrugDetailList));
        if (zyDrugDetailList.isEmpty()) {
            return;
        }

        for (ViewZyfycfFy drugDispenseInfo : zyDrugDetailList) {
            if (drugDispenseInfo.getCfxh().toString().equals(cfxh) && ObjectUtil.isNotNull(drugDispenseInfo.getSelRetnTime())) {
                String sendTime = drugDispenseInfo.getSelRetnTime();

                // 修改发药表的发药时间,发药状态
                ysfStoDpsService.lambdaUpdate()
                        .eq(YsfStoDps::getCfxh, cfxh)
                        .eq(YsfStoDps::getDelFlag, "0")
                        .set(YsfStoDps::getSendTime, sendTime)
                        .set(YsfStoDps::getFgStatus, TaskStatusEnum.COMPLETED.getCode())
                        .update();

                // 修改任务表的任务状态
                ysfStoTcTaskService.lambdaUpdate()
                        .eq(YsfStoTcTask::getCdBiz, cfxh)
                        .eq(YsfStoTcTask::getDelFlag, "0")
                        .set(YsfStoTcTask::getFgStatus, TaskStatusEnum.COMPLETED.getCode())
                        .update();
            }
        }
    }

    @DS("his")
    public List<ViewMzfycf> getMzDrugDetailList(String cfxh) {
        // 构建查询条件
        LambdaQueryWrapper<ViewMzfycf> queryWrapper = new LambdaQueryWrapper<>();


        // 如果指定了处方序号，按处方序号查询
        if (org.apache.commons.lang3.StringUtils.isNotEmpty(cfxh)) {
            String[] cfxhArray = cfxh.split(",");
            if (cfxhArray.length == 1) {
                queryWrapper.eq(ViewMzfycf::getCfxh, Long.valueOf(cfxhArray[0].trim()));
            } else {
                List<Long> cfxhList = Arrays.stream(cfxhArray)
                        .map(String::trim)
                        .map(Long::valueOf)
                        .collect(Collectors.toList());
                queryWrapper.in(ViewMzfycf::getCfxh, cfxhList);
            }
        }

        return viewMzfycfService.list(queryWrapper);
    }

    @DS("his")
    public List<ViewZyfycfFy> getZyDrugDetailList(String cfxh) {
        // 构建查询条件
        LambdaQueryWrapper<ViewZyfycfFy> queryWrapper = new LambdaQueryWrapper<>();

        // 如果指定了处方序号，按处方序号查询
        if (org.apache.commons.lang3.StringUtils.isNotEmpty(cfxh)) {
            String[] cfxhArray = cfxh.split(",");
            if (cfxhArray.length == 1) {
                queryWrapper.eq(ViewZyfycfFy::getCfxh, Long.valueOf(cfxhArray[0].trim()));
            } else {
                List<Long> cfxhList = Arrays.stream(cfxhArray)
                        .map(String::trim)
                        .map(Long::valueOf)
                        .collect(Collectors.toList());
                queryWrapper.in(ViewZyfycfFy::getCfxh, cfxhList);
            }
        }

        return viewZyfycfFyService.list(queryWrapper);
    }

    @Override
    public void syncFuYangZhongLiuDispenseTime() {
        // 查询任务
        List<YsfStoDpsTaskVo> taskList = ysfStoTcTaskMapper.selectDpsInfoByPendingTask();
        log.info("获取到待同步的任务数:{},详细数据:{}", taskList.size(), JSONUtil.toJsonStr(taskList));
        for (YsfStoDpsTaskVo task : taskList) {
            String cfxh = task.getCfxh();
            // 判断任务是否在8小时内
            if (task.getCreateTime()
                    .getTime() > DateUtil.offsetHour(DateUtil.date(), 8)
                    .getTime()) {
                log.info("任务创建时间超过8小时, task: {}", task);
                // 修改任务状态
                ysfStoTcTaskService.lambdaUpdate()
                        .eq(YsfStoTcTask::getCdBiz, cfxh)
                        .eq(YsfStoTcTask::getDelFlag, "0")
                        .set(YsfStoTcTask::getFgStatus, TaskStatusEnum.EXPIRED.getCode())
                        .update();
                continue;
            }

            // 根据发药单类型区分处理住院和门诊处方
            String sdDps = task.getSdDps();
            if ("1".equals(sdDps)) {
                // 住院处方
                syncFuYangZhongLiuInpatientDispenseTime(cfxh);
            } else {
                // 门诊处方
                syncFuYangZhongLiuOutpatientDispenseTime(cfxh);
            }
        }
    }

    /**
     * 同步阜阳肿瘤门诊处方发药时间
     *
     * @param cfxh 处方序号
     */
    private void syncFuYangZhongLiuOutpatientDispenseTime(String cfxh) {
        // 调用阜阳肿瘤服务获取门诊处方发药信息
        OutpatientPrescriptionQueryDto queryDto = new OutpatientPrescriptionQueryDto();
        queryDto.setCfxh(cfxh);
        final List<ViewYsfMzfycf> mzDrugDetailList = SpringUtils.getAopProxy(fuYangZhongLiuService).getYsfMzDrugDetailList(queryDto);
        log.info("从阜阳肿瘤服务查询门诊处方：{}的发药信息，获取到的结果: {}", cfxh, JSONUtil.toJsonStr(mzDrugDetailList));
        if (mzDrugDetailList.isEmpty()) {
            return;
        }

        for (ViewYsfMzfycf drugDispenseInfo : mzDrugDetailList) {
            if (drugDispenseInfo.getCfxh().toString().equals(cfxh) && ObjectUtil.isNotNull(drugDispenseInfo.getSelRetnTime())) {
                String sendTime = drugDispenseInfo.getSelRetnTime();

                // 修改发药表的发药时间,发药状态
                ysfStoDpsService.lambdaUpdate()
                        .eq(YsfStoDps::getCfxh, cfxh)
                        .eq(YsfStoDps::getDelFlag, "0")
                        .set(YsfStoDps::getSendTime, sendTime)
                        .set(YsfStoDps::getFgStatus, TaskStatusEnum.COMPLETED.getCode())
                        .update();

                // 修改任务表的任务状态
                ysfStoTcTaskService.lambdaUpdate()
                        .eq(YsfStoTcTask::getCdBiz, cfxh)
                        .eq(YsfStoTcTask::getDelFlag, "0")
                        .set(YsfStoTcTask::getFgStatus, TaskStatusEnum.COMPLETED.getCode())
                        .update();
            }
        }
    }

    /**
     * 同步阜阳肿瘤住院处方发药时间
     *
     * @param cfxh 处方序号
     */
    private void syncFuYangZhongLiuInpatientDispenseTime(String cfxh) {
        // 调用阜阳肿瘤服务获取住院处方发药信息
        InpatientPrescriptionQueryDto queryDto = new InpatientPrescriptionQueryDto();
        queryDto.setCfxh(cfxh);
        final List<ViewYsfZyfycf> zyDrugDetailList = SpringUtils.getAopProxy(fuYangZhongLiuService).getYsfZyDrugDetailList(queryDto);
        log.info("从阜阳肿瘤服务查询住院处方：{}的发药信息，获取到的结果: {}", cfxh, JSONUtil.toJsonStr(zyDrugDetailList));
        if (zyDrugDetailList.isEmpty()) {
            return;
        }

        for (ViewYsfZyfycf drugDispenseInfo : zyDrugDetailList) {
            if (drugDispenseInfo.getCfxh().toString().equals(cfxh) && ObjectUtil.isNotNull(drugDispenseInfo.getSelRetnTime())) {
                String sendTime = drugDispenseInfo.getSelRetnTime();

                // 修改发药表的发药时间,发药状态
                ysfStoDpsService.lambdaUpdate()
                        .eq(YsfStoDps::getCfxh, cfxh)
                        .eq(YsfStoDps::getDelFlag, "0")
                        .set(YsfStoDps::getSendTime, sendTime)
                        .set(YsfStoDps::getFgStatus, TaskStatusEnum.COMPLETED.getCode())
                        .update();

                // 修改任务表的任务状态
                ysfStoTcTaskService.lambdaUpdate()
                        .eq(YsfStoTcTask::getCdBiz, cfxh)
                        .eq(YsfStoTcTask::getDelFlag, "0")
                        .set(YsfStoTcTask::getFgStatus, TaskStatusEnum.COMPLETED.getCode())
                        .update();
            }
        }
    }
} 