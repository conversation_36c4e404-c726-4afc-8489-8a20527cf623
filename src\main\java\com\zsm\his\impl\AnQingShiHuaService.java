package com.zsm.his.impl;

import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.zsm.common.SaasAuthorizationVerifyAspect;
import com.zsm.entity.*;
import com.zsm.his.HisPrescriptionService;
import com.zsm.mapper.YsfStoTcTaskMapper;
import com.zsm.model.ApiResult;
import com.zsm.model.dto.InpatientPrescriptionQueryDto;
import com.zsm.model.dto.OutpatientPrescriptionQueryDto;
import com.zsm.model.enums.TaskStatusEnum;
import com.zsm.model.saas.request.*;
import com.zsm.model.saas.response.*;
import com.zsm.model.vo.InpatientPrescriptionResponseVo;
import com.zsm.model.vo.OutpatientPrescriptionResponseVo;
import com.zsm.model.vo.SaasUserInfoResponse;
import com.zsm.service.*;
import com.zsm.utils.SaasHttpUtil;
import com.zsm.utils.SpringUtils;
import com.zsm.constant.NhsaAccountConstant;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import jakarta.annotation.Resource;
import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 安庆石化医院业务层
 *
 * <AUTHOR>
 * @date 2025/6/10 21:43
 */
@Slf4j
@Service()
@Deprecated
public class AnQingShiHuaService implements HisPrescriptionService {

    @Resource
    private ViewZyfycfFyService viewZyfycfFyService;
    @Resource
    private ViewMzfycfService viewMzfycfService;
    @Resource
    private Yp1Service yp1Service;
    @Resource
    private YsfStoTcTaskMapper ysfStoTcTaskMapper;
    @Resource
    private Nhsa3505Service nhsa3505Service;

    @DS("his")
    public List<ViewZyfycfFy> getZyDrugDetailList(InpatientPrescriptionQueryDto queryDto) {
        // 添加调试日志，验证数据源切换
        log.info("使用数据源：his，开始查询住院药品明细");

        // 处理默认时间
        String startTime = queryDto.getStart_time();
        String endTime = queryDto.getEnd_time();

        // 如果开始时间和结束时间都为空，设置默认时间范围
        if (StringUtils.isEmpty(startTime) && StringUtils.isEmpty(endTime)) {
            // 昨天
            DateTime yesterday = DateUtil.yesterday();
            startTime = DateUtil.format(yesterday, "yyyy-MM-dd");

            // 今天 + 1天（因为结束时间用的是小于查询）
            DateTime tomorrow = DateUtil.offsetDay(DateUtil.date(), 1);
            endTime = DateUtil.format(tomorrow, "yyyy-MM-dd");

            log.info("getZyDrugDetailList - 使用默认时间范围：{} 到 {}", startTime, endTime);
        }

        // 构建查询条件
        LambdaQueryWrapper<ViewZyfycfFy> queryWrapper = new LambdaQueryWrapper<>();

        // 根据查询参数设置过滤条件
        if (StringUtils.isNotEmpty(queryDto.getPatientId())) {
            queryWrapper.eq(ViewZyfycfFy::getPatientId, queryDto.getPatientId());
        }
        if (StringUtils.isNotEmpty(startTime)) {
            queryWrapper.ge(ViewZyfycfFy::getSelRetnTime, startTime);
        }
        if (StringUtils.isNotEmpty(endTime)) {
            queryWrapper.lt(ViewZyfycfFy::getSelRetnTime, endTime);
        }
        // 如果指定了记录ID，按记录ID查询
        if (StringUtils.isNotEmpty(queryDto.getRecord_id())) {
            queryWrapper.eq(ViewZyfycfFy::getRecordId, queryDto.getRecord_id());
        }
        // 如果指定了病区名称，按病区名称查询
        if (StringUtils.isNotEmpty(queryDto.getPat_ward_name())) {
            queryWrapper.eq(ViewZyfycfFy::getPatWardName, queryDto.getPat_ward_name());
        }
        // 如果指定了发药单标记，按此标志查询（这里暂时注释，因为视图中没有此字段）
        // if (StringUtils.isNotEmpty(queryDto.getFg_dps())) {
        //     queryWrapper.eq(ViewZyfycfFy::getFgDps, queryDto.getFg_dps());
        // }

        try {
            List<ViewZyfycfFy> result = viewZyfycfFyService.list(queryWrapper);
            log.info("getZyDrugDetailList - 查询完成，返回{}条记录，数据详情：{}", result.size(), JSONUtil.toJsonStr(result));
            return result;
        } catch (Exception e) {
            log.error("getZyDrugDetailList - 查询异常：{}", e.getMessage(), e);
            throw e;
        }
    }

    @DS("his")
    public List<ViewMzfycf> getMzDrugDetailList(OutpatientPrescriptionQueryDto queryDto) {
        // 添加调试日志，验证数据源切换
        log.info("getMzDrugDetailList - 使用数据源：his，开始查询门诊药品明细");

        // 处理默认时间
        String startTime = queryDto.getStartTime();
        String endTime = queryDto.getEndTime();

        // 如果开始时间和结束时间都为空，设置默认时间范围
        if (StringUtils.isEmpty(startTime) && StringUtils.isEmpty(endTime)) {
            // 昨天
            DateTime yesterday = DateUtil.yesterday();
            startTime = DateUtil.format(yesterday, "yyyy-MM-dd");

            // 今天 + 1天（因为结束时间用的是小于查询）
            DateTime tomorrow = DateUtil.offsetDay(DateUtil.date(), 1);
            endTime = DateUtil.format(tomorrow, "yyyy-MM-dd");

            log.info("getMzDrugDetailList - 使用默认时间范围：{} 到 {}", startTime, endTime);
        }

        // 构建查询条件
        LambdaQueryWrapper<ViewMzfycf> queryWrapper = new LambdaQueryWrapper<>();

        // 根据查询参数设置过滤条件
        if (StringUtils.isNotEmpty(queryDto.getBlh())) {
            queryWrapper.eq(ViewMzfycf::getBlh, queryDto.getBlh());
        }
        if (StringUtils.isNotEmpty(queryDto.getPatient_id())) {
            queryWrapper.eq(ViewMzfycf::getPatid, queryDto.getPatient_id());
        }
        if (StringUtils.isNotEmpty(startTime)) {
            queryWrapper.ge(ViewMzfycf::getSelRetnTime, startTime);
        }
        if (StringUtils.isNotEmpty(endTime)) {
            queryWrapper.lt(ViewMzfycf::getSelRetnTime, endTime);
        }
        // 如果指定了处方序号，按处方序号查询
        if (StringUtils.isNotEmpty(queryDto.getCfxh())) {
            String[] cfxhArray = queryDto.getCfxh().split(",");
            if (cfxhArray.length == 1) {
                queryWrapper.eq(ViewMzfycf::getCfxh, Long.valueOf(cfxhArray[0].trim()));
            } else {
                List<Long> cfxhList = Arrays.stream(cfxhArray).map(String::trim).map(Long::valueOf).collect(Collectors.toList());
                queryWrapper.in(ViewMzfycf::getCfxh, cfxhList);
            }
        }

        try {
            List<ViewMzfycf> result = viewMzfycfService.list(queryWrapper);
            log.info("getMzDrugDetailList - 查询完成，返回{}条记录，数据详情：{}", result.size(), JSONUtil.toJsonStr(result));
            return result;
        } catch (Exception e) {
            log.error("getMzDrugDetailList - 查询异常：{}", e.getMessage(), e);
            throw e;
        }
    }

    /**
     * 门诊处方查询
     *
     * @param queryDto 查询dto
     * @return {@link ApiResult }<{@link List }<{@link OutpatientPrescriptionResponseVo.PrescriptionItem }>>
     */
    @Override
    public ApiResult<List<OutpatientPrescriptionResponseVo.PrescriptionItem>> queryOutpatientPrescription(OutpatientPrescriptionQueryDto queryDto) {
        try {
            log.info("开始查询门诊处方信息，参数: {}", queryDto);

            // 参数校验
            if (queryDto == null) {
                queryDto = new OutpatientPrescriptionQueryDto();
            }
            SaasUserInfoResponse userInfo = SaasAuthorizationVerifyAspect.userInfoThreadLocal.get();
            String fyyfId = userInfo.getUser().getFyyfId().trim();
            // 调用本地方法查询门诊药品明细
            final List<ViewMzfycf> entityList = SpringUtils.getAopProxy(this).getMzDrugDetailList(queryDto);
            // 根据当前token用户的药房id，过滤自己当前药房可看的数据
            List<ViewMzfycf> newList = new ArrayList<>();
            // 只有当fyyfId=405或406的时候才进行判断是否进行过滤加入newList中
            if ("405".equals(fyyfId) || "406".equals(fyyfId)) {
                entityList.forEach(item -> {
                    if (item.getFyyf().trim().equals(fyyfId)) {
                        newList.add(item);
                    }
                });
            } else {
                // 其他药房ID时，不进行过滤，直接使用原列表
                newList.addAll(entityList);
            }
            log.info("根据当前用户药房id过滤后的数据，共{}条,数据详情：{}", newList.size(), JSONUtil.toJsonStr(newList));

            // 将实体转换为响应VO
            List<OutpatientPrescriptionResponseVo.PrescriptionItem> voList = newList.stream()
                    .map(this::convertToPrescriptionItem).collect(Collectors.toList());

            log.info("门诊处方查询成功，返回{}条数据", voList.size());

            if (voList != null && !voList.isEmpty()) {
                // 处理药品追溯码库存信息
                this.handleTracCodgStoreCydy(voList);

                // 处理每条处方数据,过滤掉已完成扫码任务的药品信息,防止后续业务重复上传造成脏数据
                Iterator<OutpatientPrescriptionResponseVo.PrescriptionItem> iterator = voList.iterator();
                while (iterator.hasNext()) {
                    OutpatientPrescriptionResponseVo.PrescriptionItem dispenseInfo = iterator.next();
                    String outPresId = dispenseInfo.getCfxh();
                    if (StringUtils.isEmpty(outPresId)) {
                        continue;
                    }

                    // 1. 查询是否存在已完成且未删除的任务
                    LambdaQueryWrapper<YsfStoTcTask> completedTaskQuery = new LambdaQueryWrapper<>();
                    completedTaskQuery.eq(YsfStoTcTask::getCdBiz, outPresId)
                            .eq(YsfStoTcTask::getFgStatus, TaskStatusEnum.COMPLETED.getCode()) // 已完成
                            .eq(YsfStoTcTask::getDelFlag, "0"); // 未删除
                    Long completedTaskCount = ysfStoTcTaskMapper.selectCount(completedTaskQuery);

                    // 2. 如果存在已完成的任务，则过滤掉该处方数据
                    if (completedTaskCount > 0) {
                        log.info("存在已完成的任务，过滤掉该处方数据，处方号：{}", outPresId);
                        iterator.remove();
                        continue;
                    }

                    // 3. 如果不存在已完成的任务，则查询最新的待处理或已失效任务
                    LambdaQueryWrapper<YsfStoTcTask> latestTaskQuery = new LambdaQueryWrapper<>();
                    latestTaskQuery.eq(YsfStoTcTask::getCdBiz, outPresId)
                            .in(YsfStoTcTask::getFgStatus, Arrays.asList(TaskStatusEnum.PENDING.getCode(), TaskStatusEnum.EXPIRED.getCode())) // 待处理或已失效
                            .eq(YsfStoTcTask::getDelFlag, "0") // 未删除
                            .orderByDesc(YsfStoTcTask::getIdTask).last("limit 1"); // 按创建时间降序

                    YsfStoTcTask latestTask = ysfStoTcTaskMapper.selectOne(latestTaskQuery);

                    // 4. 如果找到这样的任务，则在处方对象中追加任务相关字段
                    if (latestTask != null) {
                        // 使用实体类中新增的字段
                        dispenseInfo.setTaskIdDps(latestTask.getIdTask() != null ? latestTask.getIdTask().toString() : null);
                        dispenseInfo.setTaskFgStatusDps(latestTask.getFgStatus());
                        dispenseInfo.setTaskScanTimeDps(latestTask.getCreateTime() != null ? latestTask.getCreateTime()
                                .format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")) : null);
                    }
                }

                // 异步,将查询到的药品信息保存到3505表中
                nhsa3505Service.save3505Async(voList);
            }

            return ApiResult.success(voList);

        } catch (Exception e) {
            String errorMsg = "查询门诊处方异常: " + e.getMessage();
            log.error(errorMsg, e);
            return ApiResult.error(errorMsg);
        }
    }

    /**
     * 安全地将字符串转换为整数，处理小数格式的字符串
     *
     * @param value 要转换的字符串值
     * @return 转换后的整数值，如果转换失败则返回0
     */
    private Integer safeParseInteger(String value) {
        if (StringUtils.isEmpty(value)) {
            return 0;
        }

        try {
            // 如果包含小数点，先转换为Double再取整
            if (value.contains(".")) {
                Double doubleValue = Double.valueOf(value);
                return doubleValue.intValue();
            } else {
                return Integer.valueOf(value);
            }
        } catch (NumberFormatException e) {
            log.warn("无法将字符串 '{}' 转换为整数，使用默认值0", value);
            return 0;
        }
    }

    /**
     * 处理住院药品的追溯码库存信息。
     * <p>
     * 用于处理住院处方药品列表。
     * 1. 调用SaaS平台接口查询药品是否需要追溯以及HIS系统中的转换比。
     * 2. 更新药品对象的追溯标志和HIS转换比。
     * 3. 如果药品需要追溯，则调用SaaS平台接口获取药品的追溯码库存信息。
     * 4. 将获取到的库存信息填充回药品对象。
     *
     * @param zsmViewList 住院药品列表。
     */
    private void handleTracCodgStoreZydy(List<InpatientPrescriptionResponseVo.InpatientPrescriptionItem> zsmViewList) {
        SaasUserInfoResponse userInfo = SaasAuthorizationVerifyAspect.userInfoThreadLocal.get();

        // 如果用户信息为空，跳过SaaS相关处理
        if (userInfo == null) {
            log.warn("SaaS用户信息为空，跳过追溯码库存处理");
            return;
        }

        // 1. 构建查询药品追溯信息的请求列表
        List<QueryTracDrugRequest> queryTracDrugRequestList = zsmViewList.stream()
                .filter(i -> StringUtils.isNotEmpty(i.getMin_sel_retn_cnt()) && StringUtils.isNotEmpty(i.getCfmxxh()))
                .map(i -> QueryTracDrugRequest.builder().cfxh(i.getCfxh()).cfmxxh(i.getCfmxxh())
                        .drugCode(i.getFixmedins_hilist_id()).dispCnt(safeParseInteger(i.getMin_sel_retn_cnt())).build())
                .collect(Collectors.toList());
        Map<String, GetTracCodgStoreDataResponse> tracCodgStoreMap = new HashMap<>();

        try {
            // 调用SaaS接口批量查询药品追溯信息
            Map<String, QueryTracDrugResponse> queryTracDrugMap = SaasHttpUtil.queryTracDrugMap(userInfo.getAuthorization(), queryTracDrugRequestList);

            List<GetTracCodgStoreDataRequest> dataList = new ArrayList<>(); // 用于存放需要查询追溯码库存的药品请求
            for (InpatientPrescriptionResponseVo.InpatientPrescriptionItem i : zsmViewList) {
                if (queryTracDrugMap.containsKey(i.getCfmxxh())) {
                    QueryTracDrugResponse queryTracDrug = queryTracDrugMap.get(i.getCfmxxh());
                    i.setTrdn_flag(Integer.valueOf(queryTracDrug.getIsTrac())); // 设置是否追溯标志
                    i.setHis_con_ratio(String.valueOf(queryTracDrug.getConRatio())); // 设置HIS转换比例
                }
                // 如果药品需要追溯
                if (i.getTrdn_flag() != null && i.getTrdn_flag() == 1 && StringUtils.isNotEmpty(i.getMin_sel_retn_cnt())) {
                    GetTracCodgStoreDataRequest build = GetTracCodgStoreDataRequest.builder().cfxh(i.getCfxh())
                            .cfmxxh(i.getCfmxxh()).dispCnt(safeParseInteger(i.getMin_sel_retn_cnt()))
                            .drugCode(i.getFixmedins_hilist_id()).build();
                    dataList.add(build);
                }
            }

            // 如果有需要查询库存的药品
            if (!ObjectUtils.isEmpty(dataList)) {
                // 调用SaaS接口批量获取药品追溯码库存信息
                List<GetTracCodgStoreDataResponse> tracCodgStore = SaasHttpUtil.getTracCodgStore(userInfo.getAuthorization(), GetTracCodgStoreRequest.builder()
                        .dataList(dataList).build());
                // 将库存信息列表转换为Map
                tracCodgStoreMap = tracCodgStore.stream()
                        .collect(Collectors.toMap(GetTracCodgStoreDataResponse::getCfmxxh, Function.identity(), (o, n) -> n));
            }
        } catch (Exception e) {
            log.error("调用SaaS平台接口处理追溯码库存信息失败", e);
            // 即使SaaS接口调用失败，也不影响主流程，继续返回基础数据
        }

        // 4. 将获取到的库存信息填充回药品对象
        for (InpatientPrescriptionResponseVo.InpatientPrescriptionItem i : zsmViewList) {
            if (i.getTrdn_flag() != null && i.getTrdn_flag() == 1) { // 只处理需要追溯的药品
                if (tracCodgStoreMap.containsKey(i.getCfmxxh())) {
                    GetTracCodgStoreDataResponse tracCodgStoreData = tracCodgStoreMap.get(i.getCfmxxh());
                    i.setDrugCode(tracCodgStoreData.getDrugCode());
                    i.setDrugTracCodgs(tracCodgStoreData.getDrugTracCodgs());
                    // 设置完整的库存对象
                    i.setTracCodgStore(tracCodgStoreData);
                    i.setDispCnt(tracCodgStoreData.getDispCnt());
                    i.setCurrNum(tracCodgStoreData.getCurrNum());
                }
            }
        }
    }

    /**
     * 处理药品的追溯码库存信息。
     * <p>
     * 用于处理(zsmViewList)列表。
     * 1. 调用SaaS平台接口查询药品是否需要追溯以及HIS系统中的转换比。
     * 2. 更新药品对象的追溯标志和HIS转换比。
     * 3. 如果药品需要追溯，则调用SaaS平台接口获取药品的追溯码库存信息。
     * 4. 将获取到的库存信息填充回药品对象。
     *
     * @param zsmViewList 药品列表。
     */
    private void handleTracCodgStoreCydy(List<OutpatientPrescriptionResponseVo.PrescriptionItem> zsmViewList) {
        SaasUserInfoResponse userInfo = SaasAuthorizationVerifyAspect.userInfoThreadLocal.get();

        // 如果用户信息为空，跳过SaaS相关处理
        if (userInfo == null) {
            log.warn("SaaS用户信息为空，跳过追溯码库存处理");
            return;
        }

        // 1. 构建查询药品追溯信息的请求列表
        List<QueryTracDrugRequest> queryTracDrugRequestList = zsmViewList.stream()
                .filter(i -> StringUtils.isNotEmpty(i.getMin_sel_retn_cnt()) && StringUtils.isNotEmpty(i.getCfmxxh()))
                .map(i -> QueryTracDrugRequest.builder().cfxh(i.getCfxh()).cfmxxh(i.getCfmxxh())
                        .drugCode(i.getFixmedins_hilist_id()).dispCnt(safeParseInteger(i.getMin_sel_retn_cnt())).build())
                .collect(Collectors.toList());
        Map<String, GetTracCodgStoreDataResponse> tracCodgStoreMap = new HashMap<>();

        try {
            // 调用SaaS接口批量查询药品追溯信息
            Map<String, QueryTracDrugResponse> queryTracDrugMap = SaasHttpUtil.queryTracDrugMap(userInfo.getAuthorization(), queryTracDrugRequestList);

            List<GetTracCodgStoreDataRequest> dataList = new ArrayList<>(); // 用于存放需要查询追溯码库存的药品请求
            for (OutpatientPrescriptionResponseVo.PrescriptionItem i : zsmViewList) {
                if (queryTracDrugMap.containsKey(i.getCfmxxh())) {
                    QueryTracDrugResponse queryTracDrug = queryTracDrugMap.get(i.getCfmxxh());
                    i.setTrdn_flag(Integer.valueOf(queryTracDrug.getIsTrac())); // 设置是否追溯标志
                    i.setHis_con_ratio(String.valueOf(queryTracDrug.getConRatio())); // 设置HIS转换比例
                }
                // 如果药品需要追溯
                if (i.getTrdn_flag() != null && i.getTrdn_flag() == 1 && StringUtils.isNotEmpty(i.getMin_sel_retn_cnt())) {
                    GetTracCodgStoreDataRequest build = GetTracCodgStoreDataRequest.builder().cfxh(i.getCfxh())
                            .cfmxxh(i.getCfmxxh()).dispCnt(safeParseInteger(i.getMin_sel_retn_cnt()))
                            .drugCode(i.getFixmedins_hilist_id()).build();
                    dataList.add(build);
                }
            }

            // 如果有需要查询库存的药品
            if (!ObjectUtils.isEmpty(dataList)) {
                // 调用SaaS接口批量获取药品追溯码库存信息
                List<GetTracCodgStoreDataResponse> tracCodgStore = SaasHttpUtil.getTracCodgStore(userInfo.getAuthorization(), GetTracCodgStoreRequest.builder()
                        .dataList(dataList).build());
                // 将库存信息列表转换为Map
                tracCodgStoreMap = tracCodgStore.stream()
                        .collect(Collectors.toMap(GetTracCodgStoreDataResponse::getCfmxxh, Function.identity(), (o, n) -> n));
            }
        } catch (Exception e) {
            log.error("调用SaaS平台接口处理追溯码库存信息失败", e);
            // 即使SaaS接口调用失败，也不影响主流程，继续返回基础数据
        }

        // 4. 将获取到的库存信息填充回药品对象
        for (OutpatientPrescriptionResponseVo.PrescriptionItem i : zsmViewList) {
            if (i.getTrdn_flag() != null && i.getTrdn_flag() == 1) { // 只处理需要追溯的药品
                if (tracCodgStoreMap.containsKey(i.getCfmxxh())) {
                    GetTracCodgStoreDataResponse tracCodgStoreData = tracCodgStoreMap.get(i.getCfmxxh());
                    i.setDrugCode(tracCodgStoreData.getDrugCode());
                    i.setDrugTracCodgs(tracCodgStoreData.getDrugTracCodgs());
                    // 设置完整的库存对象
                    i.setTracCodgStore(tracCodgStoreData);
                    i.setDispCnt(tracCodgStoreData.getDispCnt());
                    i.setCurrNum(tracCodgStoreData.getCurrNum());
                }
            }
        }
    }

    /**
     * 查询住院处方
     *
     * @param queryDto 查询dto
     * @return {@link ApiResult }<{@link List }<{@link InpatientPrescriptionResponseVo.InpatientPrescriptionItem }>>
     */
    @Override
    public ApiResult<List<InpatientPrescriptionResponseVo.InpatientPrescriptionItem>> queryInpatientPrescription(InpatientPrescriptionQueryDto queryDto) {
        try {
            log.info("开始查询住院处方信息，参数: {}", queryDto);

            // 参数校验
            if (queryDto == null) {
                queryDto = new InpatientPrescriptionQueryDto();
            }
            // SaasUserInfoResponse userInfo = SaasAuthorizationVerifyAspect.userInfoThreadLocal.get();
            // String fyyfId = userInfo.getUser().getFyyfId().trim();
            // 调用本地方法查询住院药品明细
            final List<ViewZyfycfFy> newList = SpringUtils.getAopProxy(this).getZyDrugDetailList(queryDto);

            log.info("根据当前用户药房id过滤后的数据，共{}条,数据详情：{}", newList.size(), JSONUtil.toJsonStr(newList));

            // 将实体转换为响应VO
            List<InpatientPrescriptionResponseVo.InpatientPrescriptionItem> voList = newList.stream()
                    .map(this::convertToInpatientPrescriptionItem).collect(Collectors.toList());

            log.info("住院处方查询成功，返回{}条数据", voList.size());

            if (voList != null && !voList.isEmpty()) {
                // 处理药品追溯码库存信息
                this.handleTracCodgStoreZydy(voList);

                // 处理每条处方数据,过滤掉已完成扫码任务的药品信息,防止后续业务重复上传造成脏数据
                Iterator<InpatientPrescriptionResponseVo.InpatientPrescriptionItem> iterator = voList.iterator();
                while (iterator.hasNext()) {
                    InpatientPrescriptionResponseVo.InpatientPrescriptionItem dispenseInfo = iterator.next();
                    String inPresId = dispenseInfo.getCfxh();
                    if (StringUtils.isEmpty(inPresId)) {
                        continue;
                    }

                    // 1. 查询是否存在已完成且未删除的任务
                    LambdaQueryWrapper<YsfStoTcTask> completedTaskQuery = new LambdaQueryWrapper<>();
                    completedTaskQuery.eq(YsfStoTcTask::getCdBiz, inPresId)
                            .eq(YsfStoTcTask::getFgStatus, TaskStatusEnum.COMPLETED.getCode()) // 已完成
                            .eq(YsfStoTcTask::getDelFlag, "0"); // 未删除
                    Long completedTaskCount = ysfStoTcTaskMapper.selectCount(completedTaskQuery);

                    // 2. 如果存在已完成的任务，则过滤掉该处方数据
                    if (completedTaskCount > 0) {
                        log.info("存在已完成的任务，过滤掉该处方数据，处方号：{}", inPresId);
                        iterator.remove();
                        continue;
                    }

                    // 3. 如果不存在已完成的任务，则查询最新的待处理或已失效任务
                    LambdaQueryWrapper<YsfStoTcTask> latestTaskQuery = new LambdaQueryWrapper<>();
                    latestTaskQuery.eq(YsfStoTcTask::getCdBiz, inPresId)
                            .in(YsfStoTcTask::getFgStatus, Arrays.asList(TaskStatusEnum.PENDING.getCode(), TaskStatusEnum.EXPIRED.getCode())) // 待处理或已失效
                            .eq(YsfStoTcTask::getDelFlag, "0") // 未删除
                            .orderByDesc(YsfStoTcTask::getIdTask).last("limit 1"); // 按创建时间降序

                    YsfStoTcTask latestTask = ysfStoTcTaskMapper.selectOne(latestTaskQuery);

                    // 4. 如果找到这样的任务，则在处方对象中追加任务相关字段
                    if (latestTask != null) {
                        // 使用实体类中新增的字段
                        dispenseInfo.setTaskIdDps(latestTask.getIdTask() != null ? latestTask.getIdTask().toString() : null);
                        dispenseInfo.setTaskFgStatusDps(latestTask.getFgStatus());
                        dispenseInfo.setTaskScanTimeDps(latestTask.getCreateTime() != null ? latestTask.getCreateTime()
                                .format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")) : null);
                    }
                }

                // 异步,将查询到的药品信息保存到3505表中
                nhsa3505Service.save3505ZyAsync(voList);
            }

            return ApiResult.success(voList);

        } catch (Exception e) {
            String errorMsg = "查询住院处方异常: " + e.getMessage();
            log.error(errorMsg, e);
            return ApiResult.error(errorMsg);
        }
    }

    /**
     * 实体转处方响应项
     */
    private OutpatientPrescriptionResponseVo.PrescriptionItem convertToPrescriptionItem(ViewMzfycf e) {
        OutpatientPrescriptionResponseVo.PrescriptionItem item = new OutpatientPrescriptionResponseVo.PrescriptionItem();

        // 基本药品信息
        item.setMed_list_codg(e.getMedListCodg());
        item.setFixmedins_hilist_id(e.getFixmedinsHilistId());
        item.setFixmedins_hilist_name(e.getFixmedinsHilistName());
        item.setFixmedins_bchno(e.getFixmedinsBchno());

        // 医师/药师信息
        item.setPrsc_dr_name(e.getPrscDrName());
        item.setPhar_name(e.getPharName());
        item.setPhar_prac_cert_no(e.getPharPracCertNo());

        // 就诊与人员信息
        item.setMdtrt_sn(e.getMdtrtSn());
        item.setPsn_name(e.getPsnName());

        // 生产及有效期信息
        item.setManu_lotnum(e.getManuLotnum());
        item.setManu_date(e.getManuDate());
        item.setExpy_end(e.getExpyEnd());

        // 标志及号信息
        if (e.getRxFlag() != null) {
            item.setRx_flag(Integer.valueOf(e.getRxFlag()));
        }
        if (StringUtils.isNotEmpty(e.getTrdnFlag())) {
            try {
                item.setTrdn_flag(Integer.valueOf(e.getTrdnFlag()));
            } catch (NumberFormatException ignored) {
            }
        }

        if (e.getRtalDocno() != null) {
            item.setRtal_docno(String.valueOf(e.getRtalDocno()));
        }

        // 数量及时间信息
        if (e.getSelRetnCnt() != null) {
            item.setSel_retn_cnt(e.getSelRetnCnt().toPlainString());
        }
        // 正确映射最小售退数量
        if (StringUtils.isNotEmpty(e.getMinSelRetnCnt())) {
            item.setMin_sel_retn_cnt(e.getMinSelRetnCnt());
        }
        item.setSel_retn_time(e.getSelRetnTime());
        item.setSel_retn_opter_name(e.getSelRetnOpterName());

        // 其它信息
        if (StringUtils.isNotEmpty(e.getMdtrtSetlType())) {
            try {
                item.setMdtrt_setl_type(Integer.valueOf(e.getMdtrtSetlType()));
            } catch (NumberFormatException ignored) {
            }
        }

        item.setSpec(e.getSpec());
        item.setProdentp_name(e.getProdentpName());

        // 处方序号和明细序号
        if (e.getCfxh() != null) {
            item.setCfxh(String.valueOf(e.getCfxh()));
        }
        if (e.getCfmxxh() != null) {
            item.setCfmxxh(String.valueOf(e.getCfmxxh()));
        }

        // 其他字段
        item.setSjh(e.getSjh());
        item.setPatient_id(e.getPatid());
        item.setFyyf(e.getFyyf());

        // 发药相关信息
        item.setSend_time(e.getSendTime());
        // 根据发药时间判断发送标志，如果有发药时间则认为已发送
        if (StringUtils.isNotEmpty(e.getSendTime())) {
            item.setSend_flag(1);
        } else {
            item.setSend_flag(0);
        }

        // 正确映射售退单位字段
        if (StringUtils.isNotEmpty(e.getSelRetnUnit())) {
            item.setSelRetnUnit(e.getSelRetnUnit());
        }

        // 设置默认的追溯标志（初始值为0，表示未设置）
        if (item.getTrdn_flag() == null) {
            item.setTrdn_flag(0);
        }

        // 映射HIS转换比例
        if (e.getHisConRatio() != null) {
            item.setHis_con_ratio(e.getHisConRatio().toPlainString());
        } else {
            item.setHis_con_ratio("1"); // 默认转换比例为1
        }

        // 映射HIS剂量单位
        // if (StringUtils.isNotEmpty(e.getHisDosUnit())) {
        //     item.setHisDosUnit(e.getHisDosUnit());
        // }

        // 映射HIS包装单位
        // if (StringUtils.isNotEmpty(e.getHisPacUnit())) {
        //     item.setHisPacUnit(e.getHisPacUnit());
        // }

        // 设置批次号 - 可以使用处方明细序号作为批次号
        if (!StringUtils.isNotEmpty(item.getBchno()) && e.getCfmxxh() != null) {
            item.setBchno(String.valueOf(e.getCfmxxh()));
        }

        // 为缺少的必要字段设置默认值，避免在后续处理中出现空指针异常
        if (!StringUtils.isNotEmpty(item.getRxno()) && e.getCfxh() != null) {
            item.setRxno("RX" + e.getCfxh()); // 使用处方序号生成处方号
        }

        return item;
    }

    /**
     * 实体转住院处方响应项
     */
    private InpatientPrescriptionResponseVo.InpatientPrescriptionItem convertToInpatientPrescriptionItem(ViewZyfycfFy e) {
        InpatientPrescriptionResponseVo.InpatientPrescriptionItem item = new InpatientPrescriptionResponseVo.InpatientPrescriptionItem();

        // 基本药品信息
        item.setMed_list_codg(e.getMedListCodg());
        item.setFixmedins_hilist_id(e.getFixmedinsHilistId());
        item.setFixmedins_hilist_name(e.getFixmedinsHilistName());
        item.setFixmedins_bchno(e.getFixmedinsBchno());

        // 医师/药师信息
        item.setPrsc_dr_certno(e.getPrscDrCertno());
        item.setPrsc_dr_name(e.getPrscDrName());
        item.setPhar_certno(e.getPharCertno());
        item.setPhar_name(e.getPharName());
        item.setPhar_prac_cert_no(e.getPharPracCertNo());

        // 就诊与人员信息
        item.setMdtrt_sn(e.getMdtrtSn());
        item.setPsn_name(e.getPsnName());

        // 生产及有效期信息
        item.setManu_lotnum(e.getManuLotnum());
        item.setManu_date(e.getManuDate());
        item.setExpy_end(e.getExpyEnd());

        // 标志及号信息
        if (e.getRxFlag() != null) {
            item.setRx_flag(Integer.valueOf(e.getRxFlag()));
        }
        if (StringUtils.isNotEmpty(e.getTrdnFlag())) {
            try {
                item.setTrdn_flag(Integer.valueOf(e.getTrdnFlag()));
            } catch (NumberFormatException ignored) {
            }
        }

        item.setRxno(e.getRxno());
        item.setRx_circ_flag(e.getRxCircFlag());
        item.setRtal_docno(e.getRtalDocno());
        item.setStoout_no(e.getStooutNo());
        item.setBchno(e.getBchno());

        // 数量及时间信息
        if (e.getSelRetnCnt() != null) {
            item.setSel_retn_cnt(e.getSelRetnCnt().toPlainString());
            // 对于住院处方，最小售退数量与售退数量相同
            item.setMin_sel_retn_cnt(e.getSelRetnCnt().toPlainString());
        }
        
        // 处理日期时间格式
        if (e.getSelRetnTime() != null) {
            item.setSel_retn_time(e.getSelRetnTime());
        }
        item.setSel_retn_opter_name(e.getSelRetnOpterName());

        // 其它信息
        if (StringUtils.isNotEmpty(e.getMdtrtSetlType())) {
            try {
                item.setMdtrt_setl_type(Integer.valueOf(e.getMdtrtSetlType()));
            } catch (NumberFormatException ignored) {
            }
        }

        item.setSpec(e.getSpec());
        item.setProdentp_name(e.getProdentpName());

        // 处方序号和明细序号
        item.setCfxh(e.getCfxh());
        item.setCfmxxh(e.getCfmxxh());

        // 其他字段
        item.setSjh(e.getSjh());
        item.setPatient_id(e.getPatientId());
        item.setFyyf(e.getFyyf());

        // 设置默认的追溯标志（初始值为0，表示未设置）
        if (item.getTrdn_flag() == null) {
            item.setTrdn_flag(0);
        }

        // 映射HIS转换比例
        if (e.getHisConRatio() != null) {
            item.setHis_con_ratio(e.getHisConRatio().toPlainString());
        } else {
            item.setHis_con_ratio("1"); // 默认转换比例为1
        }

        // 设置批次号 - 可以使用处方明细序号作为批次号
        if (!StringUtils.isNotEmpty(item.getBchno()) && e.getCfmxxh() != null) {
            item.setBchno(e.getCfmxxh());
        }

        // 为缺少的必要字段设置默认值，避免在后续处理中出现空指针异常
        if (!StringUtils.isNotEmpty(item.getRxno()) && e.getRxno() != null) {
            item.setRxno(e.getRxno()); // 使用处方号
        }

        // 设置发送标志 - 对于住院处方，默认设置为0
        item.setSend_flag(0);
        
        // 住院特有字段映射
        item.setRecord_id(e.getRecordId());
        item.setPat_ward_name(e.getPatWardName());
        
        // 设置售退单位
        if (StringUtils.isNotEmpty(e.getSelRetnUnit())) {
            item.setSelRetnUnit(e.getSelRetnUnit());
        }
        
        return item;
    }

    @DS("his")
    public List<Yp1> getDrugDictList() {
        return yp1Service.list();
    }

    /**
     * 同步药品字典
     *
     * @return 同步结果
     */
    @Override
    public ApiResult<String> syncDrugDictionary() {
        log.info("开始同步HIS药品字典到DMH平台");

        try {
            // 获取SaaS用户信息
            AccessTokenReponse userInfo = SaasHttpUtil.getAccessToken("aqshJk001", "aqshJk001%rs7");

            // 1. 分页拉取HIS系统全量药品数据
            List<Yp1> allDrugList = SpringUtils.getAopProxy(this).getDrugDictList();
            if (allDrugList.isEmpty()) {
                log.warn("从HIS系统未获取到任何药品数据");
                return ApiResult.success("未获取到药品数据，同步完成");
            }

            log.info("从HIS系统共获取到{}条药品数据", allDrugList.size());

            // 2. 数据转换：将HIS数据转换为DMH平台要求的格式
            List<HisDrugInfoSaasApiData> convertedDrugList = convertHisDrugsToDmhFormat(allDrugList);
            log.info("成功转换{}条药品数据", convertedDrugList.size());

            // 3. 分批上传到DMH平台
            int batchSize = 200; // 每批200条
            int totalBatches = (int) Math.ceil((double) convertedDrugList.size() / batchSize);
            int uploadedCount = 0;

            for (int i = 0; i < totalBatches; i++) {
                int fromIndex = i * batchSize;
                int toIndex = Math.min((i + 1) * batchSize, convertedDrugList.size());
                List<HisDrugInfoSaasApiData> batchData = convertedDrugList.subList(fromIndex, toIndex);

                // 上传单批数据
                boolean batchSuccess = uploadDrugBatchToDmh(batchData, i + 1, totalBatches, userInfo);
                if (batchSuccess) {
                    uploadedCount += batchData.size();
                    log.info("第{}/{}批药品数据上传成功，当前批次{}条，累计上传{}条", i + 1, totalBatches, batchData.size(), uploadedCount);
                } else {
                    log.error("第{}/{}批药品数据上传失败", i + 1, totalBatches);
                    return ApiResult.error(String.format("第%d批药品数据上传失败，已成功上传%d条", i + 1, uploadedCount));
                }

                // 避免请求过于频繁，添加间隔
                if (i < totalBatches - 1) {
                    try {
                        Thread.sleep(1000); // 1秒间隔
                    } catch (InterruptedException e) {
                        Thread.currentThread().interrupt();
                        log.warn("同步过程中断");
                        break;
                    }
                }
            }

            String successMessage = String.format("药品字典同步完成！共处理%d条数据，成功上传%d条", convertedDrugList.size(), uploadedCount);
            log.info(successMessage);
            return ApiResult.success(successMessage);

        } catch (Exception e) {
            log.error("同步药品字典异常", e);
            return ApiResult.error("同步药品字典失败：" + e.getMessage());
        }
    }

    /**
     * 将HIS药品数据转换为DMH平台要求的格式
     * 严格按照安庆石化药品字典同步映射文档进行字段转换
     *
     * @param hisDrugList HIS药品数据列表
     * @return 转换后的药品数据列表
     */
    private List<HisDrugInfoSaasApiData> convertHisDrugsToDmhFormat(List<Yp1> hisDrugList) {
        log.info("开始转换药品数据格式，共{}条待转换", hisDrugList.size());

        List<HisDrugInfoSaasApiData> convertedList = new ArrayList<>();
        int convertedCount = 0;
        int skippedCount = 0;

        for (Yp1 hisItem : hisDrugList) {
            try {
                HisDrugInfoSaasApiData dmhItem = new HisDrugInfoSaasApiData();

                // 按照映射文档进行字段转换

                // 1. hisDrugId（必填）：映射自 idm
                if (hisItem.getIdm() != null) {
                    dmhItem.setHisDrugId(String.valueOf(hisItem.getIdm()));
                } else {
                    log.warn("药品ID为空，跳过该药品：{}", JSONUtil.toJsonStr(hisItem));
                    skippedCount++;
                    continue;
                }

                // 2. hisDrugName：映射自 ypmc（药品名称）
                dmhItem.setHisDrugName(hisItem.getYpmc());

                // 3. hisDrugCountryCode：映射自 dydm_sgsyb（省工伤医保对应代码）
                dmhItem.setHisDrugCountryCode(hisItem.getDydmSgsyb());

                // 4. hisDrugCountryName：映射文档中未指定具体字段，设为null
                dmhItem.setHisDrugCountryName(null);

                // 5. hisDrugSpec：映射自 ypgg（药品规格）
                dmhItem.setHisDrugSpec(hisItem.getYpgg());

                // 6. hisPac：映射自 ykxs（药库系数），需转换为字符串
                if (hisItem.getYkxs() != null) {
                    dmhItem.setHisPac(hisItem.getYkxs().toPlainString());
                } else {
                    dmhItem.setHisPac("1"); // 默认值
                }

                // 7. hisPackUnit：映射自 jhdw（进货单位）
                dmhItem.setHisPackUnit(hisItem.getJhdw());

                // 8. hisDrugManufacturerCode：映射文档中未指定具体字段，使用厂家代码
                dmhItem.setHisDrugManufacturerCode(hisItem.getCjdm());

                // 9. hisDrugManufacturerName：映射自 cjmc（厂家名称）
                dmhItem.setHisDrugManufacturerName(hisItem.getCjmc());

                // 10. hisPurchaseUnit：映射自 jhdw（进货单位）
                dmhItem.setHisPurchaseUnit(hisItem.getJhdw());

                // 11. hisPurchasePrice：映射自 ypfj（药品费价）
                dmhItem.setHisPurchasePrice(hisItem.getYpfj());

                // 12. hisDoseForm：映射文档中未指定具体字段，设为null
                dmhItem.setHisDoseForm(null);

                // 13. hisApprovalNum：映射自 pzwh（批准文号）
                dmhItem.setHisApprovalNum(hisItem.getPzwh());

                // 14. hisDosUnit：映射自 zxdw（最小单位）
                dmhItem.setHisDosUnit(hisItem.getZxdw());

                // 15. hisPacUnit：映射自 jhdw（进货单位）
                dmhItem.setHisPacUnit(hisItem.getJhdw());

                // 16. hisConRatio：映射自 ykxs（药库系数）
                if (hisItem.getYkxs() != null) {
                    dmhItem.setHisConRatio(hisItem.getYkxs());
                } else {
                    dmhItem.setHisConRatio(BigDecimal.ONE); // 默认转换比为1
                }

                // 17. delFlag（必填）：设置固定值0（数据有效）
                dmhItem.setDelFlag(0);

                // 其他可选字段设置为null
                dmhItem.setHisEnterpriseCode(null);
                dmhItem.setHisEnterpriseName(null);
                dmhItem.setWholeQuantity(null);
                dmhItem.setHisDiscRate(null);
                dmhItem.setMemo(null);

                convertedList.add(dmhItem);
                convertedCount++;

                // 每1000条记录输出一次进度日志
                if (convertedCount % 1000 == 0) {
                    log.info("药品数据转换进度：已转换{}条", convertedCount);
                }

            } catch (Exception e) {
                log.error("转换药品数据异常，跳过该药品：idm={}, ypmc={}, 错误信息：{}", hisItem.getIdm(), hisItem.getYpmc(), e.getMessage());
                skippedCount++;

                // 如果连续跳过太多记录，记录警告
                if (skippedCount > 0 && skippedCount % 100 == 0) {
                    log.warn("已连续跳过{}条记录，请检查数据质量", skippedCount);
                }
            }
        }

        log.info("药品数据转换完成！总计处理：{}条，成功转换：{}条，跳过：{}条", hisDrugList.size(), convertedCount, skippedCount);

        // 如果跳过的记录过多，输出警告
        if (skippedCount > convertedCount * 0.1) {
            log.warn("跳过记录数量较多({})，占总数的{:.2f}%，建议检查源数据质量", skippedCount, (double) skippedCount / hisDrugList.size() * 100);
        }

        return convertedList;
    }

    /**
     * 上传单批药品数据到DMH平台
     *
     * @param batchData    批次数据
     * @param batchIndex   批次索引
     * @param totalBatches 总批次数
     * @param userInfo     用户信息
     * @return 上传是否成功
     */
    private boolean uploadDrugBatchToDmh(List<HisDrugInfoSaasApiData> batchData, int batchIndex, int totalBatches, AccessTokenReponse userInfo) {
        try {
            // 构建请求对象
            HisDrugInfoSaasRequest request = new HisDrugInfoSaasRequest();
            request.setMedicalCode(NhsaAccountConstant.getNhsaAccount().getMedicalCode());
            request.setMedicalName(NhsaAccountConstant.getNhsaAccount().getMedicalName());
            request.setDataList(batchData);

            // 生成唯一的requestID
            String timestamp = new SimpleDateFormat("yyyyMMddHHmmss").format(new Date());
            String requestID = String.format("DRUG-SYNC-%s-B%03d", timestamp, batchIndex);
            request.setRequestID(requestID);

            log.info("开始上传第{}/{}批药品数据到DMH平台，批次大小：{}，requestID：{}", batchIndex, totalBatches, batchData.size(), requestID);

            // 调用SaaS接口上传
            SaasCommmonListResponse<UploadHisDrugInfoResponse> response = SaasHttpUtil.uploadHisDrugInfo(userInfo.getAuthorization(), request);

            if (Objects.equals(response.getReturnCode(), 0)) {
                log.info("第{}/{}批药品数据上传成功，requestID：{}，响应：{}", batchIndex, totalBatches, requestID, JSONUtil.toJsonStr(response));
                return true;
            } else {
                log.error("第{}/{}批药品数据上传失败，requestID：{}，响应：{}", batchIndex, totalBatches, requestID, JSONUtil.toJsonStr(response));
                return false;
            }

        } catch (Exception e) {
            log.error("第{}/{}批药品数据上传异常", batchIndex, totalBatches, e);
            return false;
        }
    }
}
