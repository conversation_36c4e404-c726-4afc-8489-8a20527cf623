package com.zsm.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;

/**
 * 霍邱1院 出院汇总信息实体类
 * 
 * <AUTHOR>
 * @since 2025-06-10
 */
@Data
@TableName("VIEW_CYHZXX")
@Schema(description = "出院汇总信息")
public class ViewCyhzxx implements Serializable {

    private static final long serialVersionUID = 1L;


    /**
     * 患者住院ID
     */
    @Schema(description = "患者住院ID")
    @TableField("pat_in_hos_id")
    private String patInHosId;

    /**
     * 患者ID
     */
    @Schema(description = "患者ID")
    @TableField("patient_id")
    private Integer patientId;

    /**
     * 患者姓名
     */
    @Schema(description = "患者姓名")
    @TableField("patient_name")
    private String patientName;

    /**
     * 销售/退货时间
     */
    @Schema(description = "销售/退货时间")
    @TableField("sel_time")
    private String selTime;
} 
