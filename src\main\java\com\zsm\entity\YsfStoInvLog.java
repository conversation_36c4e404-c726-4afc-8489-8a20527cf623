package com.zsm.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;

/**
 * 仓储库存记录变动记录实体类
 * 
 * <AUTHOR>
 * @since 2025-06-02
 */
@Data
@TableName("ysf_sto_inv_log")
@Schema(description = "仓储库存记录变动记录")
public class YsfStoInvLog implements Serializable {

    private static final long serialVersionUID = 1L;


    /**
     * 库存变动记录主键
     */
    @Schema(description = "库存变动记录主键")
    @TableId(value = "id_inv_log", type = IdType.AUTO)
    private Long idInvLog;

    /**
     * 库存主键
     */
    @Schema(description = "库存主键")
    @Size(max = 100, message = "库存主键长度不能超过100个字符")
    @TableField("id_sto_inv")
    private String idStoInv;

    /**
     * 商品编码
     */
    @Schema(description = "商品编码")
    @Size(max = 100, message = "商品编码长度不能超过100个字符")
    @TableField("drug_code")
    private String drugCode;

    /**
     * 变动原因
     */
    @Schema(description = "变动原因")
    @Size(max = 100, message = "变动原因长度不能超过100个字符")
    @TableField("sd_amt_change")
    private String sdAmtChange;

    /**
     * 原因情况
     */
    @Schema(description = "原因情况")
    @Size(max = 100, message = "原因情况长度不能超过100个字符")
    @TableField("des_reason")
    private String desReason;

    /**
     * 原始业务主键
     */
    @Schema(description = "原始业务主键")
    @Size(max = 100, message = "原始业务主键长度不能超过100个字符")
    @TableField("id_biz_ori")
    private String idBizOri;

    /**
     * 变动数量
     */
    @Schema(description = "变动数量")
    @TableField("amt_change")
    private Integer amtChange;

    /**
     * 变动前数量
     */
    @Schema(description = "变动前数量")
    @TableField("amt_before")
    private Integer amtBefore;

    /**
     * 变动后数量
     */
    @Schema(description = "变动后数量")
    @TableField("amt_after")
    private Integer amtAfter;

    /**
     * 库存价格;变动时的价格,避免调价后导致价格变化
     */
    @Schema(description = "库存价格;变动时的价格,避免调价后导致价格变化")
    @TableField("price_sale")
    private BigDecimal priceSale;

    /**
     * 进货价格
     */
    @Schema(description = "进货价格")
    @TableField("price_pur")
    private BigDecimal pricePur;

    /**
     * 包装单位
     */
    @Schema(description = "包装单位")
    @Size(max = 100, message = "包装单位长度不能超过100个字符")
    @TableField("unit_sale")
    private String unitSale;

    /**
     * 转换系数
     */
    @Schema(description = "转换系数")
    @TableField("unit_sale_factor")
    private Integer unitSaleFactor;

    /**
     * 库房id
     */
    @Schema(description = "库房id")
    @Size(max = 100, message = "库房id长度不能超过100个字符")
    @TableField("id_sto")
    private String idSto;

    /**
     * 机构编号
     */
    @Schema(description = "机构编号")
    @Size(max = 100, message = "机构编号长度不能超过100个字符")
    @TableField("id_org")
    private String idOrg;

    /**
     * 医疗机构编码
     */
    @Schema(description = "医疗机构编码")
    @Size(max = 100, message = "医疗机构编码长度不能超过100个字符")
    @TableField("org_id")
    private String orgId;

    /**
     * 医疗机构名称
     */
    @Schema(description = "医疗机构名称")
    @NotBlank(message = "医疗机构名称不能为空")
    @Size(max = 30, message = "医疗机构名称长度不能超过30个字符")
    @TableField("org_name")
    private String orgName;

    /**
     * 乐观锁
     */
    @Schema(description = "乐观锁")
    @Size(max = 100, message = "乐观锁长度不能超过100个字符")
    @TableField("revision")
    private String revision;

    /**
     * 创建者
     */
    @Schema(description = "创建者")
    @Size(max = 100, message = "创建者长度不能超过100个字符")
    @TableField("create_by")
    private String createBy;

    /**
     * 创建时间
     */
    @Schema(description = "创建时间")
    @TableField("create_time")
    private LocalDateTime createTime;

    /**
     * 更新者
     */
    @Schema(description = "更新者")
    @Size(max = 100, message = "更新者长度不能超过100个字符")
    @TableField("update_by")
    private String updateBy;

    /**
     * 更新时间
     */
    @Schema(description = "更新时间")
    @TableField("update_time")
    private LocalDateTime updateTime;

    /**
     * 备注
     */
    @Schema(description = "备注")
    @Size(max = 500, message = "备注长度不能超过500个字符")
    @TableField("remark")
    private String remark;

    /**
     * 删除标志（0代表存在 1代表删除）
     */
    @Schema(description = "删除标志（0代表存在 1代表删除）")
    @Size(max = 100, message = "删除标志（0代表存在 1代表删除）长度不能超过100个字符")
    @TableField("del_flag")
    private String delFlag;
} 
