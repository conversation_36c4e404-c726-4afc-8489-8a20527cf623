package com.zsm.task;

import com.zsm.his.DispensingService;
import com.zsm.his.impl.AnQingShiHuaService;
import com.zsm.service.Nhsa3505Service;
import com.zsm.superset.TraceabilityService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Scheduled;

import jakarta.annotation.Resource;

/**
 * 安庆石化定时任务类
 *
 * <AUTHOR>
 * @date 2025/5/29 下午10:45
 */
@Deprecated
// @Component
@Slf4j
public class AnQingShiHuaTask {

    @Resource
    private Nhsa3505Service nhsa3505Service;
    @Resource
    private TraceabilityService traceabilityService;
    @Resource
    private AnQingShiHuaService anQingShiHuaService;
    @Resource
    private DispensingService dispensingService;

    /**
     * 同步发药时间，每5分钟执行一次
     */
    @Scheduled(cron = "0 0/5 * * * ?")
    public void syncDispenseTimeTask() {
        log.info("ShuChengTask：开始同步发药时间");
        try {
            // 调用Service实现发药时间同步逻辑
            dispensingService.syncAnQingShiHuaDispenseTime();
            log.info("ShuChengTask：发药时间同步完成");
        } catch (Exception e) {
            log.info("ShuChengTask：发药时间同步失败", e);
            e.printStackTrace();
        }
    }

    /**
     * 上传3505数据到最终平台,每3小时执行一次
     */
    @Scheduled(cron = "0 0 */3 * * ?")
    public void uploadDataToPlatformTask() {
        try {
            log.info("执行定时任务-上传3505数据到最终平台");
            nhsa3505Service.uploadDataToPlatform();
            log.info("定时任务执行完成-上传3505数据到最终平台");
        } catch (Exception e) {
            log.error("上传3505数据到最终平台失败", e);
            e.printStackTrace();
        }
    }

    /**
     * 生成追溯码数据，上传superSet平台，每2小时执行一次
     */
    @Scheduled(cron = "0 0 */2 * * ?")
    public void uploadTraceabilityDataToSuperSetTask() {
        try {
            log.info("执行定时任务-生成追溯码数据并上传superSet平台");
            // 调用Service实现追溯码数据生成和上传逻辑
            traceabilityService.generateAndUploadToSuperSet(null,null);
            log.info("定时任务执行完成-生成追溯码数据并上传superSet平台");
        } catch (Exception e) {
            log.error("生成追溯码数据并上传superSet平台失败", e);
            e.printStackTrace();
        }
    }

    /**
     * 同步HIS药品字典，每天下午1点执行一次
     */
    @Scheduled(cron = "0 20 13 * * ?")
    public void syncHisDrugDictionaryTask() {
        try {
            log.info("执行定时任务-同步HIS药品字典");
            anQingShiHuaService.syncDrugDictionary();
            log.info("定时任务执行完成-同步HIS药品字典");
        } catch (Exception e) {
            log.error("同步HIS药品字典失败", e);
            e.printStackTrace();
        }
    }
}
