<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zsm.mapper.ViewYsfZyfycfMapper">

    <!-- 结果映射：将查询结果映射到 InpatientSettlementVo -->
    <resultMap id="InpatientSettlementVoMap" type="com.zsm.model.vo.InpatientSettlementVo">
        <result column="patient_id" property="patientId"/>
        <result column="psn_name" property="patientName"/>
        <result column="mdtrt_sn" property="mdtrtSn"/>
        <result column="psn_no" property="psnNo"/>
    </resultMap>

    <!-- 查询去重后的住院患者列表 -->
    <select id="selectDistinctInpatients" resultMap="InpatientSettlementVoMap">
        SELECT DISTINCT 
            patient_id,
            psn_name,
            mdtrt_sn,
            psn_no
        FROM VIEW_YSF_ZYFYCF
        WHERE 1=1
            AND mdtrt_setl_type = '1'
            <if test="startDateTime != null and startDateTime != ''">
                AND sel_retn_time &gt;= #{startDateTime}
            </if>
            <if test="endDateTime != null and endDateTime != ''">
                AND sel_retn_time &lt;= #{endDateTime}
            </if>
            AND rxno IS NOT NULL
            AND rxno != ''
            AND patient_id IS NOT NULL
            AND patient_id != ''
        ORDER BY patient_id
    </select>

</mapper>