package com.zsm.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.math.BigDecimal;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 阜阳肿瘤药品字典视图实体类
 * 
 * <AUTHOR>
 * @since 2025-07-01
 */
@Data
@TableName("VIEW_YSF_YPZD")
@Schema(description = "阜阳肿瘤药品字典视图")
public class ViewYsfYpzd implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 序号
     */
    @Schema(description = "序号")
    @TableField("序号")
    private Long xuhao;

    /**
     * 规格代码
     */
    @Schema(description = "规格代码")
    @TableField("规格代码")
    private Long guigeDaima;

    /**
     * 临床代码
     */
    @Schema(description = "临床代码")
    @TableField("临床代码")
    private Long linchuangDaima;

    /**
     * 药品名称
     */
    @Schema(description = "药品名称")
    @TableField("药品名称")
    private String yaopinMingcheng;

    /**
     * 药品规格
     */
    @Schema(description = "药品规格")
    @TableField("药品规格")
    private String yaopinGuige;

    /**
     * 剂型
     */
    @Schema(description = "剂型")
    @TableField("剂型")
    private String jixing;

    /**
     * 最小单位
     */
    @Schema(description = "最小单位")
    @TableField("最小单位")
    private String zuixiaoDanwei;

    /**
     * 规格单位
     */
    @Schema(description = "规格单位")
    @TableField("规格单位")
    private String guigeDanwei;

    /**
     * 规格系数
     */
    @Schema(description = "规格系数")
    @TableField("规格系数")
    private BigDecimal guigeXishu;

    /**
     * 厂家代码
     */
    @Schema(description = "厂家代码")
    @TableField("厂家代码")
    private String changjiaDaima;

    /**
     * 厂家名称
     */
    @Schema(description = "厂家名称")
    @TableField("厂家名称")
    private String changjiaMingcheng;

    /**
     * 药库单位
     */
    @Schema(description = "药库单位")
    @TableField("药库单位")
    private String yaokuDanwei;

    /**
     * 药库系数
     */
    @Schema(description = "药库系数")
    @TableField("药库系数")
    private BigDecimal yaokuXishu;

    /**
     * 默认进价
     */
    @Schema(description = "默认进价")
    @TableField("默认进价")
    private String morenJinjia;

    /**
     * 账目类别
     */
    @Schema(description = "账目类别")
    @TableField("账目类别")
    private String zhangmuLeibie;

    /**
     * 供货单位代码
     */
    @Schema(description = "供货单位代码")
    @TableField("供货单位代码")
    private String gonghuoDanweiDaima;

    /**
     * 供货单位名称
     */
    @Schema(description = "供货单位名称")
    @TableField("供货单位名称")
    private String gonghuoDanweiMingcheng;

    /**
     * 批准文号
     */
    @Schema(description = "批准文号")
    @TableField("批准文号")
    private String pizhunWenhao;

    /**
     * 对应代码
     */
    @Schema(description = "对应代码")
    @TableField("对应代码")
    private String duiyingDaima;
}