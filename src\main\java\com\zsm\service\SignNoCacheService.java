package com.zsm.service;

import com.zsm.constant.NhsaAccountConstant;
import com.zsm.model.domain.NhsaAccount;
import com.zsm.utils.NhsaHttpUtil;
import com.zsm.utils.RedisUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * SignNo缓存服务类
 * 负责管理医保签到令牌(signNo)的缓存逻辑
 * 签到令牌每天24点过期，过期后自动获取新的签到令牌
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Slf4j
@Service
public class SignNoCacheService {

    @Autowired
    private RedisUtil redisUtil;

    /**
     * SignNo缓存键前缀
     */
    private static final String SIGN_NO_CACHE_PREFIX = "nhsa:signNo:";

    /**
     * 生成缓存键
     * 根据医保账户的医疗机构编码和操作员编码生成唯一的缓存键
     * 
     * @param nhsaAccount 医保账户信息
     * @return 缓存键
     */
    private String generateCacheKey(NhsaAccount nhsaAccount) {
        if (nhsaAccount == null) {
            return SIGN_NO_CACHE_PREFIX + "default";
        }
        // 使用医疗机构编码和操作员编码作为唯一标识
        return SIGN_NO_CACHE_PREFIX + nhsaAccount.getMedicalCode() + ":" + nhsaAccount.getOperatorNo();
    }

    /**
     * 获取签到令牌
     * 优先从缓存中获取，如果缓存不存在或已过期，则重新获取并缓存
     * 
     * @return 签到令牌
     */
    public String getSignNo() {
        return getSignNo(NhsaAccountConstant.getNhsaAccount());
    }

    /**
     * 获取签到令牌
     * 优先从缓存中获取，如果缓存不存在或已过期，则重新获取并缓存
     * 
     * @param nhsaAccount 医保账户信息
     * @return 签到令牌
     */
    public String getSignNo(NhsaAccount nhsaAccount) {
        try {
            String cacheKey = generateCacheKey(nhsaAccount);
            
            // 尝试从缓存中获取
            String cachedSignNo = redisUtil.get(cacheKey);
            if (cachedSignNo != null) {
                log.debug("从缓存中获取到SignNo, nhsaAccount: {}, signNo: {}", 
                         nhsaAccount.getMedicalCode(), cachedSignNo);
                return cachedSignNo;
            }
            
            // 缓存不存在或已过期，重新获取
            log.info("缓存中没有找到SignNo或已过期，重新获取签到令牌, nhsaAccount: {}", 
                    nhsaAccount.getMedicalCode());

            return fetchAndCacheSignNo(nhsaAccount);
            
        } catch (Exception e) {
            log.error("获取SignNo失败，尝试直接调用接口获取, nhsaAccount: {}", 
                     nhsaAccount.getMedicalCode(), e);
            // 如果缓存操作失败，直接调用接口获取
            return NhsaHttpUtil.getSignNo(nhsaAccount);
        }
    }

    /**
     * 获取新的签到令牌并缓存
     * 
     * @param nhsaAccount 医保账户信息
     * @return 新的签到令牌
     */
    private String fetchAndCacheSignNo(NhsaAccount nhsaAccount) {
        try {
            // 调用接口获取新的签到令牌
            String signNo = NhsaHttpUtil.getSignNo(nhsaAccount);
            
            if (signNo != null && !signNo.trim().isEmpty()) {
                // 缓存签到令牌，设置为当天24点过期
                cacheSignNo(nhsaAccount, signNo);
            }
            
            return signNo;
            
        } catch (Exception e) {
            log.error("获取并缓存新的SignNo失败, nhsaAccount: {}", 
                     nhsaAccount.getMedicalCode(), e);
            throw e;
        }
    }

    /**
     * 缓存签到令牌
     * 设置为当天24点过期
     * 
     * @param nhsaAccount 医保账户信息
     * @param signNo 签到令牌
     */
    public void cacheSignNo(NhsaAccount nhsaAccount, String signNo) {
        try {
            if (signNo == null || signNo.trim().isEmpty()) {
                log.warn("SignNo为空，不进行缓存, nhsaAccount: {}", nhsaAccount.getMedicalCode());
                return;
            }

            String cacheKey = generateCacheKey(nhsaAccount);
            // 设置缓存到当天24点过期
            redisUtil.setUntilMidnight(cacheKey, signNo);
            
            log.info("SignNo已缓存，设置为当天24点过期, nhsaAccount: {}, signNo: {}", 
                    nhsaAccount.getMedicalCode(), signNo);
        } catch (Exception e) {
            log.error("缓存SignNo失败, nhsaAccount: {}", nhsaAccount.getMedicalCode(), e);
        }
    }

    /**
     * 清除指定账户的SignNo缓存
     * 
     * @param nhsaAccount 医保账户信息
     */
    public void clearSignNoCache(NhsaAccount nhsaAccount) {
        try {
            String cacheKey = generateCacheKey(nhsaAccount);
            redisUtil.delete(cacheKey);
            log.info("SignNo缓存已清除, nhsaAccount: {}", nhsaAccount.getMedicalCode());
        } catch (Exception e) {
            log.error("清除SignNo缓存失败, nhsaAccount: {}", nhsaAccount.getMedicalCode(), e);
        }
    }

    /**
     * 清除默认的SignNo缓存
     */
    public void clearSignNoCache() {
        clearSignNoCache(NhsaAccountConstant.getNhsaAccount());
    }

    /**
     * 检查SignNo是否存在于缓存中
     * 
     * @param nhsaAccount 医保账户信息
     * @return true 存在 false 不存在
     */
    public boolean hasSignNoInCache(NhsaAccount nhsaAccount) {
        try {
            String cacheKey = generateCacheKey(nhsaAccount);
            return redisUtil.hasKey(cacheKey);
        } catch (Exception e) {
            log.error("检查SignNo缓存是否存在失败, nhsaAccount: {}", nhsaAccount.getMedicalCode(), e);
            return false;
        }
    }

    /**
     * 获取SignNo缓存的剩余过期时间
     * 
     * @param nhsaAccount 医保账户信息
     * @return 剩余过期时间(秒)，-1表示永不过期，-2表示key不存在
     */
    public long getSignNoCacheExpire(NhsaAccount nhsaAccount) {
        try {
            String cacheKey = generateCacheKey(nhsaAccount);
            return redisUtil.getExpire(cacheKey);
        } catch (Exception e) {
            log.error("获取SignNo缓存过期时间失败, nhsaAccount: {}", nhsaAccount.getMedicalCode(), e);
            return -2;
        }
    }

    /**
     * 强制刷新SignNo缓存
     * 无论缓存是否存在，都重新获取并更新缓存
     * 
     * @param nhsaAccount 医保账户信息
     * @return 新的签到令牌
     */
    public String refreshSignNo(NhsaAccount nhsaAccount) {
        try {
            log.info("强制刷新SignNo缓存, nhsaAccount: {}", nhsaAccount.getMedicalCode());
            
            // 先清除旧的缓存
            clearSignNoCache(nhsaAccount);
            
            // 获取新的签到令牌并缓存
            return fetchAndCacheSignNo(nhsaAccount);
            
        } catch (Exception e) {
            log.error("强制刷新SignNo缓存失败, nhsaAccount: {}", nhsaAccount.getMedicalCode(), e);
            throw e;
        }
    }

    /**
     * 强制刷新默认的SignNo缓存
     * 
     * @return 新的签到令牌
     */
    public String refreshSignNo() {
        return refreshSignNo(NhsaAccountConstant.getNhsaAccount());
    }
}
