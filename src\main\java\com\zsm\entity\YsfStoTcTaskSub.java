package com.zsm.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.time.LocalDate;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;

/**
 * 扫码任务明细表实体类
 * 
 * <AUTHOR>
 * @since 2025-06-02
 */
@Data
@TableName("ysf_sto_tc_task_sub")
@Schema(description = "扫码任务明细表")
public class YsfStoTcTaskSub implements Serializable {

    private static final long serialVersionUID = 1L;


    /**
     * 明细ID
     */
    @Schema(description = "明细ID")
    @TableId(value = "id_sub", type = IdType.AUTO)
    private Long idSub;

    /**
     * 关联任务ID
     */
    @Schema(description = "关联任务ID")
    @Size(max = 100, message = "关联任务ID长度不能超过100个字符")
    @TableField("id_task")
    private String idTask;

    /**
     * 追溯码: 来源于ysf_sto_tc.drugtracinfo
     */
    @Schema(description = "追溯码: 来源于ysf_sto_tc.drugtracinfo")
    @Size(max = 100, message = "追溯码: 来源于ysf_sto_tc.drugtracinfo长度不能超过100个字符")
    @TableField("drugtracinfo")
    private String drugtracinfo;

    /**
     * 商品编码: ysf_sto_inv.drug_code
     */
    @Schema(description = "商品编码: ysf_sto_inv.drug_code")
    @Size(max = 100, message = "商品编码: ysf_sto_inv.drug_code长度不能超过100个字符")
    @TableField("drug_code")
    private String drugCode;

    /**
     * 库存ID: ysf_sto_inv.id_sto_inv
     */
    @Schema(description = "库存ID: ysf_sto_inv.id_sto_inv")
    @Size(max = 100, message = "库存ID: ysf_sto_inv.id_sto_inv长度不能超过100个字符")
    @TableField("id_sto_inv")
    private String idStoInv;

    /**
     * 业务明细ID: 发药单明细(ysf_sto_dps_sub.id)
     */
    @Schema(description = "业务明细ID: 发药单明细(ysf_sto_dps_sub.id)")
    @TableField("id_biz_sub")
    private Long idBizSub;

    /**
     * 原始业务明细编号
     */
    @Schema(description = "原始业务明细编号")
    @Size(max = 100, message = "原始业务明细编号长度不能超过100个字符")
    @TableField("cfmxxh")
    private String cfmxxh;

    /**
     * 预计扫描时间: 如发药单需在指定时间前完成
     */
    @Schema(description = "预计扫描时间: 如发药单需在指定时间前完成")
    @TableField("dt_scan")
    private LocalDate dtScan;

    /**
     * 是否已扫码: 0否,1是
     */
    @Schema(description = "是否已扫码: 0否,1是")
    @Size(max = 100, message = "是否已扫码: 0否,1是长度不能超过100个字符")
    @TableField("fg_scanned")
    private String fgScanned;

    /**
     * 实际扫码人
     */
    @Schema(description = "实际扫码人")
    @Size(max = 100, message = "实际扫码人长度不能超过100个字符")
    @TableField("scan_user")
    private String scanUser;

    /**
     * 扫码时间
     */
    @Schema(description = "扫码时间")
    @TableField("scan_time")
    private LocalDateTime scanTime;

    /**
     * 扫码备注
     */
    @Schema(description = "扫码备注")
    @Size(max = 100, message = "扫码备注长度不能超过100个字符")
    @TableField("scan_remark")
    private String scanRemark;

    /**
     * 机构编号
     */
    @Schema(description = "机构编号")
    @Size(max = 100, message = "机构编号长度不能超过100个字符")
    @TableField("id_org")
    private String idOrg;

    /**
     * 医疗机构编码
     */
    @Schema(description = "医疗机构编码")
    @Size(max = 100, message = "医疗机构编码长度不能超过100个字符")
    @TableField("org_id")
    private String orgId;

    /**
     * 医疗机构名称
     */
    @Schema(description = "医疗机构名称")
    @NotBlank(message = "医疗机构名称不能为空")
    @Size(max = 30, message = "医疗机构名称长度不能超过30个字符")
    @TableField("org_name")
    private String orgName;

    /**
     * 乐观锁
     */
    @Schema(description = "乐观锁")
    @Size(max = 100, message = "乐观锁长度不能超过100个字符")
    @TableField("revision")
    private String revision;

    /**
     * 创建者
     */
    @Schema(description = "创建者")
    @Size(max = 100, message = "创建者长度不能超过100个字符")
    @TableField("create_by")
    private String createBy;

    /**
     * 创建时间
     */
    @Schema(description = "创建时间")
    @TableField("create_time")
    private LocalDateTime createTime;

    /**
     * 更新者
     */
    @Schema(description = "更新者")
    @Size(max = 100, message = "更新者长度不能超过100个字符")
    @TableField("update_by")
    private String updateBy;

    /**
     * 更新时间
     */
    @Schema(description = "更新时间")
    @TableField("update_time")
    private LocalDateTime updateTime;

    /**
     * 备注
     */
    @Schema(description = "备注")
    @Size(max = 500, message = "备注长度不能超过500个字符")
    @TableField("remark")
    private String remark;

    /**
     * 删除标志（0代表存在 1代表删除）
     */
    @Schema(description = "删除标志（0代表存在 1代表删除）")
    @Size(max = 100, message = "删除标志（0代表存在 1代表删除）长度不能超过100个字符")
    @TableField("del_flag")
    private String delFlag;

    /**
     * 药品数量
     */
    @Schema(description = "药品数量")
    @TableField("quantity")
    private Integer quantity;

    /**
     * 药品单位
     */
    @Schema(description = "药品单位")
    @Size(max = 100, message = "药品单位长度不能超过100个字符")
    @TableField("unit")
    private String unit;

    /**
     * 已采集的追溯码数量
     */
    @Schema(description = "已采集的追溯码数量")
    @TableField("trac_cnt")
    private Integer tracCnt;
} 
