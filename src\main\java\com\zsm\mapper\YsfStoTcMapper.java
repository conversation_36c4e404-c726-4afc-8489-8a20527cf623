package com.zsm.mapper;

import com.zsm.entity.YsfStoTc;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.MapKey;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * <p>
 * 商品追溯码 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-02
 */
@Mapper
public interface YsfStoTcMapper extends BaseMapper<YsfStoTc> {

    /**
     * 批量查询追溯码信息
     * 
     * @param traceinfoList 追溯码列表
     * @return 追溯码到追溯码记录的映射，key为追溯码，value为追溯码记录
     */
    @MapKey("drugtracinfo")
    Map<String, YsfStoTc> selectBatchByTraceinfo(@Param("traceinfoList") List<String> traceinfoList);
}
