package com.zsm.constant;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;
import java.util.List;

/**
 * 阜阳肿瘤医院-接口同步账号枚举
 * 用于管理多个药房账号的用户名和密码
 */
@Getter
@AllArgsConstructor
public enum SyncAccountEnum {
    
    // ========== 住院药房账号 ==========
    /**
     * 阜阳肿瘤-静脉配置中心
     */
    FYZL_IV_PREP_CENTER("jmpzzx_api", "jmpzzx_api123", "4501", "静脉配置中心"),
    /**
     * 阜阳肿瘤-静脉配置中心(河滨东路)
     */
    FYZL_IV_PREP_CENTER_HBDL("jmpzzxhbdl_api", "jmpzzxhbdl_api123", "4001", "静脉配置中心(河滨东路)"),
    /**
     * 阜阳肿瘤-住院药房
     */
    FYZL_INPATIENT_PHARMACY("zyyf_api", "zyyf_api123", "4502", "住院药房"),
    /**
     * 阜阳肿瘤-住院药房(河滨东路)
     */
    FYZL_INPATIENT_PHARMACY_HBDL("zyyfhbdl_api", "zyyfhbdl_api123", "4002", "住院药房(河滨东路)"),
    /**
     * 阜阳肿瘤-手术室药房
     */
    FYZL_SURGERY_PHARMACY("sssyf_api", "sssyf_api123", "4505", "手术室药房"),
    /**
     * 阜阳肿瘤-放射治疗中心
     */
    FYZL_RADIATION_THERAPY_CENTER("fszlzx_api", "fszlzx_api123", "5507", "放射治疗中心"),
    /**
     * 阜阳肿瘤-急诊药房
     */
    FYZL_EMERGENCY_PHARMACY("jzyf_api", "jzyf_api123", "4507", "急诊药房");

    
    /**
     * 账号
     */
    private final String username;
    
    /**
     * 密码
     */
    private final String password;
    /**
     * 药房id
     */
    private final String fyyf;
    /**
     * 描述
     */
    private final String description;

    /**
     * 获取所有账号枚举值的列表
     * @return 所有枚举值的列表
     */
    public static List<SyncAccountEnum> getAllAccounts() {
        return Arrays.asList(values());
    }

    /**
     * 遍历所有账号并执行指定操作
     * @param accountProcessor 账号处理器函数式接口
     */
    public static void forEachAccount(AccountProcessor accountProcessor) {
        for (SyncAccountEnum account : values()) {
            accountProcessor.process(account);
        }
    }

    /**
     * 根据用户名查找账号
     * @param username 用户名
     * @return 匹配的账号枚举，如果未找到返回null
     */
    public static SyncAccountEnum findByUsername(String username) {
        for (SyncAccountEnum account : values()) {
            if (account.username.equals(username)) {
                return account;
            }
        }
        return null;
    }

    /**
     * 根据描述查找账号
     * @param description 描述
     * @return 匹配的账号枚举，如果未找到返回null
     */
    public static SyncAccountEnum findByDescription(String description) {
        for (SyncAccountEnum account : values()) {
            if (account.description.equals(description)) {
                return account;
            }
        }
        return null;
    }

    /**
     * 根据发药药房ID(fyyf)查找账号
     * @param fyyf 发药药房ID
     * @return 匹配的账号枚举，如果未找到返回null
     */
    public static SyncAccountEnum findByFyyf(String fyyf) {
        for (SyncAccountEnum account : values()) {
            if (account.fyyf.equals(fyyf)) {
                return account;
            }
        }
        return null;
    }

    /**
     * 账号处理器函数式接口
     */
    @FunctionalInterface
    public interface AccountProcessor {
        /**
         * 处理账号
         * @param account 账号枚举
         */
        void process(SyncAccountEnum account);
    }
} 