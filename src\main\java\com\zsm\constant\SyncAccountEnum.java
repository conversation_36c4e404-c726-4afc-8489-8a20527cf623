package com.zsm.constant;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;
import java.util.List;

/**
 * 太和人医-接口同步账号枚举
 * 用于管理多个药房账号的用户名和密码
 */
@Getter
@AllArgsConstructor
public enum SyncAccountEnum {
    
    /**
     * 安医阜阳-介入中心药房
     */
    AYFY_INTERVENTION_CENTER_PHARMACY("ayfyjrzx_api", "ayfyjrzx_api123","72", "介入中心药房"),
    /**
     * 安医阜阳-内镜中心药房
     */
    AYFY_ENDOSCOPY_CENTER_PHARMACY("ayfynjzx_api", "ayfynjzx_api123","99", "内镜中心药房"),
    /**
     * 安医阜阳-手术药房
     */
    AYFY_SURGERY_PHARMACY("ayfyss_api", "ayfyss_api123","68", "手术药房"),
    /**
     * 安医阜阳-静脉用药调配中心
     */
    AYFY_IV_MEDICATION_PREP_CENTER("ayfyjmyydpzx_api", "ayfyjmyydpzx_api123","69", "静脉用药调配中心"),
    /**
     * 安医阜阳-急诊药房
     */
    AYFY_EMERGENCY_PHARMACY("anfyjz_api", "anfyjz_api123","70", "急诊药房"),
    /**
     * 安医阜阳-ICU药房
     */
    AYFY_ICU_PHARMACY("anfyicu_api", "anfyicu_api123","213", "ICU药房"),
    /**
     * 安医阜阳-放射科药房
     */
    AYFY_RADIOLOGY_PHARMACY("ayfyfsk_api", "ayfyfsk_api123","98", "放射科药房"),
    /**
     * 安医阜阳-核医学科药房
     */
    AYFY_NUCLEAR_MEDICINE_PHARMACY("ayfyhyxk_api", "ayfyhyxk_api123","123", "核医学科药房");

    /**
     * 账号
     */
    private final String username;
    
    /**
     * 密码
     */
    private final String password;
    /**
     * 药房id
     */
    private final String fyyf;
    /**
     * 描述
     */
    private final String description;

    /**
     * 获取所有账号枚举值的列表
     * @return 所有枚举值的列表
     */
    public static List<SyncAccountEnum> getAllAccounts() {
        return Arrays.asList(values());
    }

    /**
     * 遍历所有账号并执行指定操作
     * @param accountProcessor 账号处理器函数式接口
     */
    public static void forEachAccount(AccountProcessor accountProcessor) {
        for (SyncAccountEnum account : values()) {
            accountProcessor.process(account);
        }
    }

    /**
     * 根据用户名查找账号
     * @param username 用户名
     * @return 匹配的账号枚举，如果未找到返回null
     */
    public static SyncAccountEnum findByUsername(String username) {
        for (SyncAccountEnum account : values()) {
            if (account.username.equals(username)) {
                return account;
            }
        }
        return null;
    }

    /**
     * 根据描述查找账号
     * @param description 描述
     * @return 匹配的账号枚举，如果未找到返回null
     */
    public static SyncAccountEnum findByDescription(String description) {
        for (SyncAccountEnum account : values()) {
            if (account.description.equals(description)) {
                return account;
            }
        }
        return null;
    }

    /**
     * 根据发药药房ID(fyyf)查找账号
     * @param fyyf 发药药房ID
     * @return 匹配的账号枚举，如果未找到返回null
     */
    public static SyncAccountEnum findByFyyf(String fyyf) {
        for (SyncAccountEnum account : values()) {
            if (account.fyyf.equals(fyyf)) {
                return account;
            }
        }
        return null;
    }

    /**
     * 账号处理器函数式接口
     */
    @FunctionalInterface
    public interface AccountProcessor {
        /**
         * 处理账号
         * @param account 账号枚举
         */
        void process(SyncAccountEnum account);
    }
} 