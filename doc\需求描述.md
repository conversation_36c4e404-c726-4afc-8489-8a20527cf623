# 门诊和住院发药明细

## 需求

- 新项目`FuYangZhongLiuController`中的门诊和住院发药接口,需要参考`HuoQiuYiYuanController`的`queryOutpatientPrescription`和`queryInpatientPrescription`接口的业务流程,替换对应的实体类,做好数据的映射赋值,不要改变接口的出参和入参的实体对象,要改变请求his的视图实体对象;

- 用的都是相同的一套方案,查询数据库视图获取门诊和住院发药的数据是通过切换数据源,从`his`数据库中查询视图数据,通过入参进行过滤,然后进行数据的映射赋值,转换成接口出参的实体对象;

- 在新的服务类`FuYangZhongLiunService`,并实现两个接口进行重构;

- `queryOutpatientPrescription`接口需要参考`VIEW_YSF_MZFYCF`视图,替换对应的实体类,做好数据的映射赋值;

- `queryInpatientPrescription`接口需要参考`VIEW_YSF_ZYFYCF`视图,替换对应的实体类,做好数据的映射赋值;

- 实现定时任务`FuYangZhongLiuTask`进行定时同步药品字典的数据,参考`syncFuYangZhongLiuDispenseTime`方法;

- 按照下面的json数据结构,创建对应的实体对象,并做好数据的映射赋值,需要满足mybatis-plus的实体对象的规范,注意字段的注释说明和注解版本;

- 字段的注释,可以参考`追溯码接口文档.md`中的字段说明;

## 视图结构

- 门诊发药视图：VIEW_YSF_MZFYCF
- 药品字典: VIEW_YSF_YPCDMLK
- 药品字典: VIEW_YSF_YPZD
- 住院发药视图: VIEW_YSF_ZYFYCF
- 出院患者信息：VIEW_YSF_CYHZXX
- 科室字典: VIEW_YSF_KSXX

```
{
"VIEW_YSF_CYHZXX": [
	{
		"pat_in_hos_id" : "25006319",
		"patient_id" : 68567,
		"patient_name" : "戴劲劲",
		"sel_time" : "2025-06-21 08:33:47"
	}
]}


{
"VIEW_YSF_MZFYCF": [
	{
	"patid": "101326",                    // 患者ID
	"spec": "1mg*10支/盒",               // 规格
	"prodentp_name": "遂成药业股份有限公司", // 生产企业名称
	"med_list_codg": "XC01CAS071B002010103204", // 医疗目录编码
	"fixmedins_hilist_id": "574",        // 定点医药机构目录编号
	"fixmedins_hilist_name": "盐酸肾上腺素注射液", // 定点医药机构目录名称
	"cfxh": 819038,                      // 处方序号
	"cfmxxh": 1351928,                   // 处方明细序号
	"fixmedins_bchno": "YFKC450312946083796800", // 定点医药机构批次流水号
	"prsc_dr_name": "卢成成",            // 开方医师姓名
	"phar_name": "胡忠芝",               // 药师姓名
	"phar_prac_cert_no": "34210119790901138x", // 药师执业资格证号
	"rx_flag": 0,                        // 处方药标志
	"trdn_flag": "1",                    // 拆零标志
	"rtal_docno": 3796800,               // 零售单据号
	"sel_retn_cnt": 0.1000,              // 销售/退货数量
	"sel_retn_time": "2025-06-01 10:00:32", // 销售/退货时间
	"sel_retn_opter_name": "胡忠芝",     // 销售/退货经办人姓名
	"MDTRT_SETL_TYPE": "2",              // 就诊结算类型
	"manu_lotnum": "62410271",           // 生产批号
	"manu_date": "    -  -   00:00:00",  // 生产日期
	"expy_end": "2026-09-30 00:00:00",   // 有效期止
	"mdtrt_sn": "34122025060184570185",  // 就医流水号
	"sjh": "2025060135220063                ", // 收据号
	"psn_name": "郝学法",                // 人员姓名
	"hjxh": "819038",                    // 汇总序号(?)
	"send_time": "2025-06-01 10:05:52",  // 发药时间
	"fyyf": "4503        ",              // 发药药房ID
	"fyyfName": "西药房（门诊）",        // 发药药房名称
	"windows": "10"                      // 窗口号
	}
]}


{
"VIEW_YSF_YPCDMLK": [
	{
		"idm" : 1,
		"gg_idm" : 1,
		"lc_idm" : 1,
		"yplh" : "0001",
		"ypmc" : "注射用阿莫西林钠克拉维酸钾",
		"ypdm" : "XY00001",
		"py" : "zsyamxln                                                        ",
		"wb" : "itebassq                                                        ",
		"ypgg" : "1.2g*1瓶\/瓶",
		"jxdm" : "32  ",
		"fldm" : "010101  ",
		"zxdw" : "瓶                  ",
		"ggdw" : "g                   ",
		"ggxs" : 1.2000,
		"cjdm" : "00085   ",
		"cjmc" : "华北制药",
		"yxq" : 24,
		"ykdw" : "瓶                  ",
		"ykxs" : 1.0000,
		"jhdw" : "瓶                  ",
		"jhxs" : 1.0000,
		"mzdw" : "瓶                  ",
		"mzxs" : 1.0000,
		"zydw" : "瓶                  ",
		"zyxs" : 1.0000,
		"ylsj" : "48.7500",
		"ypfj" : "48.7500",
		"mrjj" : "48.7500",
		"pzwh" : "国药准字H10910017",
		"dydm_sgsyb" : "XJ01CRA042B001030102690",
		"dydm_ahsgsyb" : "XJ01CRA042B001030102690"
	}
]}

{
"VIEW_YSF_YPZD": [
	{
		"序号" : 6,
		"规格代码" : 6,
		"临床代码" : 4,
		"药品名称" : "注射用哌拉西林钠他唑巴坦钠",
		"药品规格" : "4.5g*1支\/支",
		"剂型" : "粉针剂",
		"最小单位" : "支                  ",
		"规格单位" : "g                   ",
		"规格系数" : 4.5000,
		"厂家代码" : "00695   ",
		"厂家名称" : "海南通用康力",
		"药库单位" : "支                  ",
		"药库系数" : 1.0000,
		"默认进价" : "45.0000",
		"账目类别" : "西药",
		"供货单位代码" : "",
		"供货单位名称" : "",
		"批准文号" : "国药准字H20103060",
		"对应代码" : "XJ01CRP018B001040179252"
	}
]}


{
"VIEW_YSF_ZYFYCF": [
	{
	"code": "0",                         // 返回编号
	"message": "成功",                   // 返回信息
	"med_list_codg": "XC01CAD162B002010181161", // 医疗目录编码
	"fixmedins_hilist_id": 1959,         // 定点医药机构目录编号
	"fixmedins_hilist_name": "盐酸多巴胺注射液", // 定点医药机构目录名称
	"fixmedins_bchno": "2615342-3518264", // 定点医药机构批次流水号
	"prsc_dr_certno": "1652        ",    // 开方医师证件号码
	"prsc_dr_name": "郑其彬",            // 开方医师姓名
	"phar_certno": "",                   // 药师证件号码（空值）
	"phar_name": "",                     // 药师姓名（空值）
	"phar_prac_cert_no": "",             // 药师执业资格证号（空值）
	"mdtrt_sn": 55233793,               // 就医流水号
	"psn_name": "王怀勋",                // 人员姓名
	"manu_lotnum": "",                   // 生产批号（空值）
	"manu_date": "",                     // 生产日期（空值）
	"expy_end": "",                      // 有效期止（空值）
	"rx_flag": "1",                      // 处方药标志
	"trdn_flag": "",                     // 拆零标志（空值）
	"rxno": 2615342,                     // 处方号
	"rx_circ_flag": "",                  // 外购处方标志（空值）
	"rtal_docno": "",                    // 零售单据号（空值）
	"stoout_no": "",                     // 销售出库单据号（空值）
	"bchno": "",                         // 批次号（空值）
	"sel_retn_cnt": 1.00,                // 销售/退货数量
	"sel_retn_time": "2025-06-01 00:19:26", // 销售/退货时间
	"sel_retn_opter_name": "3116        ", // 销售/退货经办人姓名
	"mdtrt_setl_type": "1",              // 就诊结算类型
	"spec": "5ml:200mg/支",              // 规格
	"prodentp_name": "扬州中宝药业股份有限公司", // 生产企业名称
	"cfxh": 2615342,                     // 处方序号
	"cfmxxh": 3518264,                   // 处方明细序号
	"sjh": 65180,                        // 收据号
	"patient_id": "24023820",            // 患者ID
	"fyyf": "4501        ",              // 发药药房
	"his_con_ratio": 1.0000,             // 最小单位转换比
	"cydybz": 0                          // 额外字段（接口文档中没有）
	}
]}
```


1.InpatientTraceabilityServiceImpl.processWeeklyInpatientTraceability方法参考HangChuangService.processWeeklyInpatientTraceability方法的业务流程,可以先完全照抄过来;

2.我需要在InpatientTraceabilityServiceImpl中重新实现一遍业务功能,获取数据的来源方式会有些许区别,我需要从`VIEW_YSF_ZYFYCF`视图中查询今日有发药数据的患者信息,然后去重,只保留医保就诊类型的患者数据,重点是获取`mdtrt_sn`和`psn_np`这两个参数

3.`mdtrt_sn`和`psn_np`去查询5204接口的数据

4.然后在通过`患者的住院号`查询匹配5204所有的数据

5.按照HangChuangService.processWeeklyInpatientTraceability中的业务流程实现相同的从saas接口获取拆零追溯码信息,增量和更新nhsa_3505表,最后上传追溯码数据
