package com.zsm.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;

/**
 * 药品字典表实体类
 * 
 * <AUTHOR>
 * @since 2025-06-02
 */
@Data
@TableName("his_drug_dict")
@Schema(description = "药品字典表")
public class HisDrugDict implements Serializable {

    private static final long serialVersionUID = 1L;


    /**
     * 主键ID
     */
    @Schema(description = "主键ID")
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 医疗机构编码
     */
    @Schema(description = "医疗机构编码")
    @Size(max = 100, message = "医疗机构编码长度不能超过100个字符")
    @TableField("medical_code")
    private String medicalCode;

    /**
     * 医疗机构
     */
    @Schema(description = "医疗机构")
    @NotBlank(message = "医疗机构不能为空")
    @Size(max = 30, message = "医疗机构长度不能超过30个字符")
    @TableField("medical_name")
    private String medicalName;

    /**
     * 
     */
    @Schema(description = "")
    @Size(max = 100, message = "长度不能超过100个字符")
    @TableField("drug_id")
    private String drugId;

    /**
     * 
     */
    @Schema(description = "")
    @Size(max = 100, message = "长度不能超过100个字符")
    @TableField("drug_spec_packing_id")
    private String drugSpecPackingId;

    /**
     * 
     */
    @Schema(description = "")
    @NotBlank(message = "不能为空")
    @Size(max = 30, message = "长度不能超过30个字符")
    @TableField("drug_name")
    private String drugName;

    /**
     * 
     */
    @Schema(description = "")
    @Size(max = 100, message = "长度不能超过100个字符")
    @TableField("spec")
    private String spec;

    /**
     * 
     */
    @Schema(description = "")
    @Size(max = 100, message = "长度不能超过100个字符")
    @TableField("min_packing_id")
    private String minPackingId;

    /**
     * 
     */
    @Schema(description = "")
    @NotBlank(message = "不能为空")
    @Size(max = 30, message = "长度不能超过30个字符")
    @TableField("min_packing_name")
    private String minPackingName;

    /**
     * 
     */
    @Schema(description = "")
    @Size(max = 100, message = "长度不能超过100个字符")
    @TableField("max_packing_id")
    private String maxPackingId;

    /**
     * 
     */
    @Schema(description = "")
    @NotBlank(message = "不能为空")
    @Size(max = 30, message = "长度不能超过30个字符")
    @TableField("max_packing_name")
    private String maxPackingName;

    /**
     * 
     */
    @Schema(description = "")
    @Size(max = 100, message = "长度不能超过100个字符")
    @TableField("min_dose")
    private String minDose;

    /**
     * 
     */
    @Schema(description = "")
    @Size(max = 100, message = "长度不能超过100个字符")
    @TableField("dose_unit_id")
    private String doseUnitId;

    /**
     * 
     */
    @Schema(description = "")
    @NotBlank(message = "不能为空")
    @Size(max = 30, message = "长度不能超过30个字符")
    @TableField("dose_unit_name")
    private String doseUnitName;

    /**
     * 
     */
    @Schema(description = "")
    @Size(max = 100, message = "长度不能超过100个字符")
    @TableField("dosage_form_id")
    private String dosageFormId;

    /**
     * 
     */
    @Schema(description = "")
    @NotBlank(message = "不能为空")
    @Size(max = 30, message = "长度不能超过30个字符")
    @TableField("dosage_form_name")
    private String dosageFormName;

    /**
     * 
     */
    @Schema(description = "")
    @Size(max = 100, message = "长度不能超过100个字符")
    @TableField("input_code")
    private String inputCode;

    /**
     * 
     */
    @Schema(description = "")
    @Size(max = 100, message = "长度不能超过100个字符")
    @TableField("full_code")
    private String fullCode;

    /**
     * 
     */
    @Schema(description = "")
    @Size(max = 100, message = "长度不能超过100个字符")
    @TableField("approved_no")
    private String approvedNo;

    /**
     * 
     */
    @Schema(description = "")
    @Size(max = 100, message = "长度不能超过100个字符")
    @TableField("produce_id")
    private String produceId;

    /**
     * 
     */
    @Schema(description = "")
    @NotBlank(message = "不能为空")
    @Size(max = 30, message = "长度不能超过30个字符")
    @TableField("produce_name")
    private String produceName;

    /**
     * 
     */
    @Schema(description = "")
    @Size(max = 100, message = "长度不能超过100个字符")
    @TableField("drug_catagory_id")
    private String drugCatagoryId;

    /**
     * 
     */
    @Schema(description = "")
    @NotBlank(message = "不能为空")
    @Size(max = 30, message = "长度不能超过30个字符")
    @TableField("drug_catagory_name")
    private String drugCatagoryName;

    /**
     * 
     */
    @Schema(description = "")
    @Size(max = 100, message = "长度不能超过100个字符")
    @TableField("national_medical_insurance_code")
    private String nationalMedicalInsuranceCode;

    /**
     * 
     */
    @Schema(description = "")
    @Size(max = 100, message = "长度不能超过100个字符")
    @TableField("min_dose_count")
    private String minDoseCount;

    /**
     * 
     */
    @Schema(description = "")
    @NotBlank(message = "不能为空")
    @Size(max = 30, message = "长度不能超过30个字符")
    @TableField("type_name")
    private String typeName;

    /**
     * 
     */
    @Schema(description = "")
    @Size(max = 100, message = "长度不能超过100个字符")
    @TableField("drug_code")
    private String drugCode;

    /**
     * 
     */
    @Schema(description = "")
    @Size(max = 100, message = "长度不能超过100个字符")
    @TableField("attention_points")
    private String attentionPoints;

    /**
     * 
     */
    @Schema(description = "")
    @Size(max = 100, message = "长度不能超过100个字符")
    @TableField("indication")
    private String indication;

    /**
     * 
     */
    @Schema(description = "")
    @Size(max = 100, message = "长度不能超过100个字符")
    @TableField("retail_price")
    private String retailPrice;

    /**
     * 
     */
    @Schema(description = "")
    @Size(max = 100, message = "长度不能超过100个字符")
    @TableField("buy_price")
    private String buyPrice;

    /**
     * 
     */
    @Schema(description = "")
    @Size(max = 100, message = "长度不能超过100个字符")
    @TableField("invalid_flag")
    private String invalidFlag;

    /**
     * 
     */
    @Schema(description = "")
    @Size(max = 100, message = "长度不能超过100个字符")
    @TableField("memo")
    private String memo;

    /**
     * 
     */
    @Schema(description = "")
    @Size(max = 100, message = "长度不能超过100个字符")
    @TableField("winning_bid_code")
    private String winningBidCode;

    /**
     * 
     */
    @Schema(description = "")
    @Size(max = 100, message = "长度不能超过100个字符")
    @TableField("license_number")
    private String licenseNumber;

    /**
     * 
     */
    @Schema(description = "")
    @Size(max = 100, message = "长度不能超过100个字符")
    @TableField("model_type")
    private String modelType;

    /**
     * 
     */
    @Schema(description = "")
    @Size(max = 100, message = "长度不能超过100个字符")
    @TableField("purchase_attribute_id")
    private String purchaseAttributeId;

    /**
     * 
     */
    @Schema(description = "")
    @NotBlank(message = "不能为空")
    @Size(max = 30, message = "长度不能超过30个字符")
    @TableField("purchase_attribute_name")
    private String purchaseAttributeName;

    /**
     * 
     */
    @Schema(description = "")
    @Size(max = 100, message = "长度不能超过100个字符")
    @TableField("material_monitor_flag")
    private String materialMonitorFlag;

    /**
     * 
     */
    @Schema(description = "")
    @Size(max = 100, message = "长度不能超过100个字符")
    @TableField("hospital_id")
    private String hospitalId;

    /**
     * 
     */
    @Schema(description = "")
    @Size(max = 100, message = "长度不能超过100个字符")
    @TableField("org_id")
    private String orgId;

    /**
     * 创建者
     */
    @Schema(description = "创建者")
    @Size(max = 100, message = "创建者长度不能超过100个字符")
    @TableField("create_by")
    private String createBy;

    /**
     * 创建时间
     */
    @Schema(description = "创建时间")
    @TableField("create_time")
    private LocalDateTime createTime;

    /**
     * 更新者
     */
    @Schema(description = "更新者")
    @Size(max = 100, message = "更新者长度不能超过100个字符")
    @TableField("update_by")
    private String updateBy;

    /**
     * 更新时间
     */
    @Schema(description = "更新时间")
    @TableField("update_time")
    private LocalDateTime updateTime;

    /**
     * 备注
     */
    @Schema(description = "备注")
    @Size(max = 500, message = "备注长度不能超过500个字符")
    @TableField("remark")
    private String remark;

    /**
     * 删除标志（0代表正常,1代表删除）
     */
    @Schema(description = "删除标志（0代表正常,1代表删除）")
    @Size(max = 100, message = "删除标志（0代表正常,1代表删除）长度不能超过100个字符")
    @TableField("delete_flag")
    private String deleteFlag;
} 
