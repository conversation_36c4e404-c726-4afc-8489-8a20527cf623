package com.zsm.service.impl;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.zsm.entity.ViewDeptInfo;
import com.zsm.mapper.ViewDeptInfoMapper;
import com.zsm.service.ViewDeptInfoService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

/**
 * <p>
 *  服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-10
 */
@DS("his")
@Service
public class ViewDeptInfoServiceImpl extends ServiceImpl<ViewDeptInfoMapper, ViewDeptInfo> implements ViewDeptInfoService {

}
