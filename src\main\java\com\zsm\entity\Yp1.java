package com.zsm.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import jakarta.validation.constraints.NotBlank;

/**
 * 霍邱1院 药品信息实体类
 * 
 * <AUTHOR>
 * @since 2025-06-10
 */
@Data
@TableName("yp1")
@Schema(description = "药品信息")
public class Yp1 implements Serializable {

    private static final long serialVersionUID = 1L;


    /**
     * 药品ID
     */
    @Schema(description = "药品ID")
    @TableField("idm")
    private Integer idm;

    /**
     * 规格ID
     */
    @Schema(description = "规格ID")
    @TableField("gg_idm")
    private Integer ggIdm;

    /**
     * 厂家ID
     */
    @Schema(description = "厂家ID")
    @TableField("lc_idm")
    private Integer lcIdm;

    /**
     * 药品流水号
     */
    @Schema(description = "药品流水号")
    @TableField("yplh")
    private String yplh;

    /**
     * 药品名称
     */
    @Schema(description = "药品名称")
    @TableField("ypmc")
    private String ypmc;

    /**
     * 药品代码
     */
    @Schema(description = "药品代码")
    @TableField("ypdm")
    private String ypdm;

    /**
     * 药品代码1
     */
    @Schema(description = "药品代码1")
    @TableField("ypdm1")
    private String ypdm1;

    /**
     * 药品代码2
     */
    @Schema(description = "药品代码2")
    @TableField("ypdm2")
    private String ypdm2;

    /**
     * 拼音码
     */
    @Schema(description = "拼音码")
    @TableField("py")
    private String py;

    /**
     * 五笔码
     */
    @Schema(description = "五笔码")
    @TableField("wb")
    private String wb;

    /**
     * 药品规格
     */
    @Schema(description = "药品规格")
    @TableField("ypgg")
    private String ypgg;

    /**
     * 剂型代码
     */
    @Schema(description = "剂型代码")
    @TableField("jxdm")
    private String jxdm;

    /**
     * 分类代码
     */
    @Schema(description = "分类代码")
    @TableField("fldm")
    private String fldm;

    /**
     * 最小单位
     */
    @Schema(description = "最小单位")
    @TableField("zxdw")
    private String zxdw;

    /**
     * 规格单位
     */
    @Schema(description = "规格单位")
    @TableField("ggdw")
    private String ggdw;

    /**
     * 规格系数
     */
    @Schema(description = "规格系数")
    @TableField("ggxs")
    private BigDecimal ggxs;

    /**
     * 厂家代码
     */
    @Schema(description = "厂家代码")
    @TableField("cjdm")
    private String cjdm;

    /**
     * 厂家名称
     */
    @Schema(description = "厂家名称")
    @TableField("cjmc")
    private String cjmc;

    /**
     * 有效期（天数）
     */
    @Schema(description = "有效期（天数）")
    @TableField("yxq")
    private Integer yxq;

    /**
     * 药库单位
     */
    @Schema(description = "药库单位")
    @TableField("ykdw")
    private String ykdw;

    /**
     * 药库系数
     */
    @Schema(description = "药库系数")
    @TableField("ykxs")
    private BigDecimal ykxs;

    /**
     * 进货单位
     */
    @Schema(description = "进货单位")
    @TableField("jhdw")
    private String jhdw;

    /**
     * 进货系数
     */
    @Schema(description = "进货系数")
    @TableField("jhxs")
    private BigDecimal jhxs;

    /**
     * 门诊单位
     */
    @Schema(description = "门诊单位")
    @TableField("mzdw")
    private String mzdw;

    /**
     * 门诊系数
     */
    @Schema(description = "门诊系数")
    @TableField("mzxs")
    private BigDecimal mzxs;

    /**
     * 住院单位
     */
    @Schema(description = "住院单位")
    @TableField("zydw")
    private String zydw;

    /**
     * 住院系数
     */
    @Schema(description = "住院系数")
    @TableField("zyxs")
    private BigDecimal zyxs;

    /**
     * 药理售价
     */
    @Schema(description = "药理售价")
    @TableField("ylsj")
    private BigDecimal ylsj;

    /**
     * 药品费价
     */
    @Schema(description = "药品费价")
    @TableField("ypfj")
    private BigDecimal ypfj;

    /**
     * 默认进价
     */
    @Schema(description = "默认进价")
    @TableField("mrjj")
    private BigDecimal mrjj;

    /**
     * 上限价格
     */
    @Schema(description = "上限价格")
    @TableField("sxjg")
    private BigDecimal sxjg;

    /**
     * 自费标志
     */
    @Schema(description = "自费标志")
    @TableField("zfbz")
    private Short zfbz;

    /**
     * 自费比例
     */
    @Schema(description = "自费比例")
    @TableField("zfbl")
    private BigDecimal zfbl;

    /**
     * 住院自费标志
     */
    @Schema(description = "住院自费标志")
    @TableField("zyzfbz")
    private Short zyzfbz;

    /**
     * 住院自费比例
     */
    @Schema(description = "住院自费比例")
    @TableField("zyzfbl")
    private BigDecimal zyzfbl;

    /**
     * 贵重标志
     */
    @Schema(description = "贵重标志")
    @TableField("gzbz")
    private Short gzbz;

    /**
     * 特殊标志
     */
    @Schema(description = "特殊标志")
    @TableField("tsbz")
    private Short tsbz;

    /**
     * 临界量预标志
     */
    @Schema(description = "临界量预标志")
    @TableField("ljlybz")
    private Short ljlybz;

    /**
     * 皮试标志
     */
    @Schema(description = "皮试标志")
    @TableField("psbz")
    private Short psbz;

    /**
     * 停用标志
     */
    @Schema(description = "停用标志")
    @TableField("tybz")
    private Short tybz;

    /**
     * 缺货标志
     */
    @Schema(description = "缺货标志")
    @TableField("qzbz")
    private Short qzbz;

    /**
     * 医保限制标志
     */
    @Schema(description = "医保限制标志")
    @TableField("ybxzbz")
    private Short ybxzbz;

    /**
     * 医保控制标志
     */
    @Schema(description = "医保控制标志")
    @TableField("ybkzbz")
    private Short ybkzbz;

    /**
     * 药品库量
     */
    @Schema(description = "药品库量")
    @TableField("ypkl")
    private BigDecimal ypkl;

    /**
     * 准备标志
     */
    @Schema(description = "准备标志")
    @TableField("zbbz")
    private String zbbz;

    /**
     * 录入日期
     */
    @Schema(description = "录入日期")
    @TableField("lrrq")
    private String lrrq;

    /**
     * 助记类别
     */
    @Schema(description = "助记类别")
    @TableField("zmlb")
    private String zmlb;

    /**
     * 供货单位ID
     */
    @Schema(description = "供货单位ID")
    @TableField("ghdw_id")
    private String ghdwId;

    /**
     * 供货单位名称
     */
    @Schema(description = "供货单位名称")
    @TableField("ghdw_mc")
    private String ghdwMc;

    /**
     * 药品来源
     */
    @Schema(description = "药品来源")
    @TableField("yply")
    private String yply;

    /**
     * 批准文号
     */
    @Schema(description = "批准文号")
    @TableField("pzwh")
    private String pzwh;

    /**
     * GMP标志
     */
    @Schema(description = "GMP标志")
    @TableField("gmpbz")
    private Short gmpbz;

    /**
     * 操作员号
     */
    @Schema(description = "操作员号")
    @TableField("czyh")
    private String czyh;

    /**
     * 操作日期
     */
    @Schema(description = "操作日期")
    @TableField("czrq")
    private String czrq;

    /**
     * 准备单位
     */
    @Schema(description = "准备单位")
    @TableField("zbdw")
    private String zbdw;

    /**
     * 默认准备价
     */
    @Schema(description = "默认准备价")
    @TableField("mrzbj")
    private BigDecimal mrzbj;

    /**
     * 准备期号
     */
    @Schema(description = "准备期号")
    @TableField("zbqh")
    private String zbqh;

    /**
     * 二库单位
     */
    @Schema(description = "二库单位")
    @TableField("ekdw")
    private String ekdw;

    /**
     * 二库系数
     */
    @Schema(description = "二库系数")
    @TableField("ekxs")
    private BigDecimal ekxs;

    /**
     * 分类自费标志
     */
    @Schema(description = "分类自费标志")
    @TableField("flzfbz")
    private Short flzfbz;

    /**
     * 住院分类自费标志
     */
    @Schema(description = "住院分类自费标志")
    @TableField("zyflzfbz")
    private Short zyflzfbz;

    /**
     * 地方付标志
     */
    @Schema(description = "地方付标志")
    @TableField("dffbz")
    private Short dffbz;

    /**
     * 对应代码
     */
    @Schema(description = "对应代码")
    @TableField("dydm")
    private String dydm;

    /**
     * 是否对应
     */
    @Schema(description = "是否对应")
    @TableField("isdy")
    private Short isdy;

    /**
     * 备注
     */
    @Schema(description = "备注")
    @TableField("memo")
    private String memo;

    /**
     * 保安标志
     */
    @Schema(description = "保安标志")
    @TableField("babz")
    private Short babz;

    /**
     * 全军医院标志
     */
    @Schema(description = "全军医院标志")
    @TableField("qjyybz")
    private Short qjyybz;

    /**
     * OTC标志
     */
    @Schema(description = "OTC标志")
    @TableField("otcbz")
    private Short otcbz;

    /**
     * 临床药品标志
     */
    @Schema(description = "临床药品标志")
    @TableField("lgypbz")
    private Short lgypbz;

    /**
     * 上级主管标志
     */
    @Schema(description = "上级主管标志")
    @TableField("sjzjbz")
    private Short sjzjbz;

    /**
     * 保险标志
     */
    @Schema(description = "保险标志")
    @TableField("bxbz")
    private Short bxbz;

    /**
     * 配伍对应代码
     */
    @Schema(description = "配伍对应代码")
    @TableField("pwdydm")
    private Short pwdydm;

    /**
     * 药品说明书
     */
    @Schema(description = "药品说明书")
    @TableField("ypsms")
    private String ypsms;

    /**
     * 政府价格
     */
    @Schema(description = "政府价格")
    @TableField("govprice")
    private BigDecimal govprice;

    /**
     * ABC分类
     */
    @Schema(description = "ABC分类")
    @TableField("abc")
    private String abc;

    /**
     * 价格意见
     */
    @Schema(description = "价格意见")
    @TableField("jgyj")
    private String jgyj;

    /**
     * 处方药品
     */
    @Schema(description = "处方药品")
    @TableField("cfyp")
    private Short cfyp;

    /**
     * 国发名称
     */
    @Schema(description = "国发名称")
    @TableField("gfmc")
    private String gfmc;

    /**
     * 医保名称
     */
    @Schema(description = "医保名称")
    @TableField("ybmc")
    private String ybmc;

    /**
     * 国发标志
     */
    @Schema(description = "国发标志")
    @TableField("gfbz")
    private String gfbz;

    /**
     * 医保费用等级
     */
    @Schema(description = "医保费用等级")
    @TableField("ybfydj")
    private String ybfydj;

    /**
     * 准备医保标志
     */
    @Schema(description = "准备医保标志")
    @TableField("zbybz")
    private Short zbybz;

    /**
     * 市场药品进价
     */
    @Schema(description = "市场药品进价")
    @TableField("scypjj")
    private BigDecimal scypjj;

    /**
     * 合同标志
     */
    @Schema(description = "合同标志")
    @TableField("htbz")
    private Short htbz;

    /**
     * 销售标志
     */
    @Schema(description = "销售标志")
    @TableField("xsbz")
    private Short xsbz;

    /**
     * 使用范围
     */
    @Schema(description = "使用范围")
    @TableField("syfw")
    private Short syfw;

    /**
     * 使用证标志
     */
    @Schema(description = "使用证标志")
    @TableField("syzbz")
    private Short syzbz;

    /**
     * 会议标志
     */
    @Schema(description = "会议标志")
    @TableField("hybz")
    private Short hybz;

    /**
     * 用药管理标志
     */
    @Schema(description = "用药管理标志")
    @TableField("yzglbz")
    private Short yzglbz;

    /**
     * 是否计算统计
     */
    @Schema(description = "是否计算统计")
    @TableField("isjstj")
    private Short isjstj;

    /**
     * 统计开始日期
     */
    @Schema(description = "统计开始日期")
    @TableField("tjksrq")
    private String tjksrq;

    /**
     * 统计结束日期
     */
    @Schema(description = "统计结束日期")
    @TableField("tjjsrq")
    private String tjjsrq;

    /**
     * 统计天数
     */
    @Schema(description = "统计天数")
    @TableField("tjts")
    private Integer tjts;

    /**
     * 最高零售价
     */
    @Schema(description = "最高零售价")
    @TableField("zglsj")
    private BigDecimal zglsj;

    /**
     * 是否欠费控制
     */
    @Schema(description = "是否欠费控制")
    @TableField("isqfkz")
    private Short isqfkz;

    /**
     * 普药标标志
     */
    @Schema(description = "普药标标志")
    @TableField("pybbz")
    private Short pybbz;

    /**
     * 普国发标志
     */
    @Schema(description = "普国发标志")
    @TableField("pgfbz")
    private Short pgfbz;

    /**
     * 省药标标志
     */
    @Schema(description = "省药标标志")
    @TableField("sybbz")
    private Short sybbz;

    /**
     * 省国发标志
     */
    @Schema(description = "省国发标志")
    @TableField("sgfbz")
    private Short sgfbz;

    /**
     * 常用标标志
     */
    @Schema(description = "常用标标志")
    @TableField("cybbz")
    private Short cybbz;

    /**
     * 准备码
     */
    @Schema(description = "准备码")
    @TableField("zbm")
    private String zbm;

    /**
     * 大病常用标志
     */
    @Schema(description = "大病常用标志")
    @TableField("dbcybz")
    private Short dbcybz;

    /**
     * 是否对应2
     */
    @Schema(description = "是否对应2")
    @TableField("isdy2")
    private Short isdy2;

    /**
     * 数量限标志
     */
    @Schema(description = "数量限标志")
    @TableField("slxbz")
    private Short slxbz;

    /**
     * 是否临床急诊药品
     */
    @Schema(description = "是否临床急诊药品")
    @TableField("islcjsyp")
    private Short islcjsyp;

    /**
     * 临床急诊单价
     */
    @Schema(description = "临床急诊单价")
    @TableField("lcjsdj")
    private BigDecimal lcjsdj;

    /**
     * 特殊药品管理标志
     */
    @Schema(description = "特殊药品管理标志")
    @TableField("tsypglbz")
    private Short tsypglbz;

    /**
     * 特殊药品采购方式
     */
    @Schema(description = "特殊药品采购方式")
    @TableField("tsypcgfs")
    private Short tsypcgfs;

    /**
     * 门诊临用标志
     */
    @Schema(description = "门诊临用标志")
    @TableField("mzlybz")
    private Short mzlybz;

    /**
     * 供应标志
     */
    @Schema(description = "供应标志")
    @TableField("gybz")
    private String gybz;

    /**
     * 病区等级标志
     */
    @Schema(description = "病区等级标志")
    @TableField("bqdjbz")
    private String bqdjbz;

    /**
     * 临床单位1
     */
    @Schema(description = "临床单位1")
    @TableField("lcdw1")
    private String lcdw1;

    /**
     * 临床系数1
     */
    @Schema(description = "临床系数1")
    @TableField("lcxs1")
    private BigDecimal lcxs1;

    /**
     * 批准文号有效期
     */
    @Schema(description = "批准文号有效期")
    @TableField("pzwhxq")
    private LocalDateTime pzwhxq;

    /**
     * 表ID
     */
    @Schema(description = "表ID")
    @TableField("tbid")
    private Integer tbid;

    /**
     * 基本药物标志
     */
    @Schema(description = "基本药物标志")
    @TableField("basicdrug_flag")
    private Integer basicdrugFlag;

    /**
     * 急救使用药品标志
     */
    @Schema(description = "急救使用药品标志")
    @TableField("jhsyyp_flag")
    private Short jhsyypFlag;

    /**
     * 抗菌化药品标志
     */
    @Schema(description = "抗菌化药品标志")
    @TableField("kjhyp_flag")
    private Short kjhypFlag;

    /**
     * 处方说明
     */
    @Schema(description = "处方说明")
    @TableField("cfsm")
    private String cfsm;

    /**
     * 门诊标志
     */
    @Schema(description = "门诊标志")
    @TableField("mzbz")
    private Integer mzbz;

    /**
     * 急诊标志
     */
    @Schema(description = "急诊标志")
    @TableField("jzbz")
    private Integer jzbz;

    /**
     * 门诊医生站默认剂量
     */
    @Schema(description = "门诊医生站默认剂量")
    @TableField("mzysz_mrjl")
    private Short mzyszMrjl;

    /**
     * 特殊用法对应标志
     */
    @Schema(description = "特殊用法对应标志")
    @TableField("tsyfdybz")
    private Short tsyfdybz;

    /**
     * 特殊用法对应备注
     */
    @Schema(description = "特殊用法对应备注")
    @TableField("tsyfdymemo")
    private String tsyfdymemo;

    /**
     * 是否本院药品
     */
    @Schema(description = "是否本院药品")
    @TableField("isbjyp")
    private Short isbjyp;

    /**
     * 临床标志
     */
    @Schema(description = "临床标志")
    @TableField("lcbz")
    private String lcbz;

    /**
     * 小瓶数提醒方式
     */
    @Schema(description = "小瓶数提醒方式")
    @TableField("xpstxfs")
    private Long xpstxfs;

    /**
     * 小儿日剂量
     */
    @Schema(description = "小儿日剂量")
    @TableField("xdrjl")
    private BigDecimal xdrjl;

    /**
     * 是否按药物目录
     */
    @Schema(description = "是否按药物目录")
    @TableField("sjbywml")
    private Short sjbywml;

    /**
     * 收款财务项目ID
     */
    @Schema(description = "收款财务项目ID")
    @TableField("skcwxmid")
    private String skcwxmid;

    /**
     * 收款财务项目名称
     */
    @Schema(description = "收款财务项目名称")
    @TableField("skcwxmmc")
    private String skcwxmmc;

    /**
     * 小儿日剂量国标
     */
    @Schema(description = "小儿日剂量国标")
    @TableField("xdrjl_gb")
    private BigDecimal xdrjlGb;

    /**
     * 小儿日剂量国标单位
     */
    @Schema(description = "小儿日剂量国标单位")
    @TableField("xdrjl_gbdw")
    private String xdrjlGbdw;

    /**
     * 小儿日剂量国标系数
     */
    @Schema(description = "小儿日剂量国标系数")
    @TableField("xdrjl_gbxs")
    private BigDecimal xdrjlGbxs;

    /**
     * 采购方式
     */
    @Schema(description = "采购方式")
    @TableField("cgfs")
    private String cgfs;

    /**
     * 基本药物目录省
     */
    @Schema(description = "基本药物目录省")
    @TableField("jbywml_shi")
    private Short jbywmlShi;

    /**
     * 医保用药标志
     */
    @Schema(description = "医保用药标志")
    @TableField("yb_ysbz")
    private String ybYsbz;

    /**
     * 国发用药标志
     */
    @Schema(description = "国发用药标志")
    @TableField("gfyl_ysbz")
    private String gfylYsbz;

    /**
     * 医保分类说明
     */
    @Schema(description = "医保分类说明")
    @TableField("ybflsm")
    private String ybflsm;

    /**
     * 备注1
     */
    @Schema(description = "备注1")
    @TableField("memo1")
    private String memo1;

    /**
     * 时间戳
     */
    @Schema(description = "时间戳")
    @TableField("timetemp")
    private byte[] timetemp;

    /**
     * 收费分类编码
     */
    @Schema(description = "收费分类编码")
    @TableField("sfflbm")
    private String sfflbm;

    /**
     * ATC编码
     */
    @Schema(description = "ATC编码")
    @TableField("ATCbm")
    private String aTCbm;

    /**
     * 抗肿瘤药物等级ID
     */
    @Schema(description = "抗肿瘤药物等级ID")
    @TableField("kzlywdjid")
    private Integer kzlywdjid;

    /**
     * 常务院药物等级ID
     */
    @Schema(description = "常务院药物等级ID")
    @TableField("cwyyywdjid")
    private Integer cwyyywdjid;

    /**
     * 省区县特药品标志
     */
    @Schema(description = "省区县特药品标志")
    @TableField("sqxtypbz")
    private Short sqxtypbz;

    /**
     * 中科县特药品标志
     */
    @Schema(description = "中科县特药品标志")
    @TableField("zkxtypbz")
    private Short zkxtypbz;

    /**
     * 国外药品级别
     */
    @Schema(description = "国外药品级别")
    @TableField("gwypjb")
    private Short gwypjb;

    /**
     * 条码
     */
    @Schema(description = "条码")
    @TableField("tm")
    private String tm;

    /**
     * 等级权限
     */
    @Schema(description = "等级权限")
    @TableField("djqx")
    private String djqx;

    /**
     * 农合药品对应
     */
    @Schema(description = "农合药品对应")
    @TableField("nhypdy")
    private Short nhypdy;

    /**
     * 对应代码1
     */
    @Schema(description = "对应代码1")
    @TableField("dydm1")
    private String dydm1;

    /**
     * 外购标志
     */
    @Schema(description = "外购标志")
    @TableField("wgbz")
    private Short wgbz;

    /**
     * 外购系统标志
     */
    @Schema(description = "外购系统标志")
    @TableField("wgxtbz")
    private Short wgxtbz;

    /**
     * 限售药品标志
     */
    @Schema(description = "限售药品标志")
    @TableField("xsypbz")
    private Short xsypbz;

    /**
     * 医院代码
     */
    @Schema(description = "医院代码")
    @TableField("yydm")
    private String yydm;

    /**
     * 项目代码
     */
    @Schema(description = "项目代码")
    @TableField("xmdm")
    private String xmdm;

    /**
     * 药品编码
     */
    @Schema(description = "药品编码")
    @TableField("ypbm")
    private String ypbm;

    /**
     * 中医药理标志
     */
    @Schema(description = "中医药理标志")
    @TableField("zjylbz")
    private Short zjylbz;

    /**
     * 服务对象ID
     */
    @Schema(description = "服务对象ID")
    @TableField("serviceoid")
    private String serviceoid;

    /**
     * 最小可用剂数
     */
    @Schema(description = "最小可用剂数")
    @TableField("zxkyjs")
    private Integer zxkyjs;

    /**
     * 付费标志
     */
    @Schema(description = "付费标志")
    @TableField("ffbz")
    private Integer ffbz;

    /**
     * 贵州省医保对应代码
     */
    @Schema(description = "贵州省医保对应代码")
    @TableField("dydm_gzxyb")
    private String dydmGzxyb;

    /**
     * 贵州省医保英文编码
     */
    @Schema(description = "贵州省医保英文编码")
    @TableField("yjbwm_gzxyb")
    private String yjbwmGzxyb;

    /**
     * 贵州省医保是否对应
     */
    @Schema(description = "贵州省医保是否对应")
    @TableField("isdy_gzxyb")
    private Short isdyGzxyb;

    /**
     * 贵州省医保是否上传
     */
    @Schema(description = "贵州省医保是否上传")
    @TableField("issc_gzxyb")
    private Short isscGzxyb;

    /**
     * 贵州省医保异地对应代码
     */
    @Schema(description = "贵州省医保异地对应代码")
    @TableField("dydm_gzxyb_yd")
    private String dydmGzxybYd;

    /**
     * 贵州省医保异地是否对应
     */
    @Schema(description = "贵州省医保异地是否对应")
    @TableField("isdy_gzxyb_yd")
    private Short isdyGzxybYd;

    /**
     * 贵州省医保费用标目录
     */
    @Schema(description = "贵州省医保费用标目录")
    @TableField("fybml_gzxyb")
    private Short fybmlGzxyb;

    /**
     * 贵州省医保单位报销付
     */
    @Schema(description = "贵州省医保单位报销付")
    @TableField("dwbyzf_gzxyb")
    private Short dwbyzfGzxyb;

    /**
     * 贵州省医保排序ID
     */
    @Schema(description = "贵州省医保排序ID")
    @TableField("pxid_gzxyb")
    private Integer pxidGzxyb;

    /**
     * 省工伤医保对应代码
     */
    @Schema(description = "省工伤医保对应代码")
    @TableField("dydm_sgsyb")
    private String dydmSgsyb;

    /**
     * 省工伤医保是否对应
     */
    @Schema(description = "省工伤医保是否对应")
    @TableField("isdy_sgsyb")
    private Short isdySgsyb;

    /**
     * 省工伤医保是否上传
     */
    @Schema(description = "省工伤医保是否上传")
    @TableField("issc_sgsyb")
    private Short isscSgsyb;

    /**
     * 国家医保费用标上传标志
     */
    @Schema(description = "国家医保费用标上传标志")
    @TableField("gjyb_fybscbz")
    private String gjybFybscbz;

    /**
     * 国家医保费用标上传标志门诊
     */
    @Schema(description = "国家医保费用标上传标志门诊")
    @TableField("gjyb_fybscbz_mz")
    private String gjybFybscbzMz;

    /**
     * 安徽省工伤医保对应代码
     */
    @Schema(description = "安徽省工伤医保对应代码")
    @TableField("dydm_ahsgsyb")
    private String dydmAhsgsyb;

    /**
     * 安徽省工伤医保是否对应
     */
    @Schema(description = "安徽省工伤医保是否对应")
    @TableField("isdy_ahsgsyb")
    private String isdyAhsgsyb;

    /**
     * 安徽省工伤医保上报标志
     */
    @Schema(description = "安徽省工伤医保上报标志")
    @TableField("sbbz_ahsgsyb")
    private String sbbzAhsgsyb;
} 
