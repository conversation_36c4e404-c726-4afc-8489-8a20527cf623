-- 统计日期内的无码数据和上传成功的数据,目的查询拆零数据缺口
SELECT
    nc.med_list_codg,
    nc.fixmedins_hilist_name,
    GROUP_CONCAT(
        DISTINCT CASE WHEN nc.dept_id IS NULL OR nc.dept_id = '' THEN NULL ELSE nc.dept_id END
        ORDER BY nc.dept_id SEPARATOR ','
    ) AS missing_dept_ids,
    COUNT(nc.id) as no_trace_code_count,  -- 无码的明细数据总数
    sum(nc.sel_retn_cnt) as no_zsm_count,	-- 补充的追溯码拆零数量
    COALESCE(success_data.upload_success_count, 0) as upload_success_count,  -- 上传成功的明细总数
    success_data.zsm_count
FROM anyifuyang.nhsa_3505 AS nc
LEFT JOIN (
    SELECT
        med_list_codg,
        fixmedins_hilist_name,
        COUNT(*) as upload_success_count,
        sum(sel_retn_cnt) as zsm_count
    FROM anyifuyang.nhsa_3505
    WHERE sel_retn_time > '2025-08-03'
        AND sd_dps = 1
        AND trdn_flag = 1
        AND hsa_sync_status = '1'
        and mdtrt_setl_type = 1
    GROUP BY med_list_codg, fixmedins_hilist_name
) success_data ON nc.med_list_codg = success_data.med_list_codg
                 AND nc.fixmedins_hilist_name = success_data.fixmedins_hilist_name
WHERE nc.sel_retn_time > '2025-08-03'
    AND nc.sd_dps = 1
    AND nc.drug_trac_info IS NULL
    AND nc.trdn_flag = 1
    and nc.mdtrt_setl_type = 1
GROUP BY nc.med_list_codg, nc.fixmedins_hilist_name, success_data.upload_success_count
ORDER BY no_trace_code_count DESC, nc.med_list_codg;

-- 查询该药品的数据
select
	*
from
	nhsa_3505 n
where
	n.med_list_codg = 'XN01ABQ001L020010100675'
	and n.sel_retn_time > '2025-08-07'
	and n.sd_dps = 1
	and n.mdtrt_setl_type = 1;

-- 有追溯码没上传的数据
SELECT n.* FROM anyifuyang.nhsa_3505 AS n
WHERE drug_trac_info is not null and hsa_sync_status =0 and n.sd_dps =1
ORDER BY n.id desc;









-- 同时新增两个字段
ALTER TABLE `ysf_sto_dps` 
ADD COLUMN `pat_ward_id` varchar(24) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '患者病区ID',
ADD COLUMN `pat_ward_name` varchar(24) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '患者病区名称';

-- 修改sd_dps注释
ALTER TABLE `ysf_sto_dps` 
MODIFY COLUMN `sd_dps` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '发药单类型;1: 住院处方, 2:门诊处方';

-- 修改ysf_sto_tc_task_sub表中id_biz_sub字段类型为bigint
ALTER TABLE `ysf_sto_tc_task_sub`
MODIFY COLUMN `id_biz_sub` bigint NULL DEFAULT NULL COMMENT '业务明细ID: 发药单明细(ysf_sto_dps_sub.id)';

