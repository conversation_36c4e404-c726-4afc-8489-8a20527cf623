package com.zsm.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;

/**
 * 霍邱1院 部门信息视图实体类
 * 
 * <AUTHOR>
 * @since 2025-06-10
 */
@Data
@TableName("View_Dept_Info")
@Schema(description = "部门信息视图")
public class ViewDeptInfo implements Serializable {

    private static final long serialVersionUID = 1L;


    /**
     * 部门名称
     */
    @Schema(description = "部门名称")
    @TableField("deptName")
    private String deptName;

    /**
     * 部门编码
     */
    @Schema(description = "部门编码")
    @TableField("deptCode")
    private String deptCode;

    /**
     * 部门说明
     */
    @Schema(description = "部门说明")
    @TableField("explain")
    private String explain;
} 
