package com.zsm.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.math.BigDecimal;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 阜阳肿瘤住院发药处方视图实体类
 * 
 * <AUTHOR>
 * @since 2025-07-01
 */
@Data
@TableName("VIEW_YSF_ZYFYCF")
@Schema(description = "阜阳肿瘤住院发药处方视图")
public class ViewYsfZyfycf implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 返回代码
     */
    @Schema(description = "返回代码")
    @TableField("code")
    private String code;

    /**
     * 返回信息
     */
    @Schema(description = "返回信息")
    @TableField("message")
    private String message;

    /**
     * 医疗目录编码
     */
    @Schema(description = "医疗目录编码")
    @TableField("med_list_codg")
    private String medListCodg;

    /**
     * 定点医药机构目录编号
     */
    @Schema(description = "定点医药机构目录编号")
    @TableField("fixmedins_hilist_id")
    private Long fixmedinsHilistId;

    /**
     * 定点医药机构目录名称
     */
    @Schema(description = "定点医药机构目录名称")
    @TableField("fixmedins_hilist_name")
    private String fixmedinsHilistName;

    /**
     * 定点医药机构批次流水号
     */
    @Schema(description = "定点医药机构批次流水号")
    @TableField("fixmedins_bchno")
    private String fixmedinsBchno;

    /**
     * 开方医师证件号码
     */
    @Schema(description = "开方医师证件号码")
    @TableField("prsc_dr_certno")
    private String prscDrCertno;

    /**
     * 开方医师姓名
     */
    @Schema(description = "开方医师姓名")
    @TableField("prsc_dr_name")
    private String prscDrName;

    /**
     * 药师证件号码
     */
    @Schema(description = "药师证件号码")
    @TableField("phar_certno")
    private String pharCertno;

    /**
     * 药师姓名
     */
    @Schema(description = "药师姓名")
    @TableField("phar_name")
    private String pharName;

    /**
     * 药师执业资格证号
     */
    @Schema(description = "药师执业资格证号")
    @TableField("phar_prac_cert_no")
    private String pharPracCertNo;

    /**
     * 就医流水号
     */
    @Schema(description = "就医流水号")
    @TableField("mdtrt_sn")
    private String mdtrtSn;

    /**
     * 人员姓名
     */
    @Schema(description = "人员姓名")
    @TableField("psn_name")
    private String psnName;

    /**
     * 生产批号
     */
    @Schema(description = "生产批号")
    @TableField("manu_lotnum")
    private String manuLotnum;

    /**
     * 生产日期
     */
    @Schema(description = "生产日期")
    @TableField("manu_date")
    private String manuDate;

    /**
     * 有效期止
     */
    @Schema(description = "有效期止")
    @TableField("expy_end")
    private String expyEnd;

    /**
     * 处方药标志
     */
    @Schema(description = "处方药标志")
    @TableField("rx_flag")
    private String rxFlag;

    /**
     * 拆零标志
     */
    @Schema(description = "拆零标志")
    @TableField("trdn_flag")
    private String trdnFlag;

    /**
     * 处方号
     */
    @Schema(description = "处方号")
    @TableField("rxno")
    private String rxno;

    /**
     * 处方流转标志
     */
    @Schema(description = "处方流转标志")
    @TableField("rx_circ_flag")
    private String rxCircFlag;

    /**
     * 零售单据号
     */
    @Schema(description = "零售单据号")
    @TableField("rtal_docno")
    private String rtalDocno;

    /**
     * 销售出库单据号
     */
    @Schema(description = "销售出库单据号")
    @TableField("stoout_no")
    private String stooutNo;

    /**
     * 批次号
     */
    @Schema(description = "批次号")
    @TableField("bchno")
    private String bchno;

    /**
     * 销售/退货数量
     */
    @Schema(description = "销售/退货数量")
    @TableField("sel_retn_cnt")
    private BigDecimal selRetnCnt;

    /**
     * 销售/退货时间
     */
    @Schema(description = "销售/退货时间")
    @TableField("sel_retn_time")
    private String selRetnTime;

    /**
     * 销售/退货操作员姓名
     */
    @Schema(description = "销售/退货操作员姓名")
    @TableField("sel_retn_opter_name")
    private String selRetnOpterName;

    /**
     * 就诊结算类型
     */
    @Schema(description = "就诊结算类型")
    @TableField("mdtrt_setl_type")
    private String mdtrtSetlType;

    /**
     * 规格
     */
    @Schema(description = "规格")
    @TableField("spec")
    private String spec;

    /**
     * 生产企业名称
     */
    @Schema(description = "生产企业名称")
    @TableField("prodentp_name")
    private String prodentpName;

    /**
     * 处方序号
     */
    @Schema(description = "处方序号")
    @TableField("cfxh")
    private Long cfxh;

    /**
     * 处方明细序号
     */
    @Schema(description = "处方明细序号")
    @TableField("cfmxxh")
    private Long cfmxxh;

    /**
     * 收据号
     */
    @Schema(description = "收据号")
    @TableField("sjh")
    private Long sjh;

    /**
     * 患者ID
     */
    @Schema(description = "患者ID")
    @TableField("patient_id")
    private String patientId;

    /**
     * 发药药房
     */
    @Schema(description = "发药药房")
    @TableField("fyyf")
    private String fyyf;

    /**
     * 转换比
     */
    @Schema(description = "转换比")
    @TableField("his_con_ratio")
    private String hisConRatio;

    /**
     * 出院带药标志
     */
    @Schema(description = "出院带药标志")
    @TableField("cydybz")
    private Integer cydybz;

    /**
     * 记录id
     */
    @Schema(description = "记录id")
    @TableField("record_id")
    private Long recordId;

    /**
     * 最小销售/退货数量
     */
    @Schema(description = "最小销售/退货数量")
    @TableField("min_sel_retn_cnt")
    private BigDecimal minSelRetnCnt;
}