-- H2数据库测试表结构

-- 用户表
DROP TABLE IF EXISTS sys_user;
CREATE TABLE sys_user (
  id BIGINT AUTO_INCREMENT PRIMARY KEY,
  username VARCHAR(20) NOT NULL,
  password VARCHAR(100) NOT NULL,
  nickname <PERSON><PERSON><PERSON><PERSON>(30),
  email VARCHAR(50),
  phone VARCHAR(11),
  gender TINYINT DEFAULT 2,
  avatar VARCHAR(255),
  status TINYINT DEFAULT 0,
  remark VARCHAR(500),
  create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  create_by VARCHAR(50) DEFAULT 'system',
  update_by <PERSON><PERSON><PERSON><PERSON>(50) DEFAULT 'system',
  deleted TINYINT DEFAULT 0,
  version INT DEFAULT 0
);

-- 创建唯一索引
CREATE UNIQUE INDEX uk_username ON sys_user(username);
CREATE UNIQUE INDEX uk_email ON sys_user(email);
CREATE INDEX idx_status ON sys_user(status);
CREATE INDEX idx_create_time ON sys_user(create_time); 