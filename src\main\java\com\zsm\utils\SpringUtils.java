package com.zsm.utils;

import org.springframework.aop.framework.AopContext;
import org.springframework.beans.BeansException;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.stereotype.Component;

/**
 * Spring工具类
 * 用于获取Spring应用上下文和AOP代理对象
 * 
 * <AUTHOR>
 * @date 2025/06/10
 */
@Component
public class SpringUtils implements ApplicationContextAware {

    private static ApplicationContext applicationContext;

    @Override
    public void setApplicationContext(ApplicationContext context) throws BeansException {
        SpringUtils.applicationContext = context;
    }

    /**
     * 获取ApplicationContext
     */
    public static ApplicationContext getApplicationContext() {
        return applicationContext;
    }

    /**
     * 通过name获取Bean
     */
    public static Object getBean(String name) {
        return getApplicationContext().getBean(name);
    }

    /**
     * 通过class获取Bean
     */
    public static <T> T getBean(Class<T> clazz) {
        return getApplicationContext().getBean(clazz);
    }

    /**
     * 通过name,以及Clazz返回指定的Bean
     */
    public static <T> T getBean(String name, Class<T> clazz) {
        return getApplicationContext().getBean(name, clazz);
    }

    /**
     * 获取当前AOP代理对象
     * 用于确保事务注解和数据源切换注解生效
     */
    @SuppressWarnings("unchecked")
    public static <T> T getAopProxy(T invoker) {
        try {
            return (T) AopContext.currentProxy();
        } catch (IllegalStateException e) {
            // 如果没有在AOP代理中调用，返回原对象
            return invoker;
        }
    }
} 