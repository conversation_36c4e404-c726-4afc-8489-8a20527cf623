package com.zsm.service;

import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.zsm.common.SaasAuthorizationVerifyAspect;
import com.zsm.common.exception.BusinessException;
import com.zsm.constant.NhsaAccountConstant;
import com.zsm.entity.*;
import com.zsm.his.AnsycService;
import com.zsm.mapper.YsfStoTcMapper;
import com.zsm.mapper.YsfStoTcTaskMapper;
import com.zsm.model.ApiResult;
import com.zsm.model.TableDataInfo;
import com.zsm.model.dto.DispensingRecordDetailQueryDto;
import com.zsm.model.dto.DispensingRecordQueryDto;
import com.zsm.model.dto.TraceabilityUploadDto;
import com.zsm.model.enums.DispenseOrderStatusEnum;
import com.zsm.model.enums.SdTcStatusEnum;
import com.zsm.model.enums.StoTcTaskStatusEnum;
import com.zsm.model.enums.TaskStatusEnum;
import com.zsm.model.vo.SaasUserInfoResponse;
import com.zsm.model.vo.DispensingRecordDetailVo;
import com.zsm.model.vo.DispensingRecordVo;
import com.zsm.model.vo.TraceabilityUploadResultVo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import jakarta.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.Executor;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 通用服务层
 *
 * <AUTHOR>
 * @date 2025/6/10 21:45
 */
@Slf4j
@Service
public class YsfService {

    @Resource
    private YsfStoTcTaskMapper ysfStoTcTaskMapper;

    @Resource
    private YsfStoTcTaskSubService ysfStoTcTaskSubService;

    @Resource
    private YsfStoTcMapper ysfStoTcMapper;

    @Resource
    private YsfStoDpsService ysfStoDpsService;

    @Resource
    private YsfStoDpsSubService ysfStoDpsSubService;

    @Resource
    private YsfStoTcService ysfStoTcService;

    @Resource
    private YsfStoTcStatusService ysfStoTcStatusService;

    @Resource
    private YsfStoTcTaskService ysfStoTcTaskService;

    @Resource
    private Nhsa3505Service nhsa3505Service;

    @Resource
    private AnsycService ansycService;

    @Autowired(required = false)
    private Executor taskExecutor;

    /**
     * 上传药品追溯码扫描数据（优化版本）
     *
     * @param request 追溯码上传请求
     * @return 上传结果
     */
    public ApiResult<TraceabilityUploadResultVo> uploadScans(TraceabilityUploadDto request) {
        long startTime = System.currentTimeMillis();

        // 获取token登录的账号信息
        SaasUserInfoResponse userInfo = SaasAuthorizationVerifyAspect.userInfoThreadLocal.get();
        String userId = userInfo.getUser().getUserId().toString();
        String userName = userInfo.getUser().getUserName();
        String orgId = NhsaAccountConstant.getNhsaAccount().getMedicalCode();
        String orgName = NhsaAccountConstant.getNhsaAccount().getMedicalName();

        // 校验请求参数
        if (request == null || request.getPrescriptions() == null || request.getPrescriptions().isEmpty()) {
            return ApiResult.error("请求参数不能为空");
        }

        try {
            // 第一步：并行校验所有处方数据
            Map<String, String> validationResults = validatePrescriptionsInParallel(request.getPrescriptions());

            // 统计结果
            List<String> successIds = new ArrayList<>();
            List<Object> failIds = new ArrayList<>();
            Map<String, String> failMessages = new HashMap<>();

            boolean hasValidationErrors = false;
            for (TraceabilityUploadDto.PrescriptionItem prescription : request.getPrescriptions()) {
                String outPresId = prescription.getOutPresId();
                if (StringUtils.isEmpty(outPresId)) {
                    failIds.add("unknown");
                    failMessages.put("unknown", "处方ID不能为空");
                    hasValidationErrors = true;
                    continue;
                }

                String errorMessage = validationResults.get(outPresId);
                if (errorMessage != null && !errorMessage.equals("SUCCESS")) {
                    log.error("校验处方[{}]失败: {}", outPresId, errorMessage);
                    failIds.add(outPresId);
                    failMessages.put(outPresId, errorMessage);
                    hasValidationErrors = true;
                } else {
                    successIds.add(outPresId);
                }
            }

            // 构造返回结果
            TraceabilityUploadResultVo result = new TraceabilityUploadResultVo();
            result.setSuccess(successIds);
            result.setFail(failIds);
            result.setFailMessages(failMessages);

            // 第二步：如果有校验错误，直接返回错误结果，不进行任何保存操作
            if (hasValidationErrors) {
                result.setSuccess(Collections.emptyList());
                for (String successId : successIds) {
                    if (!failIds.contains(successId)) {
                        failIds.add(successId);
                        failMessages.put(successId, "由于其他处方校验失败，该处方未被保存");
                    }
                }
                result.setFail(failIds);
                result.setFailMessages(failMessages);

                ApiResult<TraceabilityUploadResultVo> response = ApiResult.error("部分处方校验失败，所有数据未保存");
                response.setData(result);
                return response;
            }

            // 第三步：所有校验都通过，批量执行数据保存操作
            processPrescriptionsBatch(request.getPrescriptions(), userId, userName, orgId, orgName, userInfo.getAuthorization(), request);

            // 记录性能指标
            long endTime = System.currentTimeMillis();
            log.info("uploadScans方法执行完成，处理{}个处方，耗时{}ms",
                    request.getPrescriptions().size(), endTime - startTime);

            return ApiResult.success(result);

        } catch (Exception e) {
            log.error("上传药品追溯码失败: {}", e.getMessage(), e);
            throw new BusinessException("上传药品追溯码失败: " + e.getMessage());
        }
    }

    /**
     * 并行校验所有处方数据
     *
     * @param prescriptions 处方列表
     * @return 校验结果Map，key为处方ID，value为错误信息（null表示校验通过）
     */
    private Map<String, String> validatePrescriptionsInParallel(List<TraceabilityUploadDto.PrescriptionItem> prescriptions) {
        // 收集所有追溯码用于批量查询
        Set<String> allTraceabilityCodes = new HashSet<>();
        for (TraceabilityUploadDto.PrescriptionItem prescription : prescriptions) {
            if (prescription.getDrugItems() != null) {
                for (TraceabilityUploadDto.DrugItem drugItem : prescription.getDrugItems()) {
                    if (drugItem.getTrdnFlag() == 1 || StringUtils.isEmpty(drugItem.getDrugtracinfoScanned())) {
                        continue;
                    }
                    String[] codes = drugItem.getDrugtracinfoScanned().split(",");
                    for (String code : codes) {
                        if (StringUtils.isNotEmpty(code.trim())) {
                            allTraceabilityCodes.add(code.trim());
                        }
                    }
                }
            }
        }

        // 批量查询追溯码状态
        Map<String, YsfStoTc> traceabilityCodeMap = batchQueryTraceabilityCodes(allTraceabilityCodes);

        // 并行校验处方
        Map<String, String> results = new ConcurrentHashMap<>();

        // 检查是否有足够的处方数据进行并行处理
        if (prescriptions.size() <= 1) {
            // 单个处方直接处理，避免并行开销
            for (TraceabilityUploadDto.PrescriptionItem prescription : prescriptions) {
                try {
                    validatePrescriptionWithCache(prescription, traceabilityCodeMap);
                    results.put(prescription.getOutPresId(), "SUCCESS");
                } catch (BusinessException e) {
                    results.put(prescription.getOutPresId(), e.getMessage());
                }
            }
        } else {
            // 多个处方并行处理
            List<CompletableFuture<Void>> validationFutures = prescriptions.stream()
                    .map(prescription -> {
                        if (taskExecutor != null) {
                            return CompletableFuture.runAsync(() -> {
                                try {
                                    validatePrescriptionWithCache(prescription, traceabilityCodeMap);
                                    results.put(prescription.getOutPresId(), "SUCCESS");
                                } catch (BusinessException e) {
                                    results.put(prescription.getOutPresId(), e.getMessage());
                                }
                            }, taskExecutor);
                        } else {
                            return CompletableFuture.runAsync(() -> {
                                try {
                                    validatePrescriptionWithCache(prescription, traceabilityCodeMap);
                                    results.put(prescription.getOutPresId(), "SUCCESS");
                                } catch (BusinessException e) {
                                    results.put(prescription.getOutPresId(), e.getMessage());
                                }
                            });
                        }
                    })
                    .collect(Collectors.toList());

            // 等待所有校验完成
            CompletableFuture.allOf(validationFutures.toArray(new CompletableFuture[0])).join();
        }

        return results;
    }

    /**
     * 校验单个处方数据（使用缓存的追溯码数据）
     *
     * @param prescription        处方信息
     * @param traceabilityCodeMap 追溯码缓存Map
     * @throws BusinessException 校验失败时抛出异常
     */
    private void validatePrescriptionWithCache(TraceabilityUploadDto.PrescriptionItem prescription,
                                               Map<String, YsfStoTc> traceabilityCodeMap) {
        String outPresId = prescription.getOutPresId();

        // 校验处方基本信息
        if (StringUtils.isEmpty(outPresId)) {
            throw new BusinessException("处方ID不能为空");
        }

        if (prescription.getDrugItems() == null || prescription.getDrugItems().isEmpty()) {
            throw new BusinessException("处方药品明细不能为空");
        }

        // 校验每个药品明细
        for (TraceabilityUploadDto.DrugItem drugItem : prescription.getDrugItems()) {
            if (drugItem.getTrdnFlag() == 1) {
                continue;
            }
            validateDrugItemWithCache(drugItem, traceabilityCodeMap);
        }
    }

    /**
     * 批量查询追溯码状态
     *
     * @param traceabilityCodes 追溯码集合
     * @return 追溯码状态Map
     */
    private Map<String, YsfStoTc> batchQueryTraceabilityCodes(Set<String> traceabilityCodes) {
        if (traceabilityCodes.isEmpty()) {
            return new HashMap<>();
        }

        LambdaQueryWrapper<YsfStoTc> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(YsfStoTc::getDrugtracinfo, traceabilityCodes)
                .eq(YsfStoTc::getDelFlag, "0");

        List<YsfStoTc> tcList = ysfStoTcService.list(queryWrapper);
        return tcList.stream().collect(Collectors.toMap(YsfStoTc::getDrugtracinfo, Function.identity()));
    }

    /**
     * 校验药品明细数据（使用缓存）
     *
     * @param drugItem            药品明细
     * @param traceabilityCodeMap 追溯码缓存Map
     * @throws BusinessException 校验失败时抛出异常
     */
    private void validateDrugItemWithCache(TraceabilityUploadDto.DrugItem drugItem,
                                           Map<String, YsfStoTc> traceabilityCodeMap) {
        String outPresdetailid = drugItem.getOutPresdetailid();
        String drugtracinfo = drugItem.getDrugtracinfoScanned();

        if (StringUtils.isEmpty(outPresdetailid)) {
            throw new BusinessException("处方明细ID不能为空");
        }

        if (StringUtils.isEmpty(drugtracinfo)) {
            return;
        }

        // 拆分追溯码并校验每个追溯码
        String[] drugtracinfoArray = drugtracinfo.split(",");
        for (String drugZsm : drugtracinfoArray) {
            if (StringUtils.isEmpty(drugZsm.trim())) {
                throw new BusinessException("追溯码不能为空");
            }

            // 校验追溯码是否已被使用（使用缓存）
            validateTraceabilityCodeWithCache(drugZsm.trim(), traceabilityCodeMap);
        }
    }

    /**
     * 校验追溯码是否可用（使用缓存）
     *
     * @param drugtracinfo        追溯码
     * @param traceabilityCodeMap 追溯码缓存Map
     * @throws BusinessException 校验失败时抛出异常
     */
    private void validateTraceabilityCodeWithCache(String drugtracinfo,
                                                   Map<String, YsfStoTc> traceabilityCodeMap) {
        YsfStoTc stoTc = traceabilityCodeMap.get(drugtracinfo);

        if (stoTc != null) {
            // 检查追溯码是否已被使用
            if (stoTc.getAmountRem() != null && stoTc.getAmountRem()
                    .compareTo(BigDecimal.ZERO) == 0) {
                throw new BusinessException("追溯码[" + drugtracinfo + "]已被使用", stoTc);
            }
        }
    }

    /**
     * 批量处理处方数据（优化版本）
     *
     * @param prescriptions 处方列表
     * @param userId        用户ID
     * @param userName      用户名
     * @param orgId         机构ID
     * @param orgName       机构名称
     * @param authorization 授权信息
     * @param request       请求
     */
    @Transactional(rollbackFor = Exception.class)
    public void processPrescriptionsBatch(List<TraceabilityUploadDto.PrescriptionItem> prescriptions,
                                          String userId, String userName, String orgId, String orgName,
                                          String authorization, TraceabilityUploadDto request) {

        log.info("开始批量处理处方数据，总数量: {}", prescriptions.size());

        // 分步骤批量处理所有处方，确保依赖关系正确
        processPrescriptionsBatchInternal(prescriptions, userId, userName, orgId, orgName, authorization, request);

        log.info("批量处理处方数据完成，处理数量: {}", prescriptions.size());
    }

    /**
     * 内部批量处理方法
     */
    private void processPrescriptionsBatchInternal(List<TraceabilityUploadDto.PrescriptionItem> prescriptions,
                                                   String userId, String userName, String orgId, String orgName,
                                                   String authorization, TraceabilityUploadDto request) {

        long stepStartTime = System.currentTimeMillis();

        // 第一步：批量处理发药单和任务
        log.info("开始处理发药单和扫码任务，处方数量: {}", prescriptions.size());
        Map<String, YsfStoDps> stoDpsMap = new HashMap<>();
        Map<String, YsfStoTcTask> tcTaskMap = new HashMap<>();

        List<YsfStoDps> newStoDpsList = new ArrayList<>();
        List<YsfStoTcTask> newTcTaskList = new ArrayList<>();

        for (TraceabilityUploadDto.PrescriptionItem prescription : prescriptions) {
            String outPresId = prescription.getOutPresId();

            // 处理发药单
            YsfStoDps stoDps = getOrCreateStoDps(outPresId, prescription, userName, orgId, orgName, request);
            if (stoDps.getIdDps() == null) {
                newStoDpsList.add(stoDps);
            }
            stoDpsMap.put(outPresId, stoDps);

            // 处理扫码任务
            YsfStoTcTask tcTask = getOrCreateTcTask(outPresId, stoDps, userId, userName, orgId, orgName);
            if (tcTask.getIdTask() == null) {
                newTcTaskList.add(tcTask);
            }
            tcTaskMap.put(outPresId, tcTask);
        }

        // 批量保存发药单和任务
        if (!newTcTaskList.isEmpty()) {
            ysfStoTcTaskService.saveBatch(newTcTaskList);
            log.info("批量保存扫码任务完成，生成了自增id: {}", JSONUtil.toJsonStr(newTcTaskList));

            // 设置发药单的任务ID
            setTaskIdsToStoDps(newTcTaskList, newStoDpsList);
        }

        if (!newStoDpsList.isEmpty()) {
            log.info("批量保存发药单，数量: {}", newStoDpsList.size());
            ysfStoDpsService.saveBatch(newStoDpsList);
        }


        long step1Time = System.currentTimeMillis();
        log.info("第一步完成，耗时: {}ms", step1Time - stepStartTime);

        // 第二步：批量处理明细数据
        log.info("开始处理药品明细数据");
        processDrugItemsBatch(prescriptions, stoDpsMap, tcTaskMap, userId, userName, orgId, orgName);

        long step2Time = System.currentTimeMillis();
        log.info("第二步完成，耗时: {}ms", step2Time - step1Time);

        // 第三步：批量更新3505表
        List<TraceabilityUploadDto.DrugItem> allDrugItems = prescriptions.stream()
                .flatMap(p -> p.getDrugItems().stream())
                .collect(Collectors.toList());
        batchUpdateNhsa3505(allDrugItems, userName, orgId, orgName);

        long step3Time = System.currentTimeMillis();
        log.info("第三步完成，耗时: {}ms", step3Time - step2Time);

        // 异步处理拆分任务
        processSassConfirmAsync(prescriptions, authorization);

        long totalTime = System.currentTimeMillis();
        log.info("批量处理内部方法完成，总耗时: {}ms", totalTime - stepStartTime);
    }

    /**
     * 设置发药单的任务ID（仅在内存中设置，不进行数据库操作）
     *
     * @param newTcTaskList 新保存的任务列表（已有生成的ID）
     * @param newStoDpsList 新建发药单列表（需要设置id_task字段）
     */
    private void setTaskIdsToStoDps(List<YsfStoTcTask> newTcTaskList, List<YsfStoDps> newStoDpsList) {
        if (newTcTaskList.isEmpty() || newStoDpsList.isEmpty()) {
            log.warn("任务列表或发药单列表为空，跳过ID关联设置");
            return;
        }

        try {
            // 1. 建立任务的cdBiz（outPresId）到idTask的映射关系
            Map<String, Long> taskIdMap = newTcTaskList.stream()
                    .filter(task -> task.getIdTask() != null && StringUtils.isNotEmpty(task.getCdBiz()))
                    .collect(Collectors.toMap(
                            YsfStoTcTask::getCdBiz,
                            YsfStoTcTask::getIdTask,
                            (existing, replacement) -> {
                                log.warn("发现重复的处方ID: {}, 使用第一个任务ID: {}",
                                        existing, replacement);
                                return existing; // 保留第一个
                            }
                    ));

            // 2. 在内存中设置发药单的id_task字段            
            for (YsfStoDps stoDps : newStoDpsList) {
                if (StringUtils.isNotEmpty(stoDps.getCfxh())) {
                    Long taskId = taskIdMap.get(stoDps.getCfxh());
                    if (taskId != null) {
                        stoDps.setIdTask(taskId);  // 仅在内存中设置
                    } else {
                        log.warn("未找到匹配的任务，处方ID: {}", stoDps.getCfxh());
                    }
                } else {
                    log.warn("发药单的处方ID为空，跳过关联，cfxh: {}", stoDps.getCfxh());
                }
            }
            log.info("设置发药单任务ID完成，新的数据: {}", JSONUtil.toJsonStr(newStoDpsList));

        } catch (Exception e) {
            log.error("设置发药单任务ID时发生异常", e);
            throw new BusinessException("设置发药单任务ID失败: " + e.getMessage());
        }
    }


    /**
     * 批量处理药品明细数据
     */
    private void processDrugItemsBatch(List<TraceabilityUploadDto.PrescriptionItem> prescriptions,
                                       Map<String, YsfStoDps> stoDpsMap, Map<String, YsfStoTcTask> tcTaskMap,
                                       String userId, String userName, String orgId, String orgName) {

        long startTime = System.currentTimeMillis();

        List<YsfStoDpsSub> newStoDpsSubList = new ArrayList<>();
        List<YsfStoTcTaskSub> newTcTaskSubList = new ArrayList<>();

        int totalDrugItems = 0;
        for (TraceabilityUploadDto.PrescriptionItem prescription : prescriptions) {
            String outPresId = prescription.getOutPresId();
            YsfStoDps stoDps = stoDpsMap.get(outPresId);
            YsfStoTcTask tcTask = tcTaskMap.get(outPresId);

            for (TraceabilityUploadDto.DrugItem drugItem : prescription.getDrugItems()) {
                totalDrugItems++;
                String drugtracinfo = drugItem.getDrugtracinfoScanned();
                if (StringUtils.isEmpty(drugtracinfo)) {
                    continue;
                }

                // 处理任务明细
                YsfStoTcTaskSub tcTaskSub = getOrCreateTcTaskSub(tcTask, drugItem, userName, orgId, orgName);
                if (tcTaskSub.getIdSub() == null) {
                    newTcTaskSubList.add(tcTaskSub);
                }

                // 处理发药单明细
                YsfStoDpsSub stoDpsSub = getOrCreateStoDpsSub(stoDps, drugItem, userId, userName, orgId, orgName);
                if (stoDpsSub.getId() == null) {
                    newStoDpsSubList.add(stoDpsSub);
                }
            }
        }

        long prepareTime = System.currentTimeMillis();
        log.info("药品明细数据准备完成，总药品数: {}, 新发药单明细: {}, 新任务明细: {}, 耗时: {}ms",
                totalDrugItems, newStoDpsSubList.size(), newTcTaskSubList.size(), prepareTime - startTime);

        // 批量保存明细数据
        if (!newStoDpsSubList.isEmpty()) {
            log.info("批量保存发药单明细，数量: {}", newStoDpsSubList.size());
            ysfStoDpsSubService.saveBatch(newStoDpsSubList);
        }
        if (!newTcTaskSubList.isEmpty()) {
            log.info("批量保存任务明细，数量: {}", newTcTaskSubList.size());
            ysfStoTcTaskSubService.saveBatch(newTcTaskSubList);
        }

        long saveTime = System.currentTimeMillis();
        log.info("明细数据批量保存完成，耗时: {}ms", saveTime - prepareTime);

        // 处理追溯码数据（需要在明细保存后进行，因为需要明细ID）
        processTraceabilityCodesBatch(prescriptions, stoDpsMap, tcTaskMap, userName, orgId, orgName);

        long totalTime = System.currentTimeMillis();
        log.info("药品明细数据处理完成，总耗时: {}ms", totalTime - startTime);
    }

    /**
     * 批量处理追溯码数据
     */
    private void processTraceabilityCodesBatch(List<TraceabilityUploadDto.PrescriptionItem> prescriptions,
                                               Map<String, YsfStoDps> stoDpsMap, Map<String, YsfStoTcTask> tcTaskMap,
                                               String userName, String orgId, String orgName) {

        long startTime = System.currentTimeMillis();

        List<YsfStoTc> stoTcUpdateList = new ArrayList<>();
        List<YsfStoTcStatus> tcStatusList = new ArrayList<>();

        int totalTraceCodes = 0;
        for (TraceabilityUploadDto.PrescriptionItem prescription : prescriptions) {
            String outPresId = prescription.getOutPresId();
            YsfStoDps stoDps = stoDpsMap.get(outPresId);
            YsfStoTcTask tcTask = tcTaskMap.get(outPresId);
            // "1"表示住院处方，"2"表示门诊处方
            boolean isInpatient = "1".equals(stoDps.getSdDps());

            for (TraceabilityUploadDto.DrugItem drugItem : prescription.getDrugItems()) {
                String drugtracinfo = drugItem.getDrugtracinfoScanned();
                if (StringUtils.isEmpty(drugtracinfo)) {
                    continue;
                }

                // 获取发药单明细ID  
                YsfStoDpsSub stoDpsSub = getOrCreateStoDpsSub(stoDps, drugItem, userName, userName, orgId, orgName);
                String stoDpsSubId = stoDpsSub.getId().toString();

                // 处理追溯码
                String[] drugtracinfoArray = drugtracinfo.split(",");
                for (String drugZsm : drugtracinfoArray) {
                    if (StringUtils.isNotEmpty(drugZsm.trim())) {
                        totalTraceCodes++;
                        processTraceabilityCodeBatch(drugZsm.trim(), drugItem, stoDpsSubId, userName, orgId, orgName,
                                tcTask.getIdDept(), stoTcUpdateList, tcStatusList, isInpatient);
                    }
                }
            }
        }

        long prepareTime = System.currentTimeMillis();
        log.info("追溯码数据准备完成，总追溯码数: {}, 追溯码更新: {}, 状态记录: {}, 耗时: {}ms",
                totalTraceCodes, stoTcUpdateList.size(), tcStatusList.size(), prepareTime - startTime);

        // 批量保存/更新追溯码数据
        if (!stoTcUpdateList.isEmpty()) {
            log.info("批量保存/更新追溯码数据，数量: {}", stoTcUpdateList.size());
            ysfStoTcService.saveOrUpdateBatch(stoTcUpdateList);
        }
        if (!tcStatusList.isEmpty()) {
            log.info("批量保存追溯码状态记录，数量: {}", tcStatusList.size());
            ysfStoTcStatusService.saveBatch(tcStatusList);
        }

        long totalTime = System.currentTimeMillis();
        log.info("追溯码数据批量处理完成，总耗时: {}ms", totalTime - startTime);
    }

    /**
     * 处理单个追溯码（用于批量处理）
     */
    private void processTraceabilityCodeBatch(String drugtracinfo, TraceabilityUploadDto.DrugItem drugItem,
                                              String stoDpsSubId, String userName, String orgId, String orgName,
                                              String idDept, List<YsfStoTc> stoTcUpdateList, List<YsfStoTcStatus> tcStatusList,
                                              boolean isInpatient) {

        // 查询现有追溯码记录
        LambdaQueryWrapper<YsfStoTc> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(YsfStoTc::getDrugtracinfo, drugtracinfo)
                .eq(YsfStoTc::getDelFlag, "0")
                .last("limit 1");
        YsfStoTc existingStoTc = ysfStoTcService.getOne(queryWrapper);

        YsfStoTc stoTc;
        if (existingStoTc != null) {
            // 更新现有记录
            existingStoTc.setAmountRem(BigDecimal.ZERO);
            existingStoTc.setUpdateBy(userName);
            existingStoTc.setUpdateTime(LocalDateTime.now());
            stoTcUpdateList.add(existingStoTc);
            stoTc = existingStoTc;
        } else {
            // 创建新记录
            stoTc = createNewYsfStoTc(drugtracinfo, drugItem, userName, orgId, orgName, idDept);
            stoTcUpdateList.add(stoTc);
        }

        // 创建追溯码状态记录
        YsfStoTcStatus tcStatus = new YsfStoTcStatus();
        if (isInpatient) {
            tcStatus.setSdTcStatus(SdTcStatusEnum.INPATIENT_DISPENSING.getCode());
        } else {
            tcStatus.setSdTcStatus(SdTcStatusEnum.OUTPATIENT_DISPENSING.getCode());
        }
        tcStatus.setSdTcManage("1");
        tcStatus.setIdBizOri(stoDpsSubId);
        tcStatus.setCfmxxh(drugItem.getOutPresdetailid());
        tcStatus.setDrugCode(drugItem.getDrugCode());
        tcStatus.setDrugtracinfo(drugtracinfo);
        tcStatus.setSdTc("2");
        tcStatus.setSelRetnCnt(drugItem.getMinDoseCount());
        tcStatus.setFgUp("0");
        tcStatus.setFgActive("1");
        tcStatus.setIdDept(idDept);
        // 对于新创建的追溯码，先设置为null，批量保存后会自动获得ID
        tcStatus.setIdTc(stoTc.getIdTc());
        tcStatus.setIdOrg(orgId);
        tcStatus.setOrgId(orgId);
        tcStatus.setOrgName(orgName);
        tcStatus.setCreateBy(userName);
        tcStatus.setCreateTime(LocalDateTime.now());
        tcStatus.setUpdateBy(userName);
        tcStatus.setUpdateTime(LocalDateTime.now());
        tcStatus.setDelFlag("0");

        tcStatusList.add(tcStatus);
    }

    /**
     * 批量更新3505表
     */
    private void batchUpdateNhsa3505(List<TraceabilityUploadDto.DrugItem> drugItems,
                                     String userName, String orgId, String orgName) {
        if (drugItems.isEmpty()) {
            return;
        }

        log.info("开始批量更新3505表，药品明细数量: {}", drugItems.size());

        // 按处方明细ID分组，去重处理
        Map<String, String> updateMap = drugItems.stream()
                .filter(item -> StringUtils.isNotEmpty(item.getOutPresdetailid()) &&
                        StringUtils.isNotEmpty(item.getDrugtracinfoScanned()))
                .collect(Collectors.toMap(
                        TraceabilityUploadDto.DrugItem::getOutPresdetailid,
                        TraceabilityUploadDto.DrugItem::getDrugtracinfoScanned,
                        (existing, replacement) -> existing // 如果有重复key，保留第一个
                ));

        if (updateMap.isEmpty()) {
            log.warn("没有有效的药品明细需要更新3505表");
            return;
        }

        // 批量更新所有数据
        int successCount = 0;
        int failCount = 0;

        for (Map.Entry<String, String> entry : updateMap.entrySet()) {
            try {
                nhsa3505Service.updateDrugTraceabilityInfo(entry.getKey(), entry.getValue(), userName, orgId, orgName);
                successCount++;
            } catch (Exception e) {
                log.error("更新3505表失败，处方明细ID: {}, 追溯码: {}",
                        entry.getKey(), entry.getValue(), e);
                failCount++;
            }
        }

        log.info("批量更新3505表完成，成功: {}, 失败: {}, 总计: {}",
                successCount, failCount, updateMap.size());
    }

    /**
     * 异步处理Sass确认任务
     */
    private void processSassConfirmAsync(List<TraceabilityUploadDto.PrescriptionItem> prescriptions, String authorization) {
        if (prescriptions == null || prescriptions.isEmpty()) {
            return;
        }

        log.info("开始异步处理Sass确认任务，处方数量: {}", prescriptions.size());

        // 创建所有异步任务
        List<CompletableFuture<Void>> asyncTasks = prescriptions.stream()
                .map(prescription -> {
                    if (taskExecutor != null) {
                        return CompletableFuture.runAsync(() -> {
                            try {
                                ansycService.saveSassConfirmMedicationDispensingAsync(prescription, authorization);
                                log.debug("处方[{}]异步处理完成", prescription.getOutPresId());
                            } catch (Exception e) {
                                log.error("异步处理Sass确认任务失败，处方ID: {}", prescription.getOutPresId(), e);
                            }
                        }, taskExecutor);
                    } else {
                        return CompletableFuture.runAsync(() -> {
                            try {
                                ansycService.saveSassConfirmMedicationDispensingAsync(prescription, authorization);
                                log.debug("处方[{}]异步处理完成", prescription.getOutPresId());
                            } catch (Exception e) {
                                log.error("异步处理Sass确认任务失败，处方ID: {}", prescription.getOutPresId(), e);
                            }
                        });
                    }
                })
                .collect(Collectors.toList());

        // 异步执行所有任务，但不等待结果（fire and forget模式）
        CompletableFuture.allOf(asyncTasks.toArray(new CompletableFuture[0]))
                .whenComplete((result, throwable) -> {
                    if (throwable != null) {
                        log.error("批量异步任务执行过程中发生异常", throwable);
                    } else {
                        log.info("所有Sass确认异步任务已启动完成，数量: {}", asyncTasks.size());
                    }
                });
    }

    /**
     * 创建新的YsfStoTc对象
     */
    private YsfStoTc createNewYsfStoTc(String drugtracinfo, TraceabilityUploadDto.DrugItem drugItem,
                                       String userName, String orgId, String orgName, String idDept) {
        YsfStoTc stoTc = new YsfStoTc();
        stoTc.setDrugtracinfo(drugtracinfo);
        stoTc.setDrugCode(drugItem.getDrugCode());
        stoTc.setAmountRem(BigDecimal.ZERO);
        stoTc.setUnitSaleFactor(drugItem.getMinDoseCount());
        stoTc.setUnitTc(drugItem.getMinPackingName());
        stoTc.setFgActive("1");
        stoTc.setIdDept(idDept);
        stoTc.setIdOrg(orgId);
        stoTc.setOrgId(orgId);
        stoTc.setOrgName(orgName);
        stoTc.setSdTcManage("简易管理");
        stoTc.setCreateBy(userName);
        stoTc.setCreateTime(LocalDateTime.now());
        stoTc.setUpdateBy(userName);
        stoTc.setUpdateTime(LocalDateTime.now());
        stoTc.setDelFlag("0");
        return stoTc;
    }


    /**
     * 获取或创建发药单
     */
    private YsfStoDps getOrCreateStoDps(String outPresId, TraceabilityUploadDto.PrescriptionItem prescription,
                                        String userName, String orgId, String orgName, TraceabilityUploadDto request) {
        // 查询是否存在发药单
        LambdaQueryWrapper<YsfStoDps> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(YsfStoDps::getCfxh, outPresId)
                .eq(YsfStoDps::getDelFlag, "0");
        YsfStoDps stoDps = ysfStoDpsService.getOne(queryWrapper);

        if (stoDps == null) {
            // 创建新的发药单
            stoDps = new YsfStoDps();
            stoDps.setCfxh(outPresId);
            stoDps.setSdDps(request.getSdDps()); // 处方单
            stoDps.setPatientId(prescription.getPatId());
            stoDps.setPsnName(prescription.getPatName());
            stoDps.setCardNo(prescription.getCardNo());
            stoDps.setFgStatus(DispenseOrderStatusEnum.PENDING.getCode()); // 待发药
            stoDps.setFgDps("0"); // 发药单
            stoDps.setFgPrint("0"); // 未打印


            // 设置机构信息
            stoDps.setIdOrg(orgId);
            stoDps.setOrgId(orgId);
            stoDps.setOrgName(orgName);

            // 设置创建和修改信息
            stoDps.setCreateBy(userName);
            stoDps.setCreateTime(LocalDateTime.now());
            stoDps.setUpdateBy(userName);
            stoDps.setUpdateTime(LocalDateTime.now());
            stoDps.setDelFlag("0");

            // 设置发药窗口和工作人员信息
            stoDps.setIdDept(prescription.getIdDept());
            stoDps.setWindow(request.getWindow());
            stoDps.setSelRetnOpterId(request.getSelRetnOpterId());
            stoDps.setPatWardId(request.getPatWardId());
            stoDps.setPatWardName(request.getPatWardName());
        }

        return stoDps;
    }

    /**
     * 获取或创建扫码任务
     */
    private YsfStoTcTask getOrCreateTcTask(String outPresId, YsfStoDps stoDps,
                                           String userId, String userName, String orgId, String orgName) {
        YsfStoTcTask tcTask = null;

        // 如果发药单已关联任务ID，则查询任务
        if (stoDps.getIdTask() != null) {
            LambdaQueryWrapper<YsfStoTcTask> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(YsfStoTcTask::getIdTask, stoDps.getIdTask())
                    .eq(YsfStoTcTask::getDelFlag, "0");
            tcTask = ysfStoTcTaskMapper.selectOne(queryWrapper);

            // 如果任务存在且状态为待处理，直接复用
            if (tcTask != null && TaskStatusEnum.PENDING.getCode()
                    .equals(tcTask.getFgStatus())) {
                return tcTask;
            }
        }

        // 查询是否有其他待处理任务
        LambdaQueryWrapper<YsfStoTcTask> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(YsfStoTcTask::getCdBiz, outPresId)
                .eq(YsfStoTcTask::getFgStatus, TaskStatusEnum.PENDING.getCode()) // 待处理
                .eq(YsfStoTcTask::getDelFlag, "0")
                .orderByDesc(YsfStoTcTask::getCreateTime);
        tcTask = ysfStoTcTaskMapper.selectOne(queryWrapper);

        if (tcTask == null) {
            // 创建新的扫码任务
            tcTask = new YsfStoTcTask();
            tcTask.setCdBiz(outPresId);
            tcTask.setSdTaskType("1"); // 主动任务
            tcTask.setSdTcStatus(SdTcStatusEnum.OUTPATIENT_DISPENSING.getCode()); // 门诊发药
            tcTask.setFgStatus(TaskStatusEnum.PENDING.getCode()); // 待处理
            tcTask.setFgPriority("1"); // 普通优先级
            tcTask.setIdUser(userId);
            tcTask.setIdDept(stoDps.getIdDept());

            // 设置机构信息
            tcTask.setIdOrg(orgId);
            tcTask.setOrgId(orgId);
            tcTask.setOrgName(orgName);

            // 设置创建和修改信息
            tcTask.setCreateBy(userName);
            tcTask.setCreateTime(LocalDateTime.now());
            tcTask.setUpdateBy(userName);
            tcTask.setUpdateTime(LocalDateTime.now());
            tcTask.setDelFlag("0");
            tcTask.setRemark(stoDps.getPsnName());
        }

        return tcTask;
    }


    /**
     * 获取或创建任务明细
     */
    private YsfStoTcTaskSub getOrCreateTcTaskSub(YsfStoTcTask tcTask, TraceabilityUploadDto.DrugItem drugItem,
                                                 String userName, String orgId, String orgName) {
        // 查询是否存在任务明细
        YsfStoTcTaskSub tcTaskSub = ysfStoTcTaskSubService.getByTaskIdAndCfmxxh(
                tcTask.getIdTask()
                        .toString(), drugItem.getOutPresdetailid());

        if (tcTaskSub == null) {
            // 创建新的任务明细
            tcTaskSub = new YsfStoTcTaskSub();
            tcTaskSub.setIdTask(tcTask.getIdTask()
                    .toString());
            tcTaskSub.setCfmxxh(drugItem.getOutPresdetailid());
            tcTaskSub.setDrugCode(drugItem.getDrugCode());
            tcTaskSub.setFgScanned("0"); // 未扫码
            tcTaskSub.setDrugtracinfo(drugItem.getDrugtracinfoScanned());
            tcTaskSub.setQuantity(drugItem.getQuantity());
            tcTaskSub.setUnit(drugItem.getUnit());

            // 设置机构信息
            tcTaskSub.setIdOrg(orgId);
            tcTaskSub.setOrgId(orgId);
            tcTaskSub.setOrgName(orgName);

            // 设置创建和修改信息
            tcTaskSub.setCreateBy(userName);
            tcTaskSub.setCreateTime(LocalDateTime.now());
            tcTaskSub.setUpdateBy(userName);
            tcTaskSub.setUpdateTime(LocalDateTime.now());
            tcTaskSub.setDelFlag("0");
            tcTaskSub.setRemark(tcTask.getRemark());

            // 插入数据库
            ysfStoTcTaskSubService.save(tcTaskSub);
        }

        return tcTaskSub;
    }

    /**
     * 获取或创建发药单明细
     */
    private YsfStoDpsSub getOrCreateStoDpsSub(YsfStoDps stoDps, TraceabilityUploadDto.DrugItem drugItem,
                                              String userId, String userName, String orgId, String orgName) {
        // 查询是否存在发药单明细
        LambdaQueryWrapper<YsfStoDpsSub> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(YsfStoDpsSub::getIdDps, stoDps.getIdDps())
                .eq(YsfStoDpsSub::getCfmxxh, drugItem.getOutPresdetailid())
                .eq(YsfStoDpsSub::getDelFlag, "0");
        YsfStoDpsSub stoDpsSub = ysfStoDpsSubService.getOne(queryWrapper);

        if (stoDpsSub == null) {
            // 创建新的发药单明细
            stoDpsSub = new YsfStoDpsSub();
            stoDpsSub.setIdDps(stoDps.getIdDps()
                    .toString());
            stoDpsSub.setCfmxxh(drugItem.getOutPresdetailid());
            stoDpsSub.setCfxh(stoDps.getCfxh());
            stoDpsSub.setDrugCode(drugItem.getDrugCode());
            stoDpsSub.setNaFee(drugItem.getDrugName());
            stoDpsSub.setPriceSale(drugItem.getPrice() != null ? BigDecimal.valueOf(drugItem.getPrice()) : null);
            stoDpsSub.setSelRetnCnt(drugItem.getQuantity());
            stoDpsSub.setAmtTotal(drugItem.getAmount() != null ? BigDecimal.valueOf(drugItem.getAmount()) : null);
            stoDpsSub.setAmtTotalDps(drugItem.getAmount() != null ? BigDecimal.valueOf(drugItem.getAmount()) : null);
            stoDpsSub.setUnitSale(drugItem.getUnit());
            stoDpsSub.setUnitSaleFactor(drugItem.getMinDoseCount());
            stoDpsSub.setQuantity(drugItem.getQuantity());
            stoDpsSub.setUnit(drugItem.getUnit());
            stoDpsSub.setDrugtracinfo(drugItem.getDrugtracinfoScanned());

            // 设置机构信息
            stoDpsSub.setIdOrg(orgId);
            stoDpsSub.setOrgId(orgId);
            stoDpsSub.setOrgName(orgName);

            // 设置创建和修改信息
            stoDpsSub.setCreateBy(userName);
            stoDpsSub.setCreateTime(LocalDateTime.now());
            stoDpsSub.setUpdateBy(userName);
            stoDpsSub.setUpdateTime(LocalDateTime.now());
            stoDpsSub.setDelFlag("0");

            // 插入数据库
            ysfStoDpsSubService.save(stoDpsSub);
        }

        return stoDpsSub;
    }


    /**
     * 取消扫码任务
     *
     * @param taskId 任务id
     * @return {@link ApiResult }
     */
    @Transactional(rollbackFor = Exception.class)
    public ApiResult<String> cancelTask(Long taskId) {
        // 查询ysf_sto_tc_task记录，确认状态为'0'(待处理)
        YsfStoTcTask task = ysfStoTcTaskService.getById(taskId);
        if (task == null) {
            return ApiResult.error("任务不存在");
        }

        if (!StoTcTaskStatusEnum.PENDING.getCode()
                .equals(task.getFgStatus())) {
            return ApiResult.error("只能取消待处理状态的任务");
        }

        // 更新ysf_sto_tc_task状态为'2'(已失效)，并添加备注
        task.setFgStatus(StoTcTaskStatusEnum.EXPIRED.getCode());
        task.setMemo("操作员取消，发药前病人未取药");
        ysfStoTcTaskService.updateById(task);

        // 处理已扫描的追溯码ysf_sto_tc
        // 根据被取消的id_task，查询ysf_sto_tc_task_sub获取所有已扫描的drugtracinfo
        LambdaQueryWrapper<YsfStoTcTaskSub> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(YsfStoTcTaskSub::getIdTask, taskId.toString())
                .eq(YsfStoTcTaskSub::getFgScanned, "1")  // 只处理已扫描的明细
                .eq(YsfStoTcTaskSub::getDelFlag, "0");   // 未删除的记录
        List<YsfStoTcTaskSub> taskSubList = ysfStoTcTaskSubService.list(queryWrapper);

        if (!taskSubList.isEmpty()) {
            for (YsfStoTcTaskSub taskSub : taskSubList) {
                String drugtracinfo = taskSub.getDrugtracinfo();

                // 如果追溯码为空，跳过处理
                if (StringUtils.isEmpty(drugtracinfo)) {
                    log.warn("任务明细[{}]的追溯码为空，跳过处理", taskSub.getIdSub());
                    continue;
                }

                // 如果drugtracinfo包含逗号，表示有多个追溯码
                if (drugtracinfo.contains(",")) {
                    String[] arr = drugtracinfo.split(",");
                    for (String zsm : arr) {
                        if (StringUtils.isNotEmpty(zsm.trim())) {
                            this.restoreData(taskSub, zsm.trim());
                        }
                    }
                } else {
                    // 只有单个追溯码时才处理
                    this.restoreData(taskSub, drugtracinfo);
                }
            }
        }

        return ApiResult.success("取消任务成功");
    }

    private void restoreData(YsfStoTcTaskSub taskSub, String drugtracinfo) {
        // 参数校验
        if (StringUtils.isEmpty(drugtracinfo)) {
            log.warn("恢复追溯码时传入的追溯码为空");
            return;
        }

        // 将追溯码可用数量恢复为其包装数量
        LambdaQueryWrapper<YsfStoTc> tcWrapper = new LambdaQueryWrapper<>();
        tcWrapper.eq(YsfStoTc::getDrugtracinfo, drugtracinfo)
                .eq(YsfStoTc::getDelFlag, "0");
        YsfStoTc stoTc = ysfStoTcService.getOne(tcWrapper);

        if (stoTc != null) {
            // 恢复其tc表包装数量
            Integer unitSaleFactor = stoTc.getUnitSaleFactor();
            if (unitSaleFactor != null) {
                stoTc.setAmountRem(new BigDecimal(unitSaleFactor));
            } else {
                log.warn("追溯码[{}]的包装数量为空，设置默认值1", drugtracinfo);
                stoTc.setAmountRem(BigDecimal.ONE);
            }
            ysfStoTcService.updateById(stoTc);

            // 创建ysf_sto_tc_status记录
            YsfStoTcStatus tcStatus = new YsfStoTcStatus();
            tcStatus.setSdTcStatus(SdTcStatusEnum.CANCEL_TASK.getCode());  // 扫码任务取消
            tcStatus.setSdTcManage(stoTc.getSdTcManage());
            tcStatus.setIdBizOri(taskSub.getIdSub()
                    .toString());  // 关联任务明细ID
            tcStatus.setIdTc(stoTc.getIdTc());
            tcStatus.setCfmxxh(taskSub.getCfmxxh());
            tcStatus.setDrugCode(taskSub.getDrugCode());
            tcStatus.setDrugtracinfo(drugtracinfo);
            tcStatus.setIdStoInv(stoTc.getIdStoInv());
            tcStatus.setIdDept(stoTc.getIdDept());
            tcStatus.setSdTc(stoTc.getSdTc());
            tcStatus.setSelRetnCnt(unitSaleFactor != null ? unitSaleFactor : 1);  // 恢复的数量
            tcStatus.setFgActive("1");
            tcStatus.setIdOrg(stoTc.getIdOrg());
            tcStatus.setOrgId(stoTc.getOrgId());
            tcStatus.setOrgName(stoTc.getOrgName());
            tcStatus.setSdTc("2");

            ysfStoTcStatusService.save(tcStatus);
        } else {
            log.warn("无法找到追溯码[{}]的记录，跳过恢复操作", drugtracinfo);
        }
    }


    /**
     * 根据条件查询发药单列表 (分页)
     *
     * @param queryDto 查询参数
     * @return 分页后的发药单列表, 包含 DispensingRecordVo 对象
     */
    public TableDataInfo queryDispensingRecords(DispensingRecordQueryDto queryDto) {
        // 1. 设置分页参数
        int pageNum = queryDto.getPageNum() == null ? 1 : queryDto.getPageNum();
        int pageSize = queryDto.getPageSize() == null ? 10 : queryDto.getPageSize();
        Page<YsfStoDps> page = new Page<>(pageNum, pageSize);

        // 2. 构建查询条件 (MyBatis LambdaQueryWrapper)
        LambdaQueryWrapper<YsfStoDps> dpsQueryWrapper = new LambdaQueryWrapper<>();
        if (StringUtils.isNotEmpty(queryDto.getPatientName())) {
            dpsQueryWrapper.like(YsfStoDps::getPsnName, queryDto.getPatientName());
        }
        if (StringUtils.isNotEmpty(queryDto.getPrescriptionId())) {
            // 根据需求文档，处方号可以对应 outPresId 或 cfxh。
            // YsfStoDps 实体中似乎只有 cfxh。如果 outPresId 是另一个表的字段或不同含义，需确认。
            // 这里我们假设 DTO 中的 prescriptionId 直接对应 YsfStoDps 的 cfxh。
            dpsQueryWrapper.eq(YsfStoDps::getCfxh, queryDto.getPrescriptionId());
        }
        if (StringUtils.isNotEmpty(queryDto.getDispensingStatus())) {
            dpsQueryWrapper.eq(YsfStoDps::getFgStatus, queryDto.getDispensingStatus());
        }
        if (StringUtils.isNotEmpty(queryDto.getDeptId())) {
            dpsQueryWrapper.eq(YsfStoDps::getIdDept, queryDto.getDeptId());
        }
        if (StringUtils.isNotEmpty(queryDto.getPatWardId())) {
            dpsQueryWrapper.eq(YsfStoDps::getPatWardId, queryDto.getPatWardId());
        }
        if (StringUtils.isNotEmpty(queryDto.getSdDps())) {
            dpsQueryWrapper.eq(YsfStoDps::getSdDps, queryDto.getSdDps());
        }
        if (queryDto.getStartTime() != null) {
            dpsQueryWrapper.ge(YsfStoDps::getSendTime, queryDto.getStartTime());
        }
        if (queryDto.getEndTime() != null) {
            // 对于日期范围查询，通常结束时间应该包含当天，所以可以用小于第二天的开始
            // 或者数据库层面处理 DATE(send_time) <= DATE(queryDto.getEndTime())
            // 为简单起见，这里用 le，如果需要精确到秒，前端传入的时间应为当天的 23:59:59
            dpsQueryWrapper.le(YsfStoDps::getSendTime, queryDto.getEndTime());
        }
        dpsQueryWrapper.eq(YsfStoDps::getFgDps, queryDto.getFgDps());
        dpsQueryWrapper.eq(YsfStoDps::getDelFlag, "0"); // 通常查询未删除的记录
        dpsQueryWrapper.orderByDesc(YsfStoDps::getSendTime, YsfStoDps::getIdDps); // 按发药时间降序, ID降序作为次排序

        // 3. 执行分页查询
        IPage<YsfStoDps> pageResult = ysfStoDpsService.page(page, dpsQueryWrapper);

        // 4. 如果查询结果为空，直接返回
        if (pageResult.getRecords()
                .isEmpty()) {
            return new TableDataInfo(Collections.emptyList(), 0);
        }

        // 5. 遍历查询结果，对每个 YsfStoDps 对象进行处理和转换
        // 5. 批量查询任务映射，避免 N+1 查询问题
        List<String> cfxhList = pageResult.getRecords().stream()
                .map(YsfStoDps::getCfxh)
                .filter(StringUtils::isNotEmpty)
                .collect(Collectors.toList());

        Map<String, YsfStoTcTask> taskMap;
        if (!cfxhList.isEmpty()) {
            List<YsfStoTcTask> taskList = ysfStoTcTaskMapper.selectLatestByCfxh(cfxhList);
            taskMap = taskList.stream()
                    .collect(Collectors.toMap(YsfStoTcTask::getCdBiz, task -> task, (o, n) -> n));
        } else {
            taskMap = new HashMap<>();
        }

        List<DispensingRecordVo> voList = pageResult.getRecords()
                .stream()
                .map(dps -> {

                    DispensingRecordVo vo = new DispensingRecordVo();

                    BeanUtils.copyProperties(dps, vo);

                    vo.setDispensingFgStatus(dps.getFgStatus());

                    if (StringUtils.isNotEmpty(dps.getCfxh()) && taskMap.containsKey(dps.getCfxh())) {
                        YsfStoTcTask latestTask = taskMap.get(dps.getCfxh());
                        vo.setTaskStatus(latestTask.getFgStatus());
                        vo.setTaskId(latestTask.getIdTask());
                    }

                    return vo;
                })
                .collect(Collectors.toList());

        // 6. 返回分页结果
        return new TableDataInfo(voList, (int) pageResult.getTotal());
    }

    /**
     * 根据条件查询发药单明细列表 (分页)
     *
     * @param queryDto 查询参数
     * @return 分页后的发药单明细列表
     */
    public TableDataInfo queryDispensingRecordDetails(DispensingRecordDetailQueryDto queryDto) {
        // 验证必要参数：cfxh 和 idDps 不能都为空
        if (StringUtils.isEmpty(queryDto.getCfxh()) && queryDto.getIdDps() == null) {
            throw new BusinessException("处方序号(cfxh)和发药单ID(idDps)不能都为空");
        }

        // 1. 设置分页参数
        int pageNum = queryDto.getPageNum() == null ? 1 : queryDto.getPageNum();
        int pageSize = queryDto.getPageSize() == null ? 10 : queryDto.getPageSize();
        Page<YsfStoDpsSub> page = new Page<>(pageNum, pageSize);

        // 2. 构建查询条件
        LambdaQueryWrapper<YsfStoDpsSub> queryWrapper = new LambdaQueryWrapper<>();

        // 处方序号条件
        if (StringUtils.isNotEmpty(queryDto.getCfxh())) {
            queryWrapper.eq(YsfStoDpsSub::getCfxh, queryDto.getCfxh());
        }

        // 发药单ID条件
        if (queryDto.getIdDps() != null) {
            queryWrapper.eq(YsfStoDpsSub::getIdDps, queryDto.getIdDps()
                    .toString());
        }

        // 患者姓名条件（模糊匹配）
        if (StringUtils.isNotEmpty(queryDto.getPatientName())) {
            // 需要关联发药单表查询患者姓名，这里先跳过，或者可以通过子查询实现
            // queryWrapper.like(YsfStoDpsSub::getPatientName, queryDto.getPatientName());
        }

        // 药品名称或编码条件（模糊匹配）
        if (StringUtils.isNotEmpty(queryDto.getDrugNameOrCode())) {
            queryWrapper.and(wrapper -> wrapper
                    .like(YsfStoDpsSub::getNaFee, queryDto.getDrugNameOrCode())
                    .or()
                    .like(YsfStoDpsSub::getDrugCode, queryDto.getDrugNameOrCode())
            );
        }

        queryWrapper.eq(YsfStoDpsSub::getDelFlag, "0"); // 查询未删除的记录
        queryWrapper.orderByAsc(YsfStoDpsSub::getCfmxxh); // 按处方明细序号排序

        // 3. 执行分页查询
        IPage<YsfStoDpsSub> pageResult = ysfStoDpsSubService.page(page, queryWrapper);

        List<YsfStoDpsSub> subList = pageResult.getRecords();
        if (subList.isEmpty()) {
            return new TableDataInfo(Collections.emptyList(), 0);
        }


        // 查询当前活动任务
        LambdaQueryWrapper<YsfStoTcTask> taskQuery = new LambdaQueryWrapper<>();
        // 获取发药单的处方号
        String cfxh = "";
        if (!subList.isEmpty()) {
            cfxh = subList.get(0)
                    .getCfxh();
        }

        taskQuery.eq(YsfStoTcTask::getCdBiz, cfxh)
                .eq(YsfStoTcTask::getDelFlag, "0")
                .eq(YsfStoTcTask::getFgStatus, TaskStatusEnum.PENDING.getCode()) // 待处理的任务
                .orderByDesc(YsfStoTcTask::getCreateTime)
                .last("LIMIT 1");

        YsfStoTcTask currentTask = ysfStoTcTaskMapper.selectOne(taskQuery);

        // 转换为VO对象
        List<DispensingRecordDetailVo> voList = subList.stream()
                .map(sub -> {
                    DispensingRecordDetailVo vo = new DispensingRecordDetailVo();
                    BeanUtils.copyProperties(sub, vo);

                    // 设置任务明细信息
                    YsfStoTcTaskSub taskSub = ysfStoTcTaskSubService.lambdaQuery()
                            .eq(YsfStoTcTaskSub::getCfmxxh, sub.getCfmxxh())
                            .one();
                    if (taskSub != null) {
                        vo.setIdSub(taskSub.getIdSub());
                        vo.setFgScanned(taskSub.getFgScanned());
                    } else {
                        vo.setFgScanned("0"); // 默认未扫码
                    }

                    return vo;
                })
                .collect(Collectors.toList());

        // 如果指定了是否已采集条件，但没有活动任务，则根据条件过滤结果
        if (StringUtils.isNotEmpty(queryDto.getIsCollected()) && currentTask == null) {
            if ("1".equals(queryDto.getIsCollected())) {
                // 如果查询已采集但没有任务，返回空列表
                return new TableDataInfo(Collections.emptyList(), 0);
            }
        }

        return new TableDataInfo(voList, (int) pageResult.getTotal());
    }
}
