-- 创建数据库（如果不存在）
-- CREATE DATABASE IF NOT EXISTS `fuyangrenyi` DEFAULT;
-- USE `fuyangrenyi`;

DROP TABLE IF EXISTS `nhsa3505_ymf_user_count`;
CREATE TABLE `nhsa3505_ymf_user_count` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `medical_code` varchar(200) DEFAULT '' COMMENT '医疗机构编码',
  `medical_name` varchar(200) DEFAULT '' COMMENT '医疗机构',
  `count_date` varchar(200) DEFAULT '' COMMENT '统计日期',
  `total_count` int DEFAULT '0' COMMENT 'cnt总数量',
  `trace_code_count` int DEFAULT '0' COMMENT '追溯码数量',
  `all_cnt_count` int DEFAULT '0' COMMENT '所有数据cnt数量,以及没有追溯码的',
  `all_data_number` int DEFAULT '0' COMMENT '所有数据条数',
  `trace_code_data_number` int DEFAULT '0' COMMENT '所有含追溯码的数据条数',
  `ymf_user_id` bigint DEFAULT NULL COMMENT 'ymf用户ID',
  `ymf_user_name` varchar(30) DEFAULT NULL COMMENT 'ymf用户账号',
  `ymf_nick_name` varchar(30) DEFAULT NULL COMMENT 'ymf用户昵称',
  `create_by` varchar(64) DEFAULT '' COMMENT '创建者',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_by` varchar(64) DEFAULT '' COMMENT '更新者',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注',
  `delete_flag` char(1) DEFAULT '0' COMMENT '删除标志（0代表正常,1代表删除）',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE KEY `uk` (`medical_code`,`count_date`,`ymf_user_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci ROW_FORMAT=DYNAMIC COMMENT='医秒付用户维度销售数据3505采集数量';

-- ----------------------------
-- Table structure for nhsa_3505
-- ----------------------------
DROP TABLE IF EXISTS `nhsa_3505`;
CREATE TABLE `nhsa_3505`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `medical_code` varchar(200) NULL DEFAULT '' COMMENT '医疗机构编码',
  `medical_name` varchar(200) NULL DEFAULT '' COMMENT '医疗机构',
  `med_list_codg` varchar(50) NULL DEFAULT NULL COMMENT '医疗目录编码',
  `fixmedins_hilist_id` varchar(30) NULL DEFAULT NULL COMMENT '定点医药机构目录编号',
  `fixmedins_hilist_name` varchar(200) NULL DEFAULT NULL COMMENT '定点医药计够目录名称',
  `fixmedins_bchno` varchar(30) NULL DEFAULT NULL COMMENT '定点医药机构批次流水号',
  `prsc_dr_cert_type` varchar(6) NULL DEFAULT NULL COMMENT '开方医师证件类型',
  `prsc_dr_certno` varchar(50) NULL DEFAULT NULL COMMENT '开方医师证件号码',
  `prsc_dr_name` varchar(50) NULL DEFAULT NULL COMMENT '开方医师证件姓名',
  `phar_cert_type` varchar(6) NULL DEFAULT NULL COMMENT '药师证件类型',
  `phar_certno` varchar(50) NULL DEFAULT NULL COMMENT '药师证件号码',
  `phar_name` varchar(50) NULL DEFAULT NULL COMMENT '药师姓名',
  `phar_prac_cert_no` varchar(50) NULL DEFAULT NULL COMMENT '药师执业资格证号',
  `hi_feesetl_type` varchar(6) NULL DEFAULT NULL COMMENT '医保费用结算类型',
  `setl_id` varchar(30) NULL DEFAULT NULL COMMENT '结算ID',
  `mdtrt_sn` varchar(30) NULL DEFAULT '-' COMMENT '就医流水号',
  `psn_no` varchar(30) NULL DEFAULT NULL COMMENT '人员编号',
  `psn_cert_type` varchar(6) NULL DEFAULT NULL COMMENT '人员证件类型',
  `certno` varchar(50) NULL DEFAULT NULL COMMENT '证件号码',
  `psn_name` varchar(50) NULL DEFAULT NULL COMMENT '人员姓名',
  `manu_lotnum` varchar(30) NULL DEFAULT NULL COMMENT '生产批号',
  `manu_date` date NULL DEFAULT NULL COMMENT '生产日期',
  `expy_end` date NULL DEFAULT NULL COMMENT '有效期止',
  `rx_flag` varchar(3) NULL DEFAULT NULL COMMENT '处方药标志',
  `trdn_flag` varchar(3) NULL DEFAULT NULL COMMENT '拆零标识',
  `finl_trns_pric` decimal(16, 6) NULL DEFAULT NULL COMMENT '最终成交单价',
  `rxno` varchar(40) NULL DEFAULT NULL COMMENT '处方号',
  `rx_circ_flag` varchar(3) NULL DEFAULT NULL COMMENT '外购处方标志',
  `rtal_docno` varchar(40) NULL DEFAULT NULL COMMENT '零售单据号',
  `stoout_no` varchar(40) NULL DEFAULT NULL COMMENT '销售出库单据号',
  `bchno` varchar(30) NULL DEFAULT NULL COMMENT '批次号',
  `drug_prod_barc` varchar(30) NULL DEFAULT NULL COMMENT '药品条形码',
  `shelf_posi` varchar(20) NULL DEFAULT NULL COMMENT '货架位',
  `sel_retn_cnt` decimal(16, 4) NULL DEFAULT NULL COMMENT '销售/退货数量',
  `sel_retn_time` datetime NULL DEFAULT NULL COMMENT '销售/退货时间',
  `sel_retn_opter_name` varchar(50) NULL DEFAULT NULL COMMENT '销售/退货经办人姓名',
  `mdtrt_setl_type` varchar(50) NULL DEFAULT NULL COMMENT '就诊结算类型',
  `memo` varchar(500) NULL DEFAULT NULL COMMENT '备注',
  `drug_trac_info` text NULL COMMENT '溯源码节点信息',
  `third_id` varchar(100) NULL DEFAULT NULL COMMENT '第三方业务主键',
  `exp_content` varchar(4000) NULL DEFAULT NULL COMMENT '异常内容',
  `his_drug_id` varchar(100) NULL DEFAULT NULL COMMENT 'HIS药品ID',
  `his_entp_code` varchar(100) NULL DEFAULT NULL COMMENT 'HIS企业编码',
  `his_entp_name` varchar(100) NULL DEFAULT NULL COMMENT 'HIS企业名称',
  `request_data` json NULL COMMENT '请求数据',
  `response_data` json NULL COMMENT '响应数据',
  `hsa_sync_status` varchar(10) NULL DEFAULT '0' COMMENT '两定接口同步状态：0未同步，1已同步',
  `hsa_sync_time` datetime NULL DEFAULT NULL COMMENT '两定接口同步状态时间',
  `hsa_sync_remark` varchar(500) NULL DEFAULT NULL COMMENT '两定接口同步备注',
  `hsa3513_status` varchar(10) NULL DEFAULT '0' COMMENT 'HSA3513状态',
  `delete_flag` char(1) NULL DEFAULT '0' COMMENT '删除标志（0代表正常,1代表删除）',
  `create_by` varchar(64) NULL DEFAULT '' COMMENT '创建者',
  `create_time` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `create_date` date NULL DEFAULT NULL COMMENT '创建日期',
  `update_by` varchar(64) NULL DEFAULT '' COMMENT '更新者',
  `update_time` datetime NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `remark` varchar(500) NULL DEFAULT NULL COMMENT '备注',
  `ymf_user_id` bigint NULL DEFAULT NULL COMMENT '云闪付用户ID',
  `ymf_user_name` varchar(30) NULL DEFAULT NULL COMMENT '云闪付用户名',
  `ymf_nick_name` varchar(30) NULL DEFAULT NULL COMMENT '云闪付昵称',
  `sjh` varchar(50) NULL DEFAULT NULL COMMENT '手机号',
  `cfxh` varchar(50) NULL DEFAULT NULL COMMENT '处方序号',
  `cfmxxh` varchar(50) NULL DEFAULT NULL COMMENT '处方明细序号',
  `patient_id` varchar(50) NULL DEFAULT NULL COMMENT '患者ID',
  `pat_card_no` varchar(50) NULL DEFAULT NULL COMMENT '患者卡号',
  `return_status` varchar(10) NULL DEFAULT '0' COMMENT '退货状态：0未退货，1已退货',
  `return_time` datetime NULL DEFAULT NULL COMMENT '退货时间',
  `return_remark` varchar(500) NULL DEFAULT NULL COMMENT '退货备注',
  `return_drugtracinfo` varchar(500) NULL DEFAULT NULL COMMENT '退货溯源码信息',
  `min_unit_sel_retn_cnt` decimal(20, 2) NULL DEFAULT NULL COMMENT '退货数量',
  `his_unique_key` varchar(100) NULL DEFAULT NULL COMMENT '退货单号',
  `inv_cnt` decimal(20, 2) NULL DEFAULT NULL COMMENT '库存数量',
  `min_dose_count` decimal(20, 2) NULL DEFAULT NULL COMMENT '包装换算系数',
  `material_or_drug` varchar(10) NULL DEFAULT NULL COMMENT '药品材料标识 0:药品 1:材料',
  `composite_key` varchar(100) NULL DEFAULT NULL COMMENT '组合主键',
  `hospital_id` varchar(50) NULL DEFAULT NULL COMMENT '医院ID',
  `feedetl_sn` varchar(50) NULL DEFAULT NULL COMMENT '费用明细序列号',
  `out_pres_id` varchar(50) NULL DEFAULT NULL COMMENT '外部处方ID',
  `org_id` varchar(50) NULL DEFAULT NULL COMMENT '机构ID',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `uk`(`medical_code` ASC, `fixmedins_bchno` ASC) USING BTREE,
  INDEX `idx_medical_code`(`medical_code` ASC) USING BTREE,
  INDEX `idx_sel_retn_time`(`sel_retn_time` ASC) USING BTREE,
  INDEX `idx_medical_code_sel_retn_time`(`medical_code` ASC, `sel_retn_time` ASC) USING BTREE,
  INDEX `idx_fixmedins_bchno`(`fixmedins_bchno` ASC) USING BTREE,
  FULLTEXT INDEX `idx_drugcode`(`drug_trac_info`),
  INDEX `idx_out_pres_id`(`out_pres_id` ASC) USING BTREE,
  INDEX `idx_cfxh`(`cfxh` ASC) USING BTREE,
  INDEX `idx_cfmxxh`(`cfmxxh` ASC) USING BTREE,
  INDEX `idx_create_time`(`create_time` ASC) USING BTREE,
  INDEX `idx_delete_flag`(`delete_flag` ASC) USING BTREE,
  INDEX `idx_his_drug_id`(`his_drug_id` ASC) USING BTREE,
  INDEX `idx_return_status`(`return_status` ASC) USING BTREE,
  INDEX `idx_delete_flag_medical_code_create_time`(`delete_flag` ASC, `medical_code` ASC, `create_time` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '3505销售记录报文表' ROW_FORMAT = Dynamic;

DROP TABLE IF EXISTS `nhsa_3505_cnt_count`;
CREATE TABLE `nhsa_3505_cnt_count` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `medical_code` varchar(200) DEFAULT '' COMMENT '医疗机构编码',
  `medical_name` varchar(200) DEFAULT '' COMMENT '医疗机构',
  `count_date` varchar(200) DEFAULT '' COMMENT '统计日期',
  `total_count` int DEFAULT '0' COMMENT 'cnt总数量',
  `trace_code_count` int DEFAULT '0' COMMENT '追溯码数量',
  `all_cnt_count` int DEFAULT '0' COMMENT '所有数据cnt数量,以及没有追溯码的',
  `all_data_number` int DEFAULT '0' COMMENT '所有数据条数',
  `trace_code_data_number` int DEFAULT '0' COMMENT '所有含追溯码的数据条数',
  `create_by` varchar(64) DEFAULT '' COMMENT '创建者',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_by` varchar(64) DEFAULT '' COMMENT '更新者',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注',
  `delete_flag` char(1) DEFAULT '0' COMMENT '删除标志（0代表正常,1代表删除）',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE KEY `uk` (`medical_code`,`count_date`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci ROW_FORMAT=DYNAMIC COMMENT='销售数据3505采集数量';


DROP TABLE IF EXISTS `nhsa_3506`;
CREATE TABLE `nhsa_3506` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `medical_code` varchar(200) DEFAULT '' COMMENT '医疗机构编码',
  `medical_name` varchar(200) DEFAULT '' COMMENT '医疗机构名称',
  `med_list_codg` varchar(50) DEFAULT NULL COMMENT '医疗目录编码',
  `fixmedins_hilist_id` varchar(30) DEFAULT NULL COMMENT '定点医药机构目录编号',
  `fixmedins_hilist_name` varchar(200) DEFAULT NULL COMMENT '定点医药机构目录名称',
  `fixmedins_bchno` varchar(30) DEFAULT NULL COMMENT '定点医药机构批次流水号',
  `setl_id` varchar(30) DEFAULT NULL COMMENT '结算ID',
  `psn_no` varchar(30) DEFAULT NULL COMMENT '人员编号',
  `psn_cert_type` varchar(6) DEFAULT NULL COMMENT '人员证件类型',
  `certno` varchar(50) DEFAULT NULL COMMENT '证件号码',
  `psn_name` varchar(50) DEFAULT NULL COMMENT '人员姓名',
  `manu_lotnum` varchar(30) DEFAULT NULL COMMENT '生产批号',
  `manu_date` date DEFAULT NULL COMMENT '生产日期',
  `expy_end` date DEFAULT NULL COMMENT '有效期止',
  `rx_flag` varchar(3) DEFAULT NULL COMMENT '处方药标志',
  `trdn_flag` varchar(3) DEFAULT NULL COMMENT '拆零标志',
  `finl_trns_pric` decimal(16,6) DEFAULT NULL COMMENT '最终成交单价',
  `sel_retn_cnt` decimal(16,4) DEFAULT NULL COMMENT '销售/退货数量',
  `sel_retn_time` datetime DEFAULT NULL COMMENT '销售/退货时间',
  `sel_retn_opter_name` varchar(50) DEFAULT NULL COMMENT '销售/退货经办人姓名',
  `memo` varchar(500) DEFAULT NULL COMMENT '备注',
  `medins_prod_sel_no` varchar(50) DEFAULT NULL COMMENT '商品销售流水号',
  `mdtrt_sn` varchar(30) DEFAULT NULL COMMENT '就诊流水号',
  `drugtracinfo` text DEFAULT NULL COMMENT '药品追溯码信息',
  `third_id` varchar(100) DEFAULT NULL COMMENT '第三方业务主键',
  `exp_content` varchar(4000) DEFAULT NULL COMMENT '异常内容',
  `his_drug_id` varchar(100) DEFAULT NULL COMMENT 'HIS药品ID',
  `his_entp_code` varchar(100) DEFAULT NULL COMMENT 'HIS企业编码',
  `his_entp_name` varchar(100) DEFAULT NULL COMMENT 'HIS企业名称',
  `request_data` json DEFAULT NULL COMMENT '请求数据',
  `response_data` json DEFAULT NULL COMMENT '响应数据',
  `hsa_sync_status` varchar(10) DEFAULT '0' COMMENT '两定接口同步状态：0未同步，1已同步',
  `hsa_sync_time` datetime DEFAULT NULL COMMENT '两定接口同步状态时间',
  `hsa_sync_remark` varchar(500) DEFAULT NULL COMMENT '两定接口同步备注',
  `create_by` varchar(64) DEFAULT '' COMMENT '创建者',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_by` varchar(64) DEFAULT '' COMMENT '更新者',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注',
  `delete_flag` char(1) DEFAULT '0' COMMENT '删除标志（0代表正常,1代表删除）',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE KEY `uk` (`medical_code`,`fixmedins_bchno`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='3506结算记录报文表' ROW_FORMAT=DYNAMIC;

-- ----------------------------
-- Table structure for ysf_sto_check
-- ----------------------------
DROP TABLE IF EXISTS `ysf_sto_check`;
CREATE TABLE `ysf_sto_check`  (
  `id_sto_check` bigint NOT NULL AUTO_INCREMENT COMMENT '盘点主键',
  `id_sto` char(24) NULL DEFAULT NULL COMMENT '仓储主键',
  `cd_sto_check` varchar(32) NULL DEFAULT NULL COMMENT '盘点单号',
  `dt_check_begin` datetime NULL DEFAULT NULL COMMENT '盘点开始时间',
  `dt_check_end` datetime NULL DEFAULT NULL COMMENT '盘点结束时间',
  `fg_sto_check` varchar(4) NULL DEFAULT '0' COMMENT '盘点状态;0: 盘点中, 1:完成盘点, 9: 盘点作废',
  `sd_pol` varchar(4) NULL DEFAULT NULL COMMENT '盈亏状态;-1: 盘亏,0:正常,1:盘盈',
  `sd_check` varchar(4) NULL DEFAULT NULL COMMENT '盘点类型;1:库存,2:养护',
  `des_sto_check` varchar(255) NULL DEFAULT NULL COMMENT '盘点信息;盘点摘要信息',
  `des_invalid` varchar(255) NULL DEFAULT NULL COMMENT '作废原因',
  `id_org` char(24) NULL DEFAULT NULL COMMENT '机构编号',
  `org_id` varchar(32) NULL DEFAULT NULL COMMENT '医疗机构编码',
  `org_name` varchar(32) NULL DEFAULT NULL COMMENT '医疗机构名称',
  `revision` char(24) NULL DEFAULT NULL COMMENT '乐观锁',
  `create_by` varchar(64) NULL DEFAULT '' COMMENT '创建者',
  `create_time` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_by` varchar(64) NULL DEFAULT '' COMMENT '更新者',
  `update_time` datetime NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `remark` varchar(500) NULL DEFAULT NULL COMMENT '备注',
  `del_flag` char(1) NULL DEFAULT '0' COMMENT '删除标志（0代表存在 1代表删除）',
  PRIMARY KEY (`id_sto_check`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '仓储盘点' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for ysf_sto_check_sub
-- ----------------------------
DROP TABLE IF EXISTS `ysf_sto_check_sub`;
CREATE TABLE `ysf_sto_check_sub`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '盘点明细主键',
  `id_sto_check` char(24) NULL DEFAULT NULL COMMENT '盘点主键',
  `id_med_pro` char(24) NULL DEFAULT NULL COMMENT '药品主键',
  `id_sto_inv` char(24) NULL DEFAULT NULL COMMENT '库存主键;为空表示新增数据',
  `manu_lotnum` varchar(32) NULL DEFAULT NULL COMMENT '药品批号',
  `expy_end` datetime NULL DEFAULT NULL COMMENT '药品效期',
  `price_sale` decimal(24, 6) NULL DEFAULT NULL COMMENT '零售价格',
  `price_pur` decimal(24, 6) NULL DEFAULT NULL COMMENT '进货价格;新增库存时必填,自动新增时从库存查询',
  `amt_check_bgn` int NULL DEFAULT NULL COMMENT '盘前数量',
  `amt_check_end` int NULL DEFAULT NULL COMMENT '实盘数量',
  `amt_change` int NULL DEFAULT NULL COMMENT '变动数量',
  `sd_reason` varchar(4) NULL DEFAULT NULL COMMENT '养护原因;1.次品,2.伪劣,3.破损,4.霉变',
  `id_org` char(24) NULL DEFAULT NULL COMMENT '机构编号',
  `org_id` varchar(32) NULL DEFAULT NULL COMMENT '医疗机构编码',
  `org_name` varchar(32) NULL DEFAULT NULL COMMENT '医疗机构名称',
  `revision` char(24) NULL DEFAULT NULL COMMENT '乐观锁',
  `create_by` varchar(64) NULL DEFAULT '' COMMENT '创建者',
  `create_time` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_by` varchar(64) NULL DEFAULT '' COMMENT '更新者',
  `update_time` datetime NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `remark` varchar(500) NULL DEFAULT NULL COMMENT '备注',
  `del_flag` char(1) NULL DEFAULT '0' COMMENT '删除标志（0代表存在 1代表删除）',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '仓储盘点明细' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for ysf_sto_dept
-- ----------------------------
DROP TABLE IF EXISTS `ysf_sto_dept`;
CREATE TABLE `ysf_sto_dept`  (
  `id_sto` bigint NOT NULL AUTO_INCREMENT COMMENT '仓储主键',
  `sd_sto` varchar(4) NOT NULL COMMENT '仓储类型;phis.sto.stoType:1:药库,2:药房,3:库房,4科室库房',
  `sds_sto_pro` varchar(32) NOT NULL COMMENT '仓储物品类型;rbmh.base.med.articleType,1西药、2中成药、3草药、4疫苗、5耗材、6固定资产,7.民族药品,8.院内制剂',
  `sd_sto_scene` varchar(4) NOT NULL COMMENT '仓储应用场景;phis.sto.stoScene:1:门诊,2:住院,9:全部',
  `na_sto` varchar(90) NOT NULL COMMENT '仓储名称',
  `id_dept` varchar(24) NOT NULL COMMENT '部门编码;bbp部门',
  `sd_checkway` varchar(4) NOT NULL COMMENT '盘点处理方式;phis.sto.checkway:1.生成出入库单,2直接增减库存',
  `sd_packagway` varchar(4) NOT NULL COMMENT '包装管理方式;phis.sto.packagway:1.默认大包装,2默认小包装',
  `fg_active` varchar(1) NOT NULL COMMENT '有效标识;0: 失效, 1:有效',
  `fg_cloud` varchar(1) NULL DEFAULT NULL COMMENT '云药房;允许任意机构开方',
  `sds_order` varchar(32) NULL DEFAULT NULL COMMENT '出库顺序;phis.sto.orderType,多选',
  `sds_special` varchar(32) NULL DEFAULT NULL COMMENT '特殊标志;phis.sto.specialType,多选',
  `id_org` varchar(24) NOT NULL COMMENT '机构编号',
  `org_id` varchar(32) NULL DEFAULT NULL COMMENT '医疗机构编码',
  `org_name` varchar(32) NULL DEFAULT NULL COMMENT '医疗机构名称',
  `revision` varchar(24) NULL DEFAULT NULL COMMENT '乐观锁',
  `create_by` varchar(64) NULL DEFAULT '' COMMENT '创建者',
  `create_time` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_by` varchar(64) NULL DEFAULT '' COMMENT '更新者',
  `update_time` datetime NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `remark` varchar(500) NULL DEFAULT NULL COMMENT '备注',
  `del_flag` char(1) NULL DEFAULT '0' COMMENT '删除标志（0代表存在 1代表删除）',
  PRIMARY KEY (`id_sto`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '仓储定义' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for ysf_sto_dps
-- ----------------------------
DROP TABLE IF EXISTS `ysf_sto_dps`;
CREATE TABLE `ysf_sto_dps`  (
  `id_dps` bigint NOT NULL AUTO_INCREMENT COMMENT '发药单主键',
  `cfxh` varchar(32) NULL DEFAULT NULL COMMENT '发药单号',
  `sd_dps` varchar(4) NULL DEFAULT NULL COMMENT '发药单类型;1: 住院处方, 2:门诊处方',
  `send_time` datetime NULL DEFAULT NULL COMMENT '发药时间',
  `id_pi` varchar(24) NULL DEFAULT NULL COMMENT '患者ID',
  `patient_id` varchar(24) NULL DEFAULT NULL COMMENT '患者ID',
  `psn_name` varchar(32) NULL DEFAULT NULL COMMENT '患者姓名',
  `card_no` varchar(100) NULL DEFAULT NULL COMMENT '就诊卡号',
  `sn_bill` varchar(32) NULL DEFAULT NULL COMMENT '票据号码',
  `id_stl` varchar(24) NULL DEFAULT NULL COMMENT '结算主键',
  `id_pres` varchar(24) NULL DEFAULT NULL COMMENT '处方主键',
  `phar_prac_cert_no` varchar(32) NULL DEFAULT NULL COMMENT '药师执业资格证号',
  `prsc_dr_name` varchar(32) NULL DEFAULT NULL COMMENT '开单医生姓名',
  `id_doc` varchar(24) NULL DEFAULT NULL COMMENT '开单医生;医疗场景',
  `id_sto` varchar(24) NULL DEFAULT NULL COMMENT '仓储ID',
  `id_sto_win` varchar(24) NULL DEFAULT NULL COMMENT '发药窗口ID',
  `fg_status` varchar(1) NULL DEFAULT NULL COMMENT '发药单状态;发药单 0:待发药,1:已发药,7:部分退药，8:全部退药，9:作废  退药单:8:已退药未退费,9:已退费',
  `fg_print` varchar(1) NULL DEFAULT NULL COMMENT '打印标识',
  `id_dept` varchar(24) NULL DEFAULT NULL COMMENT '发药科室id，也就是发药药房的id，由此关联出是门诊药房还是住院药房',
  `sel_retn_opter_name` varchar(24) NULL DEFAULT NULL COMMENT '发药人;退药时作为退药人',
  `user_check` varchar(24) NULL DEFAULT NULL COMMENT '配药人',
  `fg_dps` varchar(1) NULL DEFAULT '0' COMMENT '退药单标记;0: 发药, 1: 退药',
  `ori_id_dps` varchar(24) NULL DEFAULT NULL COMMENT '原发药单主键;用于退药单，原发药单id',
  `id_task` bigint NULL DEFAULT NULL COMMENT '扫码任务主键',
  `id_org` varchar(24) NULL DEFAULT NULL COMMENT '机构编号;开单机构',
  `org_id` varchar(32) NULL DEFAULT NULL COMMENT '医疗机构编码',
  `org_name` varchar(32) NULL DEFAULT NULL COMMENT '医疗机构名称',
  `revision` varchar(24) NULL DEFAULT NULL COMMENT '乐观锁',
  `create_by` varchar(64) NULL DEFAULT '' COMMENT '创建者',
  `create_time` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_by` varchar(64) NULL DEFAULT '' COMMENT '更新者',
  `update_time` datetime NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `remark` varchar(500) NULL DEFAULT NULL COMMENT '备注',
  `del_flag` char(1) NULL DEFAULT '0' COMMENT '删除标志（0代表存在 1代表删除）',
  `window` varchar(24) NULL DEFAULT NULL COMMENT '窗口',
  `sel_retn_opter_id` varchar(24) NULL DEFAULT NULL COMMENT '选择退回操作员ID',
  `pat_ward_id` varchar(24) NULL DEFAULT NULL COMMENT '病区ID',
  `pat_ward_name` varchar(24) NULL DEFAULT NULL COMMENT '病区名称',
  PRIMARY KEY (`id_dps`) USING BTREE,
  INDEX `ysf_sto_dps_cfxh_IDX`(`cfxh` ASC) USING BTREE,
  INDEX `idx_del_flag_cfxh`(`del_flag` ASC, `cfxh` ASC) USING BTREE,
  INDEX `idx_psn_name`(`psn_name` ASC) USING BTREE,
  INDEX `idx_fg_status`(`fg_status` ASC) USING BTREE,
  INDEX `idx_id_dept`(`id_dept` ASC) USING BTREE,
  INDEX `idx_pat_ward_id`(`pat_ward_id` ASC) USING BTREE,
  INDEX `idx_sd_dps`(`sd_dps` ASC) USING BTREE,
  INDEX `idx_send_time`(`send_time` ASC) USING BTREE,
  INDEX `idx_fg_dps_del_flag`(`fg_dps` ASC, `del_flag` ASC) USING BTREE,
  INDEX `idx_send_time_id_dps`(`send_time` ASC, `id_dps` ASC) USING BTREE,
  INDEX `idx_del_flag_fg_status_send_time`(`del_flag` ASC, `fg_status` ASC, `send_time` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '发药单' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for ysf_sto_dps_sub
-- ----------------------------
DROP TABLE IF EXISTS `ysf_sto_dps_sub`;
CREATE TABLE `ysf_sto_dps_sub`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '发药单明细ID',
  `cfmxxh` varchar(32) NULL DEFAULT NULL COMMENT '处方明细序号',
  `ori_cfmxxh` varchar(32) NULL DEFAULT NULL COMMENT '原发药单处方明细序号;用于退药单',
  `ori_id` varchar(24) NULL DEFAULT NULL COMMENT '原发药单明细ID;用于退药单，原发药单明细id',
  `cfxh` varchar(24) NULL DEFAULT NULL COMMENT '发药单序号',
  `id_dps` varchar(24) NULL DEFAULT NULL COMMENT '发药单ID',
  `id_fee` varchar(24) NULL DEFAULT NULL COMMENT '费用明细ID',
  `na_fee` varchar(255) NULL DEFAULT NULL COMMENT '费用名称',
  `drug_code` varchar(255) NULL DEFAULT NULL COMMENT '药品编码',
  `drugtracinfo` text NULL DEFAULT NULL COMMENT '追溯码',
  `price_sale` decimal(24, 6) NULL DEFAULT NULL COMMENT '单价',
  `sel_retn_cnt` int NULL DEFAULT NULL COMMENT '数量',
  `amt_total` decimal(24, 6) NULL DEFAULT NULL COMMENT '金额',
  `amt_total_dps` decimal(24, 6) NULL DEFAULT NULL COMMENT '实发金额',
  `unit_sale` varchar(32) NULL DEFAULT NULL COMMENT '包装单位',
  `unit_sale_factor` int NULL DEFAULT NULL COMMENT '包装系数',
  `amt_total_pur` decimal(24, 6) NULL DEFAULT NULL COMMENT '发药时的进货总价',
  `id_org` varchar(24) NULL DEFAULT NULL COMMENT '机构编号',
  `org_id` varchar(32) NULL DEFAULT NULL COMMENT '医疗机构编码',
  `org_name` varchar(32) NULL DEFAULT NULL COMMENT '医疗机构名称',
  `create_by` varchar(64) NULL DEFAULT '' COMMENT '创建者',
  `create_time` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_by` varchar(64) NULL DEFAULT '' COMMENT '更新者',
  `update_time` datetime NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `remark` varchar(500) NULL DEFAULT NULL COMMENT '备注',
  `del_flag` char(1) NULL DEFAULT '0' COMMENT '删除标志（0代表存在 1代表删除）',
  `quantity` int NULL DEFAULT NULL COMMENT '药品数量',
  `unit` varchar(32) NULL DEFAULT NULL COMMENT '药品单位',
  `trac_cnt` int NULL DEFAULT NULL COMMENT '已采集的追溯码数量',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_id_dps_cfmxxh`(`id_dps` ASC, `cfmxxh` ASC) USING BTREE,
  INDEX `idx_del_flag_cfmxxh`(`del_flag` ASC, `cfmxxh` ASC) USING BTREE,
  INDEX `idx_cfxh`(`cfxh` ASC) USING BTREE,
  INDEX `idx_del_flag_cfxh`(`del_flag` ASC, `cfxh` ASC) USING BTREE,
  INDEX `idx_na_fee`(`na_fee` ASC) USING BTREE,
  INDEX `idx_drug_code`(`drug_code` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '发药单明细' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for ysf_sto_flow
-- ----------------------------
DROP TABLE IF EXISTS `ysf_sto_flow`;
CREATE TABLE `ysf_sto_flow`  (
  `id_sto_flow` bigint NOT NULL AUTO_INCREMENT COMMENT '出入库主键',
  `sd_flow` varchar(4) NULL DEFAULT NULL COMMENT '入出类型;1: 入库, 2: 出库, 3:养护',
  `sd_flow_sub` varchar(4) NULL DEFAULT NULL COMMENT '入出子类型;11: 采购,12: 申领,18:盘盈,19:其它入库,21.采购退库,22.调拨,28:盘亏,29:其它出库,31:破损',
  `cd_sto_flow` varchar(90) NULL DEFAULT NULL COMMENT '出入库单号',
  `dt_flow` datetime NULL DEFAULT NULL COMMENT '入出库业务时间;如采购日期等',
  `id_org_target` varchar(24) NULL DEFAULT NULL COMMENT '目标机构',
  `id_dept_target` varchar(32) NULL DEFAULT NULL COMMENT '目标部门;采购时填写供应商,调拨时填写仓储或者科室',
  `na_dept_target` varchar(90) NULL DEFAULT NULL COMMENT '目标名称',
  `sd_flow_status` varchar(4) NULL DEFAULT NULL COMMENT '流转状态;0:新建, 9:已确认,1:提交,2:接收方确认,3:提交方确认,99:已作废',
  `fg_active` varchar(1) NULL DEFAULT NULL COMMENT '有效标识;0: 失效, 1:有效',
  `purchase_way` varchar(4) NULL DEFAULT NULL COMMENT '购入方式;1.货到票到,2货到票未到,3.票到货未到',
  `fg_accept` varchar(1) NULL DEFAULT NULL COMMENT '验收标志;sys.sd.yesOrNo,0未验收,1已验收. 默认0',
  `memo` varchar(900) NULL DEFAULT NULL COMMENT '备注',
  `json_field` json NULL COMMENT '扩展字段;json格式扩展字段',
  `id_sto` varchar(24) NULL DEFAULT NULL COMMENT '仓储ID',
  `confirm_user` varchar(24) NULL DEFAULT NULL COMMENT '单据确认人',
  `na_confirm_user` varchar(90) NULL DEFAULT NULL COMMENT '单据确认人姓名',
  `confirm_time` datetime NULL DEFAULT NULL COMMENT '单据确时间',
  `id_org` varchar(24) NULL DEFAULT NULL COMMENT '机构编号',
  `org_id` varchar(32) NULL DEFAULT NULL COMMENT '医疗机构编码',
  `org_name` varchar(32) NULL DEFAULT NULL COMMENT '医疗机构名称',
  `revision` varchar(24) NULL DEFAULT NULL COMMENT '乐观锁',
  `create_by` varchar(64) NULL DEFAULT '' COMMENT '创建者',
  `create_time` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_by` varchar(64) NULL DEFAULT '' COMMENT '更新者',
  `update_time` datetime NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `remark` varchar(500) NULL DEFAULT NULL COMMENT '备注',
  `del_flag` char(1) NULL DEFAULT '0' COMMENT '删除标志（0代表存在 1代表删除）',
  PRIMARY KEY (`id_sto_flow`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '仓储物品出入库记录' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for ysf_sto_flow_sub
-- ----------------------------
DROP TABLE IF EXISTS `ysf_sto_flow_sub`;
CREATE TABLE `ysf_sto_flow_sub`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '出入库明细ID',
  `id_sto_flow` varchar(24) NULL DEFAULT NULL COMMENT '出入库主键',
  `drug_code` varchar(24) NULL DEFAULT NULL COMMENT '商品编码',
  `sn_bill` varchar(255) NULL DEFAULT NULL COMMENT '发票号码',
  `price_pur` decimal(24, 6) NULL DEFAULT NULL COMMENT '进货价格;调拨和申领都以出库方信息为准',
  `price_sale` decimal(24, 6) NULL DEFAULT NULL COMMENT '零售价格;调拨和申领都以出库方信息为准',
  `sel_retn_cnt` int NULL DEFAULT NULL COMMENT '出入库数量',
  `unit_flow` varchar(32) NULL DEFAULT NULL COMMENT '出入库包装单位',
  `unit_sale_factor` int NULL DEFAULT NULL COMMENT '出入库包装转换系数',
  `amt_total_pur` decimal(24, 6) NULL DEFAULT NULL COMMENT '进货合计',
  `amt_total` decimal(24, 6) NULL DEFAULT NULL COMMENT '零售合计',
  `amount_request` int NULL DEFAULT NULL COMMENT '请求数量',
  `unit_flow_request` varchar(32) NULL DEFAULT NULL COMMENT '请求包装单位',
  `unit_sale_factor_request` int NULL DEFAULT NULL COMMENT '请求包装转换系数',
  `manu_lotnum` varchar(32) NULL DEFAULT NULL COMMENT '药品批次',
  `expy_end` datetime NULL DEFAULT NULL COMMENT '药品效期',
  `json_field` json NULL COMMENT '扩展字段',
  `id_sto_inv` varchar(24) NULL DEFAULT NULL COMMENT '库存id;发起方库存id',
  `id_sto_inv_target` varchar(24) NULL DEFAULT NULL COMMENT '目标库房库存id;目标库房库存id',
  `id_med_pro_target` varchar(255) NULL DEFAULT NULL COMMENT '目标库房商品id;相同的药品有多个不同的包装,一个包装一个商品.跨部门库存移动时可能存在不同包装的商品移动,故记录目标库房商品id',
  `id_flow_accept_sub` varchar(24) NULL DEFAULT NULL COMMENT '验收明细id',
  `per` decimal(24, 6) NULL DEFAULT NULL COMMENT '加成率',
  `sn` int NULL DEFAULT NULL COMMENT '排序号',
  `id_org` varchar(24) NULL DEFAULT NULL COMMENT '机构编号',
  `org_id` varchar(32) NULL DEFAULT NULL COMMENT '医疗机构编码',
  `org_name` varchar(32) NULL DEFAULT NULL COMMENT '医疗机构名称',
  `revision` varchar(24) NULL DEFAULT NULL COMMENT '乐观锁',
  `create_by` varchar(64) NULL DEFAULT '' COMMENT '创建者',
  `create_time` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_by` varchar(64) NULL DEFAULT '' COMMENT '更新者',
  `update_time` datetime NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `remark` varchar(500) NULL DEFAULT NULL COMMENT '备注',
  `del_flag` char(1) NULL DEFAULT '0' COMMENT '删除标志（0代表存在 1代表删除）',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '仓储物品出入库明细' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for ysf_sto_hos_dps
-- ----------------------------
DROP TABLE IF EXISTS `ysf_sto_hos_dps`;
CREATE TABLE `ysf_sto_hos_dps`  (
  `id_dps_hos` bigint NOT NULL AUTO_INCREMENT COMMENT '主键',
  `id_plan_sub` varchar(24) NOT NULL COMMENT '来源ID',
  `id_adsn` varchar(24) NULL DEFAULT NULL COMMENT '入住记录唯一标识',
  `ori_id` varchar(24) NULL DEFAULT NULL COMMENT '原发药单ID;用于退药单,原发药单明细id',
  `cd_submit_no` varchar(255) NULL DEFAULT NULL COMMENT '提交单号',
  `sd_classify` varchar(4) NULL DEFAULT NULL COMMENT '医嘱类别;1临时 2长期;phis.ins.drord_type',
  `id_fee` varchar(24) NULL DEFAULT NULL COMMENT '费用明细ID',
  `na_fee` varchar(90) NULL DEFAULT NULL COMMENT '费用名称',
  `id_med_pro` varchar(24) NULL DEFAULT NULL COMMENT '药品ID（Pro）',
  `id_war` varchar(24) NULL DEFAULT NULL COMMENT '发药病区',
  `sd_type` varchar(4) NULL DEFAULT NULL COMMENT '类型;0常规，1 出院带药，2 急诊用药',
  `price_sale` decimal(24, 6) NULL DEFAULT NULL COMMENT '单价',
  `sel_retn_cnt` int NULL DEFAULT NULL COMMENT '数量',
  `amount_dps` int NULL DEFAULT NULL COMMENT '实发数量',
  `amt_total` decimal(24, 6) NULL DEFAULT NULL COMMENT '金额',
  `amt_total_dps` decimal(24, 6) NULL DEFAULT NULL COMMENT '实发金额',
  `unit_sale` varchar(90) NULL DEFAULT NULL COMMENT '包装单位',
  `unit_sale_factor` int NULL DEFAULT NULL COMMENT '包装系数',
  `sd_round` varchar(4) NULL DEFAULT NULL COMMENT '取整策略',
  `sd_dps` varchar(4) NULL DEFAULT NULL COMMENT '发药方式;rbmh.base.med.dispensingMethod',
  `cd_dps` varchar(32) NULL DEFAULT NULL COMMENT '发药单号',
  `sd_dps_type` varchar(4) NULL DEFAULT NULL COMMENT '发药单类型;1:购药单, 2: 处方单;phis.stp.dpsType',
  `send_time` datetime NULL DEFAULT NULL COMMENT '发药时间',
  `id_pi` varchar(24) NOT NULL COMMENT '人员唯一标识',
  `psn_name` varchar(90) NULL DEFAULT NULL COMMENT '患者姓名',
  `cd_file` varchar(32) NULL DEFAULT NULL COMMENT '病案号',
  `fg_baby` varchar(1) NULL DEFAULT NULL COMMENT '是否婴儿',
  `id_baby` varchar(24) NULL DEFAULT NULL COMMENT '婴儿主键',
  `cd_bed` varchar(32) NULL DEFAULT NULL COMMENT '床号',
  `dt_order_start` datetime NULL DEFAULT NULL COMMENT '开嘱时间',
  `dt_exec` datetime NULL DEFAULT NULL COMMENT '执行时间',
  `id_ord` varchar(24) NULL DEFAULT NULL COMMENT '医嘱ID',
  `sn_bill` varchar(32) NULL DEFAULT NULL COMMENT '票据号码',
  `id_stl` varchar(24) NULL DEFAULT NULL COMMENT '结算主键',
  `id_sto` varchar(24) NOT NULL COMMENT '仓储ID',
  `id_sto_win` varchar(24) NULL DEFAULT NULL COMMENT '发药窗口ID',
  `fg_status` varchar(1) NULL DEFAULT NULL COMMENT '发药单状态;0:待发药,1:已发药,7:部分退药，8:全部退药，9:作废  退药单:8:已退药未退费,9:已退费',
  `fg_print` varchar(1) NULL DEFAULT NULL COMMENT '打印标识',
  `id_doc` varchar(24) NULL DEFAULT NULL COMMENT '开单医生',
  `id_dept` varchar(24) NULL DEFAULT NULL COMMENT '开单科室',
  `sel_retn_opter_name` varchar(24) NULL DEFAULT NULL COMMENT '发药人',
  `user_check` varchar(24) NULL DEFAULT NULL COMMENT '核对人',
  `fg_dps` varchar(1) NULL DEFAULT NULL COMMENT '退药单标记;0:发药, 1: 退药',
  `group_no` varchar(32) NULL DEFAULT NULL COMMENT '组号',
  `fg_active` varchar(1) NOT NULL COMMENT '有效标识;0: 失效, 1:有效',
  `amt_total_pur` decimal(24, 6) NULL DEFAULT NULL COMMENT '发药时的进货总价',
  `id_org` varchar(24) NOT NULL COMMENT '机构标识',
  `org_id` varchar(32) NOT NULL COMMENT '医疗机构编码',
  `org_name` varchar(32) NULL DEFAULT NULL COMMENT '医疗机构名称',
  `revision` varchar(24) NULL DEFAULT NULL COMMENT '乐观锁',
  `create_by` varchar(64) NULL DEFAULT '' COMMENT '创建者',
  `create_time` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_by` varchar(64) NULL DEFAULT '' COMMENT '更新者',
  `update_time` datetime NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `remark` varchar(500) NULL DEFAULT NULL COMMENT '备注',
  `del_flag` char(1) NULL DEFAULT '0' COMMENT '删除标志（0代表存在 1代表删除）',
  PRIMARY KEY (`id_dps_hos`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '住院发药单' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for ysf_sto_inv
-- ----------------------------
DROP TABLE IF EXISTS `ysf_sto_inv`;
CREATE TABLE `ysf_sto_inv`  (
  `id_sto_inv` bigint NOT NULL AUTO_INCREMENT COMMENT '药品库存主键',
  `drug_code` varchar(24) NOT NULL COMMENT '商品编码',
  `amount` int NOT NULL COMMENT '库存数量;包装单位为hi_sto_med中的unit_sale',
  `price_sale` decimal(24, 6) NOT NULL COMMENT '零售价格;hi_sto_med中每1(unit_sale)的价格',
  `price_pur` decimal(24, 6) NOT NULL COMMENT '进货价格',
  `manu_lotnum` varchar(255) NULL DEFAULT NULL COMMENT '药品批次',
  `expy_end` datetime NULL DEFAULT NULL COMMENT '药品效期',
  `fg_active` varchar(1) NULL DEFAULT '1' COMMENT '有效标识;0: 失效, 1:有效',
  `memo` varchar(255) NULL DEFAULT NULL COMMENT '备注',
  `id_sto` varchar(24) NOT NULL COMMENT '仓储主键',
  `id_org` varchar(24) NOT NULL COMMENT '机构编号',
  `org_id` varchar(32) NULL DEFAULT NULL COMMENT '医疗机构编码',
  `org_name` varchar(32) NULL DEFAULT NULL COMMENT '医疗机构名称',
  `revision` varchar(24) NULL DEFAULT NULL COMMENT '乐观锁',
  `create_by` varchar(64) NULL DEFAULT '' COMMENT '创建者',
  `create_time` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_by` varchar(64) NULL DEFAULT '' COMMENT '更新者',
  `update_time` datetime NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `remark` varchar(500) NULL DEFAULT NULL COMMENT '备注',
  `del_flag` char(1) NULL DEFAULT '0' COMMENT '删除标志（0代表存在 1代表删除）',
  PRIMARY KEY (`id_sto_inv`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '仓储库存记录' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for ysf_sto_inv_log
-- ----------------------------
DROP TABLE IF EXISTS `ysf_sto_inv_log`;
CREATE TABLE `ysf_sto_inv_log`  (
  `id_inv_log` bigint NOT NULL AUTO_INCREMENT COMMENT '库存变动记录主键',
  `id_sto_inv` varchar(24) NULL DEFAULT NULL COMMENT '库存主键',
  `drug_code` varchar(24) NULL DEFAULT NULL COMMENT '商品编码',
  `sd_amt_change` varchar(4) NULL DEFAULT NULL COMMENT '变动原因',
  `des_reason` varchar(255) NULL DEFAULT NULL COMMENT '原因情况',
  `id_biz_ori` varchar(24) NULL DEFAULT NULL COMMENT '原始业务主键',
  `amt_change` int NULL DEFAULT NULL COMMENT '变动数量',
  `amt_before` int NULL DEFAULT NULL COMMENT '变动前数量',
  `amt_after` int NULL DEFAULT NULL COMMENT '变动后数量',
  `price_sale` decimal(24, 6) NULL DEFAULT NULL COMMENT '库存价格;变动时的价格,避免调价后导致价格变化',
  `price_pur` decimal(24, 6) NULL DEFAULT NULL COMMENT '进货价格',
  `unit_sale` varchar(90) NULL DEFAULT NULL COMMENT '包装单位',
  `unit_sale_factor` int NULL DEFAULT NULL COMMENT '转换系数',
  `id_sto` varchar(24) NULL DEFAULT NULL COMMENT '库房id',
  `id_org` varchar(24) NULL DEFAULT NULL COMMENT '机构编号',
  `org_id` varchar(32) NULL DEFAULT NULL COMMENT '医疗机构编码',
  `org_name` varchar(32) NULL DEFAULT NULL COMMENT '医疗机构名称',
  `revision` varchar(24) NULL DEFAULT NULL COMMENT '乐观锁',
  `create_by` varchar(64) NULL DEFAULT '' COMMENT '创建者',
  `create_time` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_by` varchar(64) NULL DEFAULT '' COMMENT '更新者',
  `update_time` datetime NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `remark` varchar(500) NULL DEFAULT NULL COMMENT '备注',
  `del_flag` char(1) NULL DEFAULT '0' COMMENT '删除标志（0代表存在 1代表删除）',
  PRIMARY KEY (`id_inv_log`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '仓储库存记录变动记录' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for ysf_sto_pha
-- ----------------------------
DROP TABLE IF EXISTS `ysf_sto_pha`;
CREATE TABLE `ysf_sto_pha`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键',
  `sd_disp` varchar(4) NOT NULL COMMENT '类别;rbmh.base.med.articleType,1西药、2中成药、3草药、4疫苗、5耗材、6固定资产,7.民族药品,8.院内制剂',
  `id_sto` varchar(24) NOT NULL COMMENT '仓储id',
  `id_dept` json NULL COMMENT '科室id',
  `sd_use` varchar(4) NOT NULL COMMENT '使用范围;1.门诊,2.住院,3.全部',
  `sd_limit` varchar(4) NULL DEFAULT NULL COMMENT '其他限定;21常规,24急诊用药,25.出院带药',
  `id_org` varchar(24) NULL DEFAULT NULL COMMENT '机构编号',
  `org_id` varchar(32) NULL DEFAULT NULL COMMENT '医疗机构编码',
  `org_name` varchar(32) NULL DEFAULT NULL COMMENT '医疗机构名称',
  `revision` varchar(24) NULL DEFAULT NULL COMMENT '乐观锁',
  `create_by` varchar(64) NULL DEFAULT '' COMMENT '创建者',
  `create_time` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_by` varchar(64) NULL DEFAULT '' COMMENT '更新者',
  `update_time` datetime NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `remark` varchar(500) NULL DEFAULT NULL COMMENT '备注',
  `del_flag` char(1) NULL DEFAULT '0' COMMENT '删除标志（0代表存在 1代表删除）',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '发药药房设置' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for ysf_sto_tc
-- ----------------------------
DROP TABLE IF EXISTS `ysf_sto_tc`;
CREATE TABLE `ysf_sto_tc`  (
  `id_tc` bigint NOT NULL AUTO_INCREMENT COMMENT '主键',
  `sd_tc` varchar(4) NULL DEFAULT NULL COMMENT '追溯码类型(废弃);用于标识是按明细的还是按商品的,按明细的会对记录进行核销',
  `sd_tc_manage` varchar(4) NOT NULL COMMENT '追溯码管理模式;数据落地时的追溯码管理模式,简易管理/严格管理',
  `drug_code` varchar(24) NOT NULL COMMENT '商品编码',
  `id_sto_inv` varchar(24) NULL DEFAULT NULL COMMENT '库存id;预留字段,后期扩展用',
  `id_dept` varchar(24) NULL DEFAULT NULL COMMENT '部门id;预留字段,后期扩展用',
  `drugtracinfo` varchar(255) NOT NULL COMMENT '追溯码',
  `unit_sale_factor` int NULL DEFAULT NULL COMMENT '追溯码包装转换系数',
  `unit_tc` varchar(32) NULL DEFAULT NULL COMMENT '追溯码对应的包装',
  `amount_rem` decimal(24, 6) NULL DEFAULT NULL COMMENT '剩余数量;用于拆零卖的情况,剩余数量<=包装转换系数',
  `fg_active` varchar(1) NULL DEFAULT NULL COMMENT '有效标识;0: 失效, 1:有效',
  `id_org` varchar(24) NULL DEFAULT NULL COMMENT '机构编号',
  `manu_lotnum` varchar(24) NULL DEFAULT NULL COMMENT '生产批号',
  `manu_date` datetime NULL DEFAULT NULL COMMENT '生产日期',
  `expy_end` datetime NULL DEFAULT NULL COMMENT '有效期止',
  `org_id` varchar(32) NULL DEFAULT NULL COMMENT '医疗机构编码',
  `org_name` varchar(32) NULL DEFAULT NULL COMMENT '医疗机构名称',
  `revision` varchar(24) NULL DEFAULT NULL COMMENT '乐观锁',
  `create_by` varchar(64) NULL DEFAULT '' COMMENT '创建者',
  `create_time` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_by` varchar(64) NULL DEFAULT '' COMMENT '更新者',
  `update_time` datetime NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `remark` varchar(500) NULL DEFAULT NULL COMMENT '备注',
  `del_flag` char(1) NULL DEFAULT '0' COMMENT '删除标志（0代表存在 1代表删除）',
  PRIMARY KEY (`id_tc`) USING BTREE,
  INDEX `idx_drugtracinfo`(`drugtracinfo` ASC) USING BTREE,
  INDEX `idx_del_flag_drugtracinfo`(`del_flag` ASC, `drugtracinfo` ASC) USING BTREE,
  INDEX `idx_drug_code`(`drug_code` ASC) USING BTREE,
  INDEX `idx_del_flag_drug_code`(`del_flag` ASC, `drug_code` ASC) USING BTREE,
  INDEX `idx_org_id_del_flag`(`org_id` ASC, `del_flag` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '商品追溯码' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for ysf_sto_tc_status
-- ----------------------------
DROP TABLE IF EXISTS `ysf_sto_tc_status`;
CREATE TABLE `ysf_sto_tc_status`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键',
  `sd_tc_status` varchar(4) NOT NULL COMMENT '业务类型',
  `sd_tc_manage` varchar(4) NOT NULL COMMENT '追溯码管理模式;数据落地时的追溯码管理模式,简易管理/严格管理',
  `id_biz_ori` varchar(24) NOT NULL COMMENT '发药单明细id(原始业务主键)',
  `id_tc` bigint NULL DEFAULT NULL COMMENT '追溯码主键',
  `cfmxxh` varchar(24) NULL DEFAULT NULL COMMENT '原始业务明细编号',
  `drug_code` varchar(255) NOT NULL COMMENT '商品编码',
  `drugtracinfo` varchar(255) NOT NULL COMMENT '追溯码',
  `id_sto_inv` varchar(24) NULL DEFAULT NULL COMMENT '库存id;预留字段,后期扩展用',
  `id_dept` varchar(24) NULL DEFAULT NULL COMMENT '库房id;预留字段,后期扩展用',
  `sd_tc` varchar(4) NOT NULL COMMENT '追溯码类型;1.整箱码,2.商品追溯码.',
  `sel_retn_cnt` int NULL DEFAULT NULL COMMENT '数量;针对于sd_tc=1的记录,记录下商品的数量',
  `fg_pack` varchar(1) NULL DEFAULT NULL COMMENT '是否拆包装:0未拆零,1拆零',
  `fg_up` varchar(1) NULL DEFAULT NULL COMMENT '上传标志',
  `fg_active` varchar(1) NULL DEFAULT NULL COMMENT '确认标志;0未确认,1已确认,用于调拨申领',
  `id_org` varchar(24) NULL DEFAULT NULL COMMENT '机构编号',
  `org_id` varchar(32) NULL DEFAULT NULL COMMENT '医疗机构编码',
  `org_name` varchar(32) NULL DEFAULT NULL COMMENT '医疗机构名称',
  `revision` varchar(24) NULL DEFAULT NULL COMMENT '乐观锁',
  `create_by` varchar(64) NULL DEFAULT '' COMMENT '创建者',
  `create_time` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_by` varchar(64) NULL DEFAULT '' COMMENT '更新者',
  `update_time` datetime NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `remark` varchar(500) NULL DEFAULT NULL COMMENT '备注',
  `del_flag` char(1) NULL DEFAULT '0' COMMENT '删除标志（0代表存在 1代表删除）',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_cfmxxh_drugtracinfo`(`cfmxxh` ASC, `drugtracinfo` ASC) USING BTREE,
  INDEX `idx_del_flag_cfmxxh`(`del_flag` ASC, `cfmxxh` ASC) USING BTREE,
  INDEX `idx_del_flag_drugtracinfo`(`del_flag` ASC, `drugtracinfo` ASC) USING BTREE,
  INDEX `idx_id_tc`(`id_tc` ASC) USING BTREE,
  INDEX `idx_sd_tc_status`(`sd_tc_status` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '追溯码状态记录表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for ysf_sto_tc_task
-- ----------------------------
DROP TABLE IF EXISTS `ysf_sto_tc_task`;
CREATE TABLE `ysf_sto_tc_task`  (
  `id_task` bigint NOT NULL AUTO_INCREMENT COMMENT '任务主键',
  `sd_task_type` varchar(4) NOT NULL COMMENT '任务类型;1.主动任务,2.被动任务',
  `sd_tc_status` varchar(4) NULL DEFAULT NULL COMMENT '业务类型',
  `cd_biz` varchar(32) NOT NULL COMMENT '关联的业务单号: 入库单号/发药单号/退药单号等',
  `id_dept` varchar(24) NULL DEFAULT NULL COMMENT '关联科室/部门: 来源于ysf_sto_dept.id_dept',
  `id_user` varchar(24) NULL DEFAULT NULL COMMENT '操作员ID: 被动任务时绑定具体操作员',
  `dt_expire` date NULL DEFAULT NULL COMMENT '任务过期时间: 如发药时间和当前时间的差额超过30分钟，那会自动将这个任务推送到医保接口。如果始终没有关联到发药时间，那么6个小时后会自动失效',
  `fg_status` varchar(4) NULL DEFAULT '0' COMMENT '任务状态: 0.待处理, 1.已完成, 2.已失效',
  `fg_priority` varchar(1) NULL DEFAULT '1' COMMENT '优先级: 1.普通, 2.紧急',
  `memo` varchar(255) NULL DEFAULT NULL COMMENT '任务备注',
  `id_org` varchar(24) NOT NULL COMMENT '机构编号',
  `org_id` varchar(32) NULL DEFAULT NULL COMMENT '医疗机构编码',
  `org_name` varchar(32) NULL DEFAULT NULL COMMENT '医疗机构名称',
  `revision` varchar(24) NULL DEFAULT NULL COMMENT '乐观锁',
  `create_by` varchar(64) NULL DEFAULT '' COMMENT '创建者',
  `create_time` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_by` varchar(64) NULL DEFAULT '' COMMENT '更新者',
  `update_time` datetime NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `remark` varchar(500) NULL DEFAULT NULL COMMENT '备注',
  `del_flag` char(1) NULL DEFAULT '0' COMMENT '删除标志（0代表存在 1代表删除）',
  PRIMARY KEY (`id_task`) USING BTREE,
  INDEX `ysf_sto_tc_task_cd_biz_IDX`(`cd_biz` ASC) USING BTREE,
  INDEX `idx_cd_biz_fg_status`(`cd_biz` ASC, `fg_status` ASC) USING BTREE,
  INDEX `idx_del_flag_cd_biz`(`del_flag` ASC, `cd_biz` ASC) USING BTREE,
  INDEX `idx_fg_status_del_flag`(`fg_status` ASC, `del_flag` ASC) USING BTREE,
  INDEX `idx_create_time`(`create_time` ASC) USING BTREE,
  INDEX `idx_del_flag_fg_status_create_time`(`del_flag` ASC, `fg_status` ASC, `create_time` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '扫码任务记录表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for ysf_sto_tc_task_sub
-- ----------------------------
DROP TABLE IF EXISTS `ysf_sto_tc_task_sub`;
CREATE TABLE `ysf_sto_tc_task_sub`  (
  `id_sub` bigint NOT NULL AUTO_INCREMENT COMMENT '明细ID',
  `id_task` varchar(24) NOT NULL COMMENT '关联任务ID',
  `drugtracinfo` text NULL COMMENT '追溯码: 来源于ysf_sto_tc.drugtracinfo',
  `drug_code` varchar(24) NULL DEFAULT NULL COMMENT '商品编码: ysf_sto_inv.drug_code',
  `id_sto_inv` varchar(24) NULL DEFAULT NULL COMMENT '库存ID: ysf_sto_inv.id_sto_inv',
  `id_biz_sub` bigint NULL DEFAULT NULL COMMENT '业务明细ID: 发药单明细(ysf_sto_dps_sub.id)',
  `cfmxxh` varchar(24) NULL DEFAULT NULL COMMENT '原始业务明细编号',
  `dt_scan` date NULL DEFAULT NULL COMMENT '预计扫描时间: 如发药单需在指定时间前完成',
  `fg_scanned` varchar(1) NULL DEFAULT '0' COMMENT '是否已扫码: 0否,1是',
  `scan_user` varchar(24) NULL DEFAULT NULL COMMENT '实际扫码人',
  `scan_time` datetime NULL DEFAULT NULL COMMENT '扫码时间',
  `scan_remark` varchar(255) NULL DEFAULT NULL COMMENT '扫码备注',
  `id_org` varchar(24) NOT NULL COMMENT '机构编号',
  `org_id` varchar(32) NULL DEFAULT NULL COMMENT '医疗机构编码',
  `org_name` varchar(32) NULL DEFAULT NULL COMMENT '医疗机构名称',
  `revision` varchar(24) NULL DEFAULT NULL COMMENT '乐观锁',
  `create_by` varchar(64) NULL DEFAULT '' COMMENT '创建者',
  `create_time` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_by` varchar(64) NULL DEFAULT '' COMMENT '更新者',
  `update_time` datetime NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `remark` varchar(500) NULL DEFAULT NULL COMMENT '备注',
  `del_flag` char(1) NULL DEFAULT '0' COMMENT '删除标志（0代表存在 1代表删除）',
  `quantity` int NULL DEFAULT NULL COMMENT '药品数量',
  `unit` varchar(32) NULL DEFAULT NULL COMMENT '药品单位',
  `trac_cnt` int NULL DEFAULT NULL COMMENT '已采集的追溯码数量',
  PRIMARY KEY (`id_sub`) USING BTREE,
  INDEX `idx_id_task_cfmxxh`(`id_task` ASC, `cfmxxh` ASC) USING BTREE,
  INDEX `idx_id_task_fg_scanned`(`id_task` ASC, `fg_scanned` ASC) USING BTREE,
  INDEX `idx_del_flag_cfmxxh`(`del_flag` ASC, `cfmxxh` ASC) USING BTREE,
  INDEX `idx_cfmxxh`(`cfmxxh` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '扫码任务明细表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for ysf_sto_win
-- ----------------------------
DROP TABLE IF EXISTS `ysf_sto_win`;
CREATE TABLE `ysf_sto_win`  (
  `id_win` bigint NOT NULL AUTO_INCREMENT COMMENT '窗口id',
  `cd_win` varchar(32) NULL DEFAULT NULL COMMENT '窗口编号,预留',
  `na_win` varchar(90) NULL DEFAULT NULL COMMENT '窗口名称',
  `id_sto` varchar(24) NULL DEFAULT NULL COMMENT '库房id',
  `id_dept` varchar(24) NULL DEFAULT NULL COMMENT '部门id',
  `fg_active` varchar(1) NULL DEFAULT NULL COMMENT '有效标识',
  `win_ip` varchar(32) NULL DEFAULT NULL COMMENT 'id地址,预留',
  `win_mac` varchar(32) NULL DEFAULT NULL COMMENT 'mac地址,预留',
  `id_org` varchar(24) NULL DEFAULT NULL COMMENT '机构编号',
  `org_id` varchar(32) NULL DEFAULT NULL COMMENT '医疗机构编码',
  `org_name` varchar(32) NULL DEFAULT NULL COMMENT '医疗机构名称',
  `revision` varchar(24) NULL DEFAULT NULL COMMENT '乐观锁',
  `create_by` varchar(64) NULL DEFAULT '' COMMENT '创建者',
  `create_time` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_by` varchar(64) NULL DEFAULT '' COMMENT '更新者',
  `update_time` datetime NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `remark` varchar(500) NULL DEFAULT NULL COMMENT '备注',
  `del_flag` char(1) NULL DEFAULT '0' COMMENT '删除标志（0代表存在 1代表删除）',
  PRIMARY KEY (`id_win`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '药房窗口' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for ysf_sto_win_limit
-- ----------------------------
DROP TABLE IF EXISTS `ysf_sto_win_limit`;
CREATE TABLE `ysf_sto_win_limit`  (
  `id_win_limit` bigint NOT NULL AUTO_INCREMENT COMMENT '限定id',
  `id_win` varchar(24) NULL DEFAULT NULL COMMENT '窗口id',
  `fg_west` varchar(1) NULL DEFAULT NULL COMMENT '西药权限',
  `fg_med` varchar(1) NULL DEFAULT NULL COMMENT '成药权限',
  `fg_herb` varchar(1) NULL DEFAULT NULL COMMENT '草药权限',
  `fg_vac` varchar(1) NULL DEFAULT NULL COMMENT '疫苗权限',
  `fg_sup` varchar(1) NULL DEFAULT NULL COMMENT '耗材权限',
  `fg_eth` varchar(1) NULL DEFAULT NULL COMMENT '民族药权限',
  `fg_hos` varchar(1) NULL DEFAULT NULL COMMENT '院内制剂权限',
  `id_org` varchar(24) NULL DEFAULT NULL COMMENT '机构编号',
  `org_id` varchar(32) NULL DEFAULT NULL COMMENT '医疗机构编码',
  `org_name` varchar(32) NULL DEFAULT NULL COMMENT '医疗机构名称',
  `revision` varchar(24) NULL DEFAULT NULL COMMENT '乐观锁',
  `create_by` varchar(64) NULL DEFAULT '' COMMENT '创建者',
  `create_time` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_by` varchar(64) NULL DEFAULT '' COMMENT '更新者',
  `update_time` datetime NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `remark` varchar(500) NULL DEFAULT NULL COMMENT '备注',
  `del_flag` char(1) NULL DEFAULT '0' COMMENT '删除标志（0代表存在 1代表删除）',
  PRIMARY KEY (`id_win_limit`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '药房窗口限定' ROW_FORMAT = Dynamic;


