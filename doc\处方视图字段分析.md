# Compare output parameters and highlight differences
_Exported on 2025/7/2 at GMT+8 12:18:35 from Cursor (1.2.0)_

---

**User**

VIEW_YSF_MZFYCF和接口文档内的出参内容比较下差别,单独标记出来

---

**Cursor**

我来比较VIEW_YSF_MZFYCF视图数据和接口文档中的出参内容，找出它们之间的差别。

现在让我详细比较VIEW_YSF_MZFYCF视图数据和接口文档中的出参内容，标记出差别：

## VIEW_YSF_MZFYCF 与接口文档出参对比分析

### 📋 **VIEW_YSF_MZFYCF视图现有字段**
```json
{
  "patid": "101326",                    // 患者ID
  "spec": "1mg*10支/盒",               // 规格
  "prodentp_name": "遂成药业股份有限公司", // 生产企业名称
  "med_list_codg": "XC01CAS071B002010103204", // 医疗目录编码
  "fixmedins_hilist_id": "574",        // 定点医药机构目录编号
  "fixmedins_hilist_name": "盐酸肾上腺素注射液", // 定点医药机构目录名称
  "cfxh": 819038,                      // 处方序号
  "cfmxxh": 1351928,                   // 处方明细序号
  "fixmedins_bchno": "YFKC450312946083796800", // 定点医药机构批次流水号
  "prsc_dr_name": "卢成成",            // 开方医师姓名
  "phar_name": "胡忠芝",               // 药师姓名
  "phar_prac_cert_no": "34210119790901138x", // 药师执业资格证号
  "rx_flag": 0,                        // 处方药标志
  "trdn_flag": "1",                    // 拆零标志
  "rtal_docno": 3796800,               // 零售单据号
  "sel_retn_cnt": 0.1000,              // 销售/退货数量
  "sel_retn_time": "2025-06-01 10:00:32", // 销售/退货时间
  "sel_retn_opter_name": "胡忠芝",     // 销售/退货经办人姓名
  "MDTRT_SETL_TYPE": "2",              // 就诊结算类型
  "manu_lotnum": "62410271",           // 生产批号
  "manu_date": "    -  -   00:00:00",  // 生产日期
  "expy_end": "2026-09-30 00:00:00",   // 有效期止
  "mdtrt_sn": "34122025060184570185",  // 就医流水号
  "sjh": "2025060135220063                ", // 收据号
  "psn_name": "郝学法",                // 人员姓名
  "hjxh": "819038",                    // 汇总序号(?)
  "send_time": "2025-06-01 10:05:52",  // 发药时间
  "fyyf": "4503        ",              // 发药药房ID
  "fyyfName": "西药房（门诊）",        // 发药药房名称
  "windows": "10"                      // 窗口号
}
```

---

### ❌ **VIEW_YSF_MZFYCF 缺少的必填字段**

| 字段名 | 中文名称 | 必填性 | 说明 |
|--------|----------|--------|------|
| `prsc_dr_certno` | 开方医师证件号码 | 非必填 | 视图中没有此字段 |
| `phar_certno` | 药师证件号码 | 非必填 | 视图中没有此字段 |
| `rxno` | 处方号 | 非必填 | 视图中没有此字段 |
| `rx_circ_flag` | 外购处方标志 | 非必填 | 视图中没有此字段 |
| `stoout_no` | 销售出库单据号 | 非必填 | 视图中没有此字段 |
| `bchno` | 批次号 | 非必填 | 视图中没有此字段 |
| `min_sel_retn_cnt` | 最小单位销售数量 | 非必填 | 视图中没有此字段 |
| `sel_retn_unit` | 发药时候的单位 | **必填** | ⚠️ **视图中缺少此必填字段** |
| `his_dos_unit` | His的剂量单位 | **必填** | ⚠️ **视图中缺少此必填字段** |
| `his_pac_unit` | His的包装单位 | **必填** | ⚠️ **视图中缺少此必填字段** |
| `patient_id` | 患者id | **必填** | 视图中有`patid`，可能对应此字段 |
| `his_con_ratio` | 最小单位转换比 | 建议 | 视图中没有此字段 |
| `send_flag` | 发药标志 | 建议 | 视图中没有此字段 |
| `return_time` | 退药时间 | 非必填 | 视图中没有此字段 |
| `dept_id` | 发药药房id | **必填** | 视图中有`fyyf`，可能对应此字段 |
| `dept_name` | 发药药房名称 | **必填** | 视图中有`fyyfName`，对应此字段 |

---

### ✅ **VIEW_YSF_MZFYCF 额外包含的字段**

| 视图字段名 | 说明 |
|------------|------|
| `patid` | 患者ID（可能对应接口文档中的patient_id） |
| `hjxh` | 汇总序号（接口文档中没有此字段） |
| `fyyf` | 发药药房ID（可能对应接口文档中的dept_id） |
| `fyyfName` | 发药药房名称（对应接口文档中的dept_name） |

---

### 🔍 **字段名称不一致但可能对应的字段**

| 视图字段 | 接口文档字段 | 说明 |
|----------|--------------|------|
| `patid` | `patient_id` | 患者ID，字段名不同但含义相同 |
| `fyyf` | `dept_id` | 发药药房ID，字段名不同但含义相同 |
| `fyyfName` | `dept_name` | 发药药房名称，字段名不同但含义相同 |
| `windows` | `window` | 窗口号，字段名略有差异 |

---

### ⚠️ **关键问题总结**

1. **缺少3个必填字段**：
   - `sel_retn_unit`（发药时候的单位）
   - `his_dos_unit`（His的剂量单位）
   - `his_pac_unit`（His的包装单位）

2. **数据格式问题**：
   - `manu_date`：视图中为"    -  -   00:00:00"，接口要求"yyyy-MM-dd"格式
   - `sjh`：视图中有多余空格，需要trim处理

3. **字段映射需要调整**：
   - `patid` → `patient_id`
   - `fyyf` → `dept_id`
   - `windows` → `window`

4. **建议添加的字段**：
   - `send_flag`（发药标志）
   - `his_con_ratio`（最小单位转换比）

这些差别需要在数据库视图或数据处理逻辑中进行调整，以确保完全符合接口文档的要求。

```json
{
  "patid": "101326",                    // 患者ID
  "spec": "1mg*10支/盒",               // 规格
  "prodentp_name": "遂成药业股份有限公司", // 生产企业名称
  "med_list_codg": "XC01CAS071B002010103204", // 医疗目录编码
  "fixmedins_hilist_id": "574",        // 定点医药机构目录编号
  "fixmedins_hilist_name": "盐酸肾上腺素注射液", // 定点医药机构目录名称
  "cfxh": 819038,                      // 处方序号
  "cfmxxh": 1351928,                   // 处方明细序号
  "fixmedins_bchno": "YFKC450312946083796800", // 定点医药机构批次流水号
  "prsc_dr_name": "卢成成",            // 开方医师姓名
  "phar_name": "胡忠芝",               // 药师姓名
  "phar_prac_cert_no": "34210119790901138x", // 药师执业资格证号
  "rx_flag": 0,                        // 处方药标志
  "trdn_flag": "1",                    // 拆零标志
  "rtal_docno": 3796800,               // 零售单据号
  "sel_retn_cnt": 0.1000,              // 销售/退货数量
  "sel_retn_time": "2025-06-01 10:00:32", // 销售/退货时间
  "sel_retn_opter_name": "胡忠芝",     // 销售/退货经办人姓名
  "MDTRT_SETL_TYPE": "2",              // 就诊结算类型
  "manu_lotnum": "62410271",           // 生产批号
  "manu_date": "    -  -   00:00:00",  // 生产日期
  "expy_end": "2026-09-30 00:00:00",   // 有效期止
  "mdtrt_sn": "34122025060184570185",  // 就医流水号
  "sjh": "2025060135220063                ", // 收据号
  "psn_name": "郝学法",                // 人员姓名
  "hjxh": "819038",                    // 汇总序号(?)
  "send_time": "2025-06-01 10:05:52",  // 发药时间
  "fyyf": "4503        ",              // 发药药房ID
  "fyyfName": "西药房（门诊）",        // 发药药房名称
  "windows": "10"                      // 窗口号
}
```

---

**User**

同样比较下

---

**Cursor**

现在我来比较第二个数据示例与接口文档的差别。从附加数据中，我看到了一个新的数据示例和住院追溯码采集的接口文档。让我详细分析：

## 新数据示例 与接口文档出参对比分析

### 📋 **新数据示例现有字段**
```json
{
  "code": "0",                         // 返回编号
  "message": "成功",                   // 返回信息
  "med_list_codg": "XC01CAD162B002010181161", // 医疗目录编码
  "fixmedins_hilist_id": 1959,         // 定点医药机构目录编号
  "fixmedins_hilist_name": "盐酸多巴胺注射液", // 定点医药机构目录名称
  "fixmedins_bchno": "2615342-3518264", // 定点医药机构批次流水号
  "prsc_dr_certno": "1652        ",    // 开方医师证件号码
  "prsc_dr_name": "郑其彬",            // 开方医师姓名
  "phar_certno": "",                   // 药师证件号码（空值）
  "phar_name": "",                     // 药师姓名（空值）
  "phar_prac_cert_no": "",             // 药师执业资格证号（空值）
  "mdtrt_sn": 55233793,               // 就医流水号
  "psn_name": "王怀勋",                // 人员姓名
  "manu_lotnum": "",                   // 生产批号（空值）
  "manu_date": "",                     // 生产日期（空值）
  "expy_end": "",                      // 有效期止（空值）
  "rx_flag": "1",                      // 处方药标志
  "trdn_flag": "",                     // 拆零标志（空值）
  "rxno": 2615342,                     // 处方号
  "rx_circ_flag": "",                  // 外购处方标志（空值）
  "rtal_docno": "",                    // 零售单据号（空值）
  "stoout_no": "",                     // 销售出库单据号（空值）
  "bchno": "",                         // 批次号（空值）
  "sel_retn_cnt": 1.00,                // 销售/退货数量
  "sel_retn_time": "2025-06-01 00:19:26", // 销售/退货时间
  "sel_retn_opter_name": "3116        ", // 销售/退货经办人姓名
  "mdtrt_setl_type": "1",              // 就诊结算类型
  "spec": "5ml:200mg/支",              // 规格
  "prodentp_name": "扬州中宝药业股份有限公司", // 生产企业名称
  "cfxh": 2615342,                     // 处方序号
  "cfmxxh": 3518264,                   // 处方明细序号
  "sjh": 65180,                        // 收据号
  "patient_id": "24023820",            // 患者ID
  "fyyf": "4501        ",              // 发药药房
  "his_con_ratio": 1.0000,             // 最小单位转换比
  "cydybz": 0                          // 额外字段（接口文档中没有）
}
```

---

### ❌ **数据示例中的关键问题**

#### 1. **必填字段为空值问题**
| 字段名 | 中文名称 | 必填性 | 当前值 | 问题 |
|--------|----------|--------|--------|------|
| `phar_name` | 药师姓名 | **必填** | `""` | ⚠️ **必填字段为空** |
| `phar_prac_cert_no` | 药师执业资格证号 | **必填** | `""` | ⚠️ **必填字段为空** |
| `manu_lotnum` | 生产批号 | **必填** | `""` | ⚠️ **必填字段为空** |
| `manu_date` | 生产日期 | **必填** | `""` | ⚠️ **必填字段为空** |
| `rtal_docno` | 零售单据号 | **必填** | `""` | ⚠️ **必填字段为空** |
| `trdn_flag` | 拆零标志 | **必填** | `""` | ⚠️ **必填字段为空** |

#### 2. **仍然缺少的必填字段**
| 字段名 | 中文名称 | 必填性 | 说明 |
|--------|----------|--------|------|
| `sel_retn_unit` | 发药时候的单位 | **必填** | ⚠️ **完全缺少此字段** |
| `his_dos_unit` | His的剂量单位 | **必填** | ⚠️ **完全缺少此字段** |
| `his_pac_unit` | His的包装单位 | **必填** | ⚠️ **完全缺少此字段** |
| `dept_id` | 发药药房id | **必填** | 有`fyyf`但字段名不匹配 |
| `dept_name` | 发药药房名称 | **必填** | ⚠️ **完全缺少此字段** |
| `window` | 窗口号 | 非必填 | ⚠️ **缺少此字段** |

---

### 🔍 **与住院追溯码接口对比**

如果这个数据是用于住院追溯码采集，还需要额外的字段：

#### **住院接口额外需要的必填字段**
| 字段名 | 中文名称 | 必填性 | 当前状态 |
|--------|----------|--------|----------|
| `record_id` | 发药记录id | **必填** | ❌ 缺少 |
| `record_detail_id` | 发药明细id | **必填** | ❌ 缺少 |
| `fg_dps` | 发药单标记 | **必填** | ❌ 缺少 |
| `pat_ward_id` | 病区id | **必填** | ❌ 缺少 |
| `pat_ward_name` | 病区名称 | **必填** | ❌ 缺少 |
| `his_drug_code` | HIS系统药品编码 | **必填** | ❌ 缺少 |
| `pat_in_hos_id` | 住院id | **必填** | ❌ 缺少 |
| `order_id` | 医嘱id | **必填** | ❌ 缺少 |
| `bed_no` | 床位号 | 非必填 | ❌ 缺少 |
| `sel_retn_unit` | 销售/退货单位 | 建议必填 | ❌ 缺少 |

---

### ✅ **数据示例的优点**

1. **字段名称标准化**：使用了接口文档标准的字段名（如`patient_id`而不是`patid`）
2. **包含转换比**：有`his_con_ratio`字段
3. **数据类型正确**：数值型字段使用了正确的数据类型

---

### ⚠️ **关键问题总结**

#### **门诊接口问题**
1. **6个必填字段为空值**：需要从HIS系统获取真实数据
2. **3个必填字段完全缺少**：需要在视图中添加
3. **数据质量问题**：多个关键字段（如药师信息、批号等）为空

#### **住院接口问题**
1. **如果用于住院接口，缺少12个必填字段**
2. **缺少病区、医嘱等住院特有信息**

#### **建议**
1. **立即修复空值问题**：确保必填字段有真实数据
2. **添加缺失字段**：特别是单位相关的字段
3. **数据验证**：建立数据质量检查机制
4. **明确接口用途**：确认是门诊还是住院接口，按需实现

这个数据示例虽然字段名称更标准，但数据质量问题更严重，多个必填字段为空值，无法满足接口要求。

```json
{
  "code": "0",                         // 返回编号
  "message": "成功",                   // 返回信息
  "med_list_codg": "XC01CAD162B002010181161", // 医疗目录编码
  "fixmedins_hilist_id": 1959,         // 定点医药机构目录编号
  "fixmedins_hilist_name": "盐酸多巴胺注射液", // 定点医药机构目录名称
  "fixmedins_bchno": "2615342-3518264", // 定点医药机构批次流水号
  "prsc_dr_certno": "1652        ",    // 开方医师证件号码
  "prsc_dr_name": "郑其彬",            // 开方医师姓名
  "phar_certno": "",                   // 药师证件号码（空值）
  "phar_name": "",                     // 药师姓名（空值）
  "phar_prac_cert_no": "",             // 药师执业资格证号（空值）
  "mdtrt_sn": 55233793,               // 就医流水号
  "psn_name": "王怀勋",                // 人员姓名
  "manu_lotnum": "",                   // 生产批号（空值）
  "manu_date": "",                     // 生产日期（空值）
  "expy_end": "",                      // 有效期止（空值）
  "rx_flag": "1",                      // 处方药标志
  "trdn_flag": "",                     // 拆零标志（空值）
  "rxno": 2615342,                     // 处方号
  "rx_circ_flag": "",                  // 外购处方标志（空值）
  "rtal_docno": "",                    // 零售单据号（空值）
  "stoout_no": "",                     // 销售出库单据号（空值）
  "bchno": "",                         // 批次号（空值）
  "sel_retn_cnt": 1.00,                // 销售/退货数量
  "sel_retn_time": "2025-06-01 00:19:26", // 销售/退货时间
  "sel_retn_opter_name": "3116        ", // 销售/退货经办人姓名
  "mdtrt_setl_type": "1",              // 就诊结算类型
  "spec": "5ml:200mg/支",              // 规格
  "prodentp_name": "扬州中宝药业股份有限公司", // 生产企业名称
  "cfxh": 2615342,                     // 处方序号
  "cfmxxh": 3518264,                   // 处方明细序号
  "sjh": 65180,                        // 收据号
  "patient_id": "24023820",            // 患者ID
  "fyyf": "4501        ",              // 发药药房
  "his_con_ratio": 1.0000,             // 最小单位转换比
  "cydybz": 0                          // 额外字段（接口文档中没有）
}
```

