package com.zsm.utils;

import lombok.extern.slf4j.Slf4j;
import org.springframework.util.StringUtils;

import java.lang.reflect.Field;
import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 数据清洗工具类
 * 提供高性能的字符串字段清洗功能，支持反射和缓存优化
 * 
 * <AUTHOR>  
 * @date 2025/07/01
 */
@Slf4j
public class DataCleanUtil {

    /**
     * 方法缓存，避免重复反射操作
     * Key: 类名-字段名, Value: Setter方法
     */
    private static final Map<String, Method> SETTER_CACHE = new ConcurrentHashMap<>();

    /**
     * 字段缓存，避免重复反射操作
     * Key: 类名, Value: 字符串字段列表
     */
    private static final Map<String, List<Field>> FIELD_CACHE = new ConcurrentHashMap<>();

    /**
     * 清洗单个对象的所有字符串字段
     * 
     * @param obj 需要清洗的对象
     * @param <T> 对象类型
     * @return 清洗后的对象（原对象引用）
     */
    public static <T> T cleanObject(T obj) {
        if (obj == null) {
            return null;
        }

        Class<?> clazz = obj.getClass();
        String className = clazz.getName();

        try {
            // 从缓存获取字符串字段列表
            List<Field> stringFields = FIELD_CACHE.computeIfAbsent(className, k -> getStringFields(clazz));

            // 遍历字符串字段进行清洗
            for (Field field : stringFields) {
                cleanField(obj, field);
            }
        } catch (Exception e) {
            log.warn("数据清洗失败，对象类型：{}，错误信息：{}", className, e.getMessage());
        }

        return obj;
    }

    /**
     * 批量清洗对象列表
     * 使用并行流提高处理性能
     * 
     * @param list 需要清洗的对象列表
     * @param <T> 对象类型
     * @return 清洗后的对象列表（原列表引用）
     */
    public static <T> List<T> cleanList(List<T> list) {
        if (list == null || list.isEmpty()) {
            return list;
        }

        // 对于大数据量使用并行流，小数据量使用串行流
        if (list.size() > 100) {
            list.parallelStream().forEach(DataCleanUtil::cleanObject);
        } else {
            list.forEach(DataCleanUtil::cleanObject);
        }

        return list;
    }

    /**
     * 批量清洗对象列表（带自定义清洗函数）
     * 
     * @param list 需要清洗的对象列表
     * @param cleaner 自定义清洗函数
     * @param <T> 对象类型
     * @return 清洗后的对象列表
     */
    public static <T> List<T> cleanList(List<T> list, Function<T, T> cleaner) {
        if (list == null || list.isEmpty()) {
            return list;
        }

        return list.stream()
                .map(cleaner)
                .collect(Collectors.toList());
    }

    /**
     * 清洗字符串（去除首尾空格）
     * 
     * @param str 需要清洗的字符串
     * @return 清洗后的字符串
     */
    public static String cleanString(String str) {
        if (str == null) {
            return null;
        }
        
        String trimmed = str.trim();
        return trimmed.isEmpty() ? null : trimmed;
    }

    /**
     * 高级字符串清洗（去除首尾空格、制表符、换行符等）
     * 
     * @param str 需要清洗的字符串
     * @return 清洗后的字符串
     */
    public static String deepCleanString(String str) {
        if (str == null) {
            return null;
        }
        
        // 去除首尾空白字符（包括空格、制表符、换行符等）
        String cleaned = str.replaceAll("^\\s+|\\s+$", "");
        
        // 如果清洗后为空字符串，返回null
        return cleaned.isEmpty() ? null : cleaned;
    }

    /**
     * 获取类中所有的字符串字段
     * 
     * @param clazz 类对象
     * @return 字符串字段列表
     */
    private static List<Field> getStringFields(Class<?> clazz) {
        List<Field> stringFields = new ArrayList<>();
        
        // 获取当前类及父类的所有字段
        Class<?> currentClass = clazz;
        while (currentClass != null && currentClass != Object.class) {
            Field[] fields = currentClass.getDeclaredFields();
            
            for (Field field : fields) {
                // 只处理String类型的字段
                if (field.getType() == String.class) {
                    field.setAccessible(true);
                    stringFields.add(field);
                }
            }
            
            currentClass = currentClass.getSuperclass();
        }
        
        return stringFields;
    }

    /**
     * 清洗对象的单个字段
     * 
     * @param obj 对象实例
     * @param field 字段
     */
    private static void cleanField(Object obj, Field field) {
        try {
            // 获取字段值
            String value = (String) field.get(obj);
            
            if (value != null) {
                // 清洗字符串
                String cleanedValue = cleanString(value);
                
                // 设置清洗后的值
                field.set(obj, cleanedValue);
            }
        } catch (IllegalAccessException e) {
            // 如果直接访问失败，尝试使用setter方法
            try {
                setFieldUsingSetter(obj, field, cleanString(getFieldUsingGetter(obj, field)));
            } catch (Exception ex) {
                log.debug("清洗字段失败：{}，字段名：{}", obj.getClass().getSimpleName(), field.getName());
            }
        }
    }

    /**
     * 使用getter方法获取字段值
     * 
     * @param obj 对象实例
     * @param field 字段
     * @return 字段值
     */
    private static String getFieldUsingGetter(Object obj, Field field) throws Exception {
        String methodName = "get" + capitalize(field.getName());
        Method getter = obj.getClass().getMethod(methodName);
        return (String) getter.invoke(obj);
    }

    /**
     * 使用setter方法设置字段值
     * 
     * @param obj 对象实例  
     * @param field 字段
     * @param value 字段值
     */
    private static void setFieldUsingSetter(Object obj, Field field, String value) throws Exception {
        String cacheKey = obj.getClass().getName() + "-" + field.getName();
        
        Method setter = SETTER_CACHE.computeIfAbsent(cacheKey, k -> {
            try {
                String methodName = "set" + capitalize(field.getName());
                return obj.getClass().getMethod(methodName, String.class);
            } catch (NoSuchMethodException e) {
                return null;
            }
        });

        if (setter != null) {
            setter.invoke(obj, value);
        }
    }

    /**
     * 首字母大写
     * 
     * @param str 字符串
     * @return 首字母大写的字符串
     */
    private static String capitalize(String str) {
        if (str == null || str.isEmpty()) {
            return str;
        }
        return str.substring(0, 1).toUpperCase() + str.substring(1);
    }

    /**
     * 清空缓存（在应用关闭时调用，释放内存）
     */
    public static void clearCache() {
        SETTER_CACHE.clear();
        FIELD_CACHE.clear();
        log.info("数据清洗工具缓存已清空");
    }

    /**
     * 获取缓存统计信息
     * 
     * @return 缓存统计信息
     */
    public static Map<String, Integer> getCacheStats() {
        Map<String, Integer> stats = new HashMap<>();
        stats.put("setterCacheSize", SETTER_CACHE.size());
        stats.put("fieldCacheSize", FIELD_CACHE.size());
        return stats;
    }

    /**
     * 针对特定视图实体的清洗方法
     * 专门用于清洗VIEW_YSF_*视图的数据
     * 
     * @param viewDataList 视图数据列表
     * @param <T> 视图实体类型
     * @return 清洗后的数据列表
     */
    public static <T> List<T> cleanViewData(List<T> viewDataList) {
        if (viewDataList == null || viewDataList.isEmpty()) {
            return viewDataList;
        }

        long startTime = System.currentTimeMillis();
        
        // 使用并行流提高处理性能
        List<T> cleanedList = viewDataList.parallelStream()
                .map(DataCleanUtil::cleanObject)
                .collect(Collectors.toList());
        
        long endTime = System.currentTimeMillis();
        
        log.debug("数据清洗完成，处理记录数：{}，耗时：{}ms", 
                cleanedList.size(), endTime - startTime);
        
        return cleanedList;
    }
}