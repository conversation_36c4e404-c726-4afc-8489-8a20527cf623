package com.zsm.service.impl;

import cn.hutool.core.date.DateUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.zsm.common.SaasAuthorizationVerifyAspect;
import com.zsm.constant.NhsaAccountConstant;
import com.zsm.entity.Nhsa3505;
import com.zsm.entity.YsfStoDpsSub;
import com.zsm.mapper.Nhsa3505Mapper;
import com.zsm.mapper.YsfStoDpsSubMapper;
import com.zsm.model.ApiResult;
import com.zsm.model.domain.DateRange;
import com.zsm.model.domain.NhsaAccount;
import com.zsm.model.enums.SdDpsEnum;
import com.zsm.model.nhsa.request.DrugTracInfo;
import com.zsm.model.nhsa.request.fsi3505.Selinfo3505;
import com.zsm.model.nhsa.response.Fsi9001Response;
import com.zsm.model.nhsa.response.NhsaCityResponse;
import com.zsm.model.vo.InPatientDispenseDetailBindScatteredVo;
import com.zsm.model.vo.SaasUserInfoResponse;
import com.zsm.model.vo.InpatientPrescriptionResponseVo;
import com.zsm.model.vo.OutpatientPrescriptionResponseVo;
import com.zsm.service.Nhsa3505Service;
import com.zsm.utils.DateRangeUtil;
import com.zsm.utils.DateUtils;
import com.zsm.utils.NhsaHttpUtil;
import com.zsm.utils.NhsaRetryUtil;
import lombok.Data;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StringUtils;

import jakarta.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <p>
 * 3505销售记录报文表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-02
 */
@Slf4j
@Service
public class Nhsa3505ServiceImpl extends ServiceImpl<Nhsa3505Mapper, Nhsa3505> implements Nhsa3505Service {

    @Resource
    private YsfStoDpsSubMapper ysfStoDpsSubMapper;
	@Resource
    private NhsaRetryUtil nhsaRetryUtil;

    @Async
    @Override
    public void save3505ZyAsync(List<InpatientPrescriptionResponseVo.InpatientPrescriptionItem> dataList) {
        if (dataList == null || dataList.isEmpty()) {
            log.warn("住院处方药品数据列表为空，跳过保存操作");
            return;
        }

        log.info("开始异步保存3505住院销售记录，总数据量: {}", dataList.size());

        try {
            List<Nhsa3505> saveList = new ArrayList<>();
            int skipCount = 0;

            for (InpatientPrescriptionResponseVo.InpatientPrescriptionItem item : dataList) {
                // 检查必要字段
                if (!StringUtils.hasText(item.getFixmedins_bchno())) {
                    log.warn("定点医疗机构批次号为空，跳过该条记录: {}", item);
                    skipCount++;
                    continue;
                }

                // 通过fixmedins_bchno查询是否已存在记录
                LambdaQueryWrapper<Nhsa3505> queryWrapper = new LambdaQueryWrapper<>();
                queryWrapper.eq(Nhsa3505::getFixmedinsBchno, item.getFixmedins_bchno());

                if (count(queryWrapper) > 0) {
                    log.debug("记录已存在，跳过保存 fixmedins_bchno: {}", item.getFixmedins_bchno());
                    skipCount++;
                    continue;
                }

                // 转换为Nhsa3505实体
                Nhsa3505 entity = convertInpatientToNhsa3505(item);
                if (entity != null) {
                    saveList.add(entity);
                }
            }

            // 批量保存
            if (!saveList.isEmpty()) {
                boolean result = saveBatch(saveList);
                if (result) {
                    log.info("3505住院销售记录批量保存成功，保存数量: {}, 跳过数量: {}", saveList.size(), skipCount);
                } else {
                    log.error("3505住院销售记录批量保存失败");
                }
            } else {
                log.info("没有新的住院记录需要保存，跳过数量: {}", skipCount);
            }

        } catch (Exception e) {
            log.error("异步保存3505住院销售记录发生异常", e);
        }
    }

    @Async
    @Override
    public void save3505Async(List<OutpatientPrescriptionResponseVo.PrescriptionItem> dataList) {
        if (dataList == null || dataList.isEmpty()) {
            log.warn("处方药品数据列表为空，跳过保存操作");
            return;
        }

        log.info("开始异步保存3505销售记录，总数据量: {}", dataList.size());

        try {
            List<Nhsa3505> saveList = new ArrayList<>();
            int skipCount = 0;

            for (OutpatientPrescriptionResponseVo.PrescriptionItem item : dataList) {
                // 检查必要字段
                if (!StringUtils.hasText(item.getFixmedins_bchno())) {
                    log.warn("定点医疗机构批次号为空，跳过该条记录: {}", item);
                    skipCount++;
                    continue;
                }

                // 通过fixmedins_bchno查询是否已存在记录
                LambdaQueryWrapper<Nhsa3505> queryWrapper = new LambdaQueryWrapper<>();
                queryWrapper.eq(Nhsa3505::getFixmedinsBchno, item.getFixmedins_bchno());

                if (count(queryWrapper) > 0) {
                    log.debug("记录已存在，跳过保存 fixmedins_bchno: {}", item.getFixmedins_bchno());
                    skipCount++;
                    continue;
                }

                // 转换为Nhsa3505实体
                Nhsa3505 entity = convertToNhsa3505(item);
                if (entity != null) {
                    saveList.add(entity);
                }
            }

            // 批量保存
            if (!saveList.isEmpty()) {
                boolean result = saveBatch(saveList);
                if (result) {
                    log.info("3505销售记录批量保存成功，保存数量: {}, 跳过数量: {}", saveList.size(), skipCount);
                } else {
                    log.error("3505销售记录批量保存失败");
                }
            } else {
                log.info("没有新的记录需要保存，跳过数量: {}", skipCount);
            }

        } catch (Exception e) {
            log.error("异步保存3505销售记录发生异常", e);
        }
    }

    @Override
    public void updateDrugTraceabilityInfo(String outPresdetailid, String drugtracinfo, String userName, String orgId, String orgName) {
        if (!StringUtils.hasText(outPresdetailid) || !StringUtils.hasText(drugtracinfo)) {
            log.warn("更新追溯信息时，outPresdetailid或drugtracinfo为空");
            return;
        }

        // 查询对应的Nhsa3505记录
        LambdaQueryWrapper<Nhsa3505> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(Nhsa3505::getCfmxxh, outPresdetailid);

        Nhsa3505 nhsa3505 = getOne(queryWrapper);
        if (nhsa3505 == null) {
            log.warn("未找到对应的Nhsa3505记录，outPresdetailid: {}", outPresdetailid);
            return;
        }

        // 更新追溯码信息
        nhsa3505.setDrugTracInfo(drugtracinfo);

        boolean updated = updateById(nhsa3505);
        if (updated) {
            log.info("成功更新Nhsa3505记录，outPresdetailid: {}, drugtracinfo: {}", outPresdetailid, drugtracinfo);
        } else {
            log.error("更新Nhsa3505记录失败，outPresdetailid: {}", outPresdetailid);
        }
    }

    /**
     * 将PrescriptionItem转换为Nhsa3505实体
     */
    private Nhsa3505 convertToNhsa3505(OutpatientPrescriptionResponseVo.PrescriptionItem item) {
        try {
            Nhsa3505 entity = new Nhsa3505();

            entity.setMedicalCode(NhsaAccountConstant.getNhsaAccount().getMedicalCode());
            entity.setMedicalName(NhsaAccountConstant.getNhsaAccount().getMedicalName());

            // 基本药品信息
            entity.setMedListCodg(item.getMed_list_codg());
            entity.setFixmedinsHilistId(item.getFixmedins_hilist_id());
            entity.setFixmedinsHilistName(item.getFixmedins_hilist_name());
            entity.setFixmedinsBchno(item.getFixmedins_bchno());

            // 医师信息
            entity.setPrscDrCertno(item.getPrsc_dr_certno());
            entity.setPrscDrName(item.getPrsc_dr_name());

            // 药师信息
            entity.setPharCertno(item.getPhar_certno());
            entity.setPharName(item.getPhar_name());
            entity.setPharPracCertNo(item.getPhar_prac_cert_no());

            // 患者就诊信息
            entity.setMdtrtSn(item.getMdtrt_sn());
            entity.setPsnName(item.getPsn_name());

            // 生产信息
            entity.setManuLotnum(item.getManu_lotnum());
            if (StringUtils.hasText(item.getManu_date())) {
                entity.setManuDate(DateUtils.parseDate(item.getManu_date()));
            }
            if (StringUtils.hasText(item.getExpy_end())) {
                entity.setExpyEnd(DateUtils.parseDate(item.getExpy_end()));
            }

            // 标志信息
            entity.setRxFlag(item.getRx_flag() != null ? String.valueOf(item.getRx_flag()) : null);
            entity.setTrdnFlag(item.getTrdn_flag() != null ? String.valueOf(item.getTrdn_flag()) : null);

            // 处方信息
            entity.setRxno(item.getRxno());
            entity.setRxCircFlag(item.getRx_circ_flag());
            entity.setRtalDocno(item.getRtal_docno());
            entity.setStooutNo(item.getStoout_no());
            entity.setBchno(item.getBchno());

            // 销售信息
            if (StringUtils.hasText(item.getSel_retn_cnt())) {
                entity.setSelRetnCnt(new BigDecimal(item.getSel_retn_cnt()));
            }
            if (StringUtils.hasText(item.getMin_sel_retn_cnt())) {
                entity.setMinUnitSelRetnCnt(new BigDecimal(item.getMin_sel_retn_cnt()));
            }
            if (StringUtils.hasText(item.getSel_retn_time())) {
                entity.setSelRetnTime(DateUtils.parseDateTime(item.getSel_retn_time()));
            }
            entity.setSelRetnOpterName(item.getSel_retn_opter_name());
            entity.setMdtrtSetlType(item.getMdtrt_setl_type() != null ? String.valueOf(item.getMdtrt_setl_type()) : null);

            // 生产企业信息
            entity.setHisEntpName(item.getProdentp_name());

            // 处方序号信息
            entity.setCfxh(item.getCfxh());
            entity.setCfmxxh(item.getCfmxxh());
            entity.setSjh(item.getSjh());
            entity.setPatientId(item.getPatient_id());

            // 规格信息存储到备注字段
            if (StringUtils.hasText(item.getSpec())) {
                entity.setMemo(item.getSpec());
            }

            // 发送和退货信息
            if (StringUtils.hasText(item.getReturn_time())) {
                entity.setReturnTime(DateUtils.parseDateTime(item.getReturn_time()));
            }

            // 系统信息
            LocalDateTime now = LocalDateTime.now();
            entity.setCreateTime(now);
            entity.setCreateDate(now.toLocalDate());
            entity.setDeleteFlag("0");
            entity.setHsaSyncStatus("0"); // 未同步状态

            // 设置必填字段的默认值（如果为空）
            if (!StringUtils.hasText(entity.getMedicalName())) {
                entity.setMedicalName("默认医疗机构");
            }
            if (!StringUtils.hasText(entity.getFixmedinsHilistName())) {
                entity.setFixmedinsHilistName("未知药品");
            }
            if (!StringUtils.hasText(entity.getPrscDrName())) {
                entity.setPrscDrName("未知医师");
            }
            if (!StringUtils.hasText(entity.getPharName())) {
                entity.setPharName("未知药师");
            }
            if (!StringUtils.hasText(entity.getPsnName())) {
                entity.setPsnName("未知患者");
            }
            if (!StringUtils.hasText(entity.getSelRetnOpterName())) {
                entity.setSelRetnOpterName("系统");
            }
            if (!StringUtils.hasText(entity.getHisEntpName())) {
                entity.setHisEntpName("未知生产企业");
            }
            if (!StringUtils.hasText(entity.getHisUniqueKey())) {
                entity.setHisUniqueKey(entity.getFixmedinsBchno());
            }
            if (!StringUtils.hasText(entity.getCompositeKey())) {
                entity.setCompositeKey(entity.getFixmedinsBchno());
            }
            // 根据token获取用户信息,设置所属的用户信息,方便统计药房扫码数据信息
            SaasUserInfoResponse userInfo = SaasAuthorizationVerifyAspect.userInfoThreadLocal.get();
            entity.setYmfUserName(userInfo.getUser().getUserName());
            entity.setYmfNickName(userInfo.getUser().getNickName());
            entity.setYmfUserId(userInfo.getUser().getUserId());

            return entity;

        } catch (Exception e) {
            log.error("转换PrescriptionItem到Nhsa3505实体时发生异常: {}", item, e);
            return null;
        }
    }
    

    /**
     * 将InpatientPrescriptionItem转换为Nhsa3505实体
     */
    private Nhsa3505 convertInpatientToNhsa3505(InpatientPrescriptionResponseVo.InpatientPrescriptionItem item) {
        try {
            Nhsa3505 entity = new Nhsa3505();

            entity.setMedicalCode(NhsaAccountConstant.getNhsaAccount().getMedicalCode());
            entity.setMedicalName(NhsaAccountConstant.getNhsaAccount().getMedicalName());

            // 基本药品信息
            entity.setMedListCodg(item.getMed_list_codg());
            entity.setFixmedinsHilistId(item.getFixmedins_hilist_id());
            entity.setFixmedinsHilistName(item.getFixmedins_hilist_name());
            entity.setFixmedinsBchno(item.getFixmedins_bchno());

            // 医师信息
            entity.setPrscDrCertno(item.getPrsc_dr_certno());
            entity.setPrscDrName(item.getPrsc_dr_name());

            // 药师信息
            entity.setPharCertno(item.getPhar_certno());
            entity.setPharName(item.getPhar_name());
            entity.setPharPracCertNo(item.getPhar_prac_cert_no());

            // 患者就诊信息
            entity.setMdtrtSn(item.getMdtrt_sn());
            entity.setPsnName(item.getPsn_name());

            // 生产信息
            entity.setManuLotnum(item.getManu_lotnum());
            if (!StringUtils.hasText(item.getManu_lotnum())) {
                entity.setManuLotnum("-");
            }
            if (StringUtils.hasText(item.getManu_date())) {
                entity.setManuDate(DateUtils.parseDate(item.getManu_date()));
            }
            if (StringUtils.hasText(item.getExpy_end())) {
                entity.setExpyEnd(DateUtils.parseDate(item.getExpy_end()));
            }

            // 标志信息
            entity.setRxFlag(item.getRx_flag() != null ? String.valueOf(item.getRx_flag()) : null);
            entity.setTrdnFlag(item.getTrdn_flag() != null ? String.valueOf(item.getTrdn_flag()) : null);

            // 处方信息
            entity.setRxno(item.getRxno());
            entity.setFeedetlSn(item.getRxno());
            entity.setRxCircFlag(item.getRx_circ_flag());
            entity.setRtalDocno(item.getRtal_docno());
            if (!StringUtils.hasText(item.getRtal_docno())) {
                entity.setRtalDocno("-");
            }
            entity.setStooutNo(item.getStoout_no());
            entity.setBchno(item.getBchno());

            // 销售信息
            if (StringUtils.hasText(item.getSel_retn_cnt())) {
                entity.setSelRetnCnt(new BigDecimal(item.getSel_retn_cnt()));
            }
            if (StringUtils.hasText(item.getMin_sel_retn_cnt())) {
                entity.setMinUnitSelRetnCnt(new BigDecimal(item.getMin_sel_retn_cnt()));
            }
            if (StringUtils.hasText(item.getSel_retn_time())) {
                entity.setSelRetnTime(DateUtils.parseDateTime(item.getSel_retn_time()));
            }
            entity.setSelRetnOpterName(item.getSel_retn_opter_name());
            entity.setMdtrtSetlType(item.getMdtrt_setl_type() != null ? String.valueOf(item.getMdtrt_setl_type()) : null);

            // 生产企业信息
            entity.setHisEntpName(item.getProdentp_name());

            // 处方序号信息
            entity.setCfxh(item.getCfxh());
            entity.setCfmxxh(item.getCfmxxh());
            entity.setSjh(item.getSjh());
            entity.setPatientId(item.getPatient_id());

            // 规格信息存储到备注字段
            if (StringUtils.hasText(item.getSpec())) {
                entity.setMemo(item.getSpec());
            }

            // 发送和退货信息
            if (StringUtils.hasText(item.getReturn_time())) {
                entity.setReturnTime(DateUtils.parseDateTime(item.getReturn_time()));
            }

            // 系统信息
            LocalDateTime now = LocalDateTime.now();
            entity.setCreateTime(now);
            entity.setCreateDate(now.toLocalDate());
            entity.setDeleteFlag("0");
            entity.setHsaSyncStatus("0"); // 未同步状态

            // 设置必填字段的默认值（如果为空）
            if (!StringUtils.hasText(entity.getPrscDrName())) {
                entity.setPrscDrName("-");
            }
            if (!StringUtils.hasText(entity.getPharName())) {
                entity.setPharName("-");
            }
            if (!StringUtils.hasText(entity.getPsnName())) {
                entity.setPsnName("-");
            }
            if (!StringUtils.hasText(entity.getSelRetnOpterName())) {
                entity.setSelRetnOpterName("-");
            }
            if (!StringUtils.hasText(entity.getHisEntpName())) {
                entity.setHisEntpName("-");
            }
            if (!StringUtils.hasText(entity.getHisUniqueKey())) {
                entity.setHisUniqueKey(entity.getFixmedinsBchno());
            }
            if (!StringUtils.hasText(entity.getCompositeKey())) {
                entity.setCompositeKey(entity.getFixmedinsBchno());
            }
            // 根据token获取用户信息,设置所属的用户信息,方便统计药房扫码数据信息
            SaasUserInfoResponse userInfo = SaasAuthorizationVerifyAspect.userInfoThreadLocal.get();
            entity.setYmfUserName(userInfo.getUser().getUserName());
            entity.setYmfNickName(userInfo.getUser().getNickName());
            entity.setYmfUserId(userInfo.getUser().getUserId());

            return entity;

        } catch (Exception e) {
            log.error("转换InpatientPrescriptionItem到Nhsa3505实体时发生异常: {}", item, e);
            return null;
        }
    }


    /**
     * 将数据上传到两定接口平台
     */
    public void uploadDataToPlatform() {
        try {
            // 同步追溯码信息
            syncDrugTraceInfo();

            // 查询需要上传的数据
            List<Nhsa3505> dataList = queryDataToUpload(null, null);
            if (ObjectUtils.isEmpty(dataList)) {
                log.info("没有需要上传的数据");
                return;
            }

            // 处理上传逻辑
            processUpload(dataList, false);

        } catch (Exception e) {
            log.error("上传数据到两定接口平台时发生异常", e);
        }
    }


    @Override
    public ApiResult<String> manualUploadDataToPlatform(String cfmxxh, String cfxh) {
        try {
            log.info("开始手动上传数据到两定接口平台，cfmxxh: {}, cfxh: {}", cfmxxh, cfxh);

            // 同步追溯码信息
            syncDrugTraceInfo();

            // 查询需要上传的数据
            List<Nhsa3505> dataList = queryDataToUpload(cfmxxh, cfxh);
            if (ObjectUtils.isEmpty(dataList)) {
                log.info("没有符合条件的数据需要上传");
                return ApiResult.success("没有符合条件的数据需要上传");
            }

            log.info("查询到符合条件的数据数量: {}", dataList.size());

            // 处理上传逻辑并返回结果
            return processUpload(dataList, true);

        } catch (Exception e) {
            log.error("手动上传数据到两定接口平台时发生异常", e);
            return ApiResult.error("手动上传失败: " + e.getMessage());
        }
    }

    /**
     * 同步发药明细表的追溯码信息到销售表
     */
    private void syncDrugTraceInfo() {
        List<YsfStoDpsSub> dpsSubList = ysfStoDpsSubMapper.getSendStatusList();
        log.info("查询到发药单明细数量:{}", dpsSubList.size());

        for (YsfStoDpsSub sub : dpsSubList) {
            Nhsa3505 nhsa3505 = baseMapper.getByCfxhandDrugCode(sub.getCfxh(), sub.getDrugCode());
            if (!ObjectUtils.isEmpty(nhsa3505)) {
                if (ObjectUtils.isEmpty(nhsa3505.getDrugTracInfo())) {
                    nhsa3505.setDrugTracInfo(sub.getDrugtracinfo());
                    baseMapper.updateById(nhsa3505);
                }
            }
        }
    }

    /**
     * 查询需要上传的数据
     *
     * @param cfmxxh 处方明细序号，可为空
     * @param cfxh   处方序号，可为空
     * @return 符合条件的数据列表
     */
    private List<Nhsa3505> queryDataToUpload(String cfmxxh, String cfxh) {
        LambdaQueryWrapper<Nhsa3505> qw = new LambdaQueryWrapper<>();
        // 本次统计周期内的数据
        DateRange weeklyRange = DateRangeUtil.calculateCurrentWeekRange();
        qw.ge(Nhsa3505::getSelRetnTime, weeklyRange.getStartDate());
        qw.le(Nhsa3505::getSelRetnTime, weeklyRange.getEndDate());

        qw.eq(Nhsa3505::getHsaSyncStatus, "0"); // 未同步的数据
        // qw.eq(Nhsa3505::getMdtrtSetlType, "1"); // 只查询医保结算的数据
        qw.eq(Nhsa3505::getSdDps, SdDpsEnum.OUTPATIENT); // 只查询门诊的处方数据
        qw.ne(Nhsa3505::getDrugTracInfo, "");
        qw.isNotNull(Nhsa3505::getDrugTracInfo);
        qw.ne(Nhsa3505::getMdtrtSn, "");
        qw.isNotNull(Nhsa3505::getMdtrtSn);

        // 根据传入的参数进行条件筛选
        if (StringUtils.hasText(cfmxxh)) {
            qw.eq(Nhsa3505::getCfmxxh, cfmxxh);
            log.info("按处方明细序号筛选，cfmxxh: {}", cfmxxh);
        }
        if (StringUtils.hasText(cfxh)) {
            qw.eq(Nhsa3505::getCfxh, cfxh);
            log.info("按处方序号筛选，cfxh: {}", cfxh);
        }

        return baseMapper.selectList(qw);
    }

    /**
     * 构建Selinfo3505对象
     *
     * @param nhsa3505      源数据对象
     * @param lastYearToday 去年今天的日期（用于默认生产日期）
     * @param nextYearToday 明年今天的日期（用于默认过期日期）
     * @return 构建好的Selinfo3505对象
     */
    private Selinfo3505 buildSelinfo3505(Nhsa3505 nhsa3505, LocalDate lastYearToday, LocalDate nextYearToday) {
        Selinfo3505 selinfo3505 = new Selinfo3505();

        // 设置基本字段
        if (!ObjectUtils.isEmpty(nhsa3505.getMedListCodg())) {
            selinfo3505.setMed_list_codg(nhsa3505.getMedListCodg());
        } else {
            selinfo3505.setMed_list_codg("-");
        }

        selinfo3505.setFixmedins_hilist_id(nhsa3505.getFixmedinsHilistId());
        selinfo3505.setFixmedins_hilist_name(nhsa3505.getFixmedinsHilistName());
        selinfo3505.setFixmedins_bchno(nhsa3505.getFixmedinsBchno());
        selinfo3505.setPrsc_dr_cert_type(nhsa3505.getPrscDrCertType());
        selinfo3505.setPrsc_dr_certno(nhsa3505.getPrscDrCertno());
        selinfo3505.setPrsc_dr_name(nhsa3505.getPrscDrName());
        selinfo3505.setPhar_cert_type(nhsa3505.getPharCertType());
        selinfo3505.setPhar_certno(nhsa3505.getPharCertno());
        selinfo3505.setPhar_name(nhsa3505.getPharName());
        selinfo3505.setPhar_prac_cert_no(nhsa3505.getPharPracCertNo());
        selinfo3505.setHi_feesetl_type(nhsa3505.getHiFeesetlType());
        selinfo3505.setSetl_id(nhsa3505.getSetlId());
        selinfo3505.setMdtrt_sn(nhsa3505.getMdtrtSn());
        selinfo3505.setPsn_no(nhsa3505.getPsnNo());
        selinfo3505.setPsn_cert_type(nhsa3505.getPsnCertType());
        selinfo3505.setCertno(nhsa3505.getCertno());
        selinfo3505.setPsn_name(nhsa3505.getPsnName());
        selinfo3505.setManu_lotnum(nhsa3505.getManuLotnum());

        // 设置日期字段（带默认值）
        if (!ObjectUtils.isEmpty(nhsa3505.getManuDate())) {
            selinfo3505.setManu_date(nhsa3505.getManuDate());
        } else {
            selinfo3505.setManu_date(lastYearToday);
        }
        if (!ObjectUtils.isEmpty(nhsa3505.getExpyEnd())) {
            selinfo3505.setExpy_end(nhsa3505.getExpyEnd());
        } else {
            selinfo3505.setExpy_end(nextYearToday);
        }

        // 设置其他字段
        selinfo3505.setRx_flag(nhsa3505.getRxFlag());
        selinfo3505.setTrdn_flag(nhsa3505.getTrdnFlag());
        selinfo3505.setMemo(nhsa3505.getMemo());
        selinfo3505.setFinl_trns_pric(nhsa3505.getFinlTrnsPric());
        selinfo3505.setRxno(nhsa3505.getRxno());
        selinfo3505.setRx_circ_flag(nhsa3505.getRxCircFlag());
        selinfo3505.setRtal_docno(nhsa3505.getRtalDocno());
        selinfo3505.setStoout_no(nhsa3505.getStooutNo());
        selinfo3505.setBchno(nhsa3505.getBchno());
        selinfo3505.setDrug_prod_barc(nhsa3505.getDrugProdBarc());
        selinfo3505.setShelf_posi(nhsa3505.getShelfPosi());
        selinfo3505.setSel_retn_time(nhsa3505.getSelRetnTime());
        selinfo3505.setSel_retn_opter_name(nhsa3505.getSelRetnOpterName());
        selinfo3505.setMdtrt_setl_type(nhsa3505.getMdtrtSetlType());
        selinfo3505.setSel_retn_cnt(nhsa3505.getSelRetnCnt());

        // 设置追溯信息
        if (!ObjectUtils.isEmpty(nhsa3505.getDrugTracInfo())) {
            String[] split = nhsa3505.getDrugTracInfo()
                    .split(",");
            List<DrugTracInfo> drugtracinfo = Arrays.stream(split)
                    .map(a -> DrugTracInfo.builder()
                            .drug_trac_codg(a)
                            .build())
                    .collect(Collectors.toList());
            selinfo3505.setDrugtracinfo(drugtracinfo);
        }

        return selinfo3505;
    }

    /**
     * 处理数据上传到两定接口平台
     *
     * @param dataList 需要上传的数据列表
     * @param isManual 是否为手动上传
     * @return 如果是手动上传则返回ApiResult，否则返回null
     */
    public ApiResult<String> processUpload(List<Nhsa3505> dataList, boolean isManual) {
        try {
            // 获取医保操作员的账号信息
            NhsaAccount nhsaAccount = NhsaAccountConstant.getNhsaAccount();

            // 获取今天的日期和默认日期
            LocalDate today = LocalDate.now();
            LocalDate lastYearToday = today.minusYears(1);
            LocalDate nextYearToday = today.plusYears(1);


            // 统计信息（用于手动上传）
            int successCount = 0;
            int failCount = 0;
            StringBuilder errorMessages = new StringBuilder();

            // 逐条处理数据
            for (Nhsa3505 nhsa3505 : dataList) {
                try {
                    // 构建请求对象
                    Selinfo3505 selinfo3505 = buildSelinfo3505(nhsa3505, lastYearToday, nextYearToday);

                    // 调用接口上传（带自动重试）
                    NhsaCityResponse response = nhsaRetryUtil.executeWithRetry(nhsaAccount,
                        currentSignNo -> NhsaHttpUtil.fsi3505(currentSignNo, selinfo3505, nhsaAccount));
                    // 处理响应结果
                    if (response.getBody()
                            .getInfcode() == 0) {
                        // 上传成功
                        String nowTime = LocalDateTime.now()
                                .format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
                        String successRemark = isManual ? "手动上传两定接口成功 " + nowTime : "上传两定接口成功" + nowTime;

                        nhsa3505.setHsaSyncRemark(successRemark);
                        nhsa3505.setHsaSyncStatus("1");
                        nhsa3505.setHsaSyncTime(LocalDateTime.now());
                        nhsa3505.setDrugTracInfo(nhsa3505.getDrugTracInfo());
                        baseMapper.updateById(nhsa3505);

                        successCount++;
                        if (isManual) {
                            log.info("成功上传数据，cfmxxh: {}, cfxh: {}", nhsa3505.getCfmxxh(), nhsa3505.getCfxh());
                        }
                    } else {
                        // 上传失败
                        String errorMsg = "上传两定接口失败，错误内容：" + response.getBody()
                                .getErr_msg();
                        nhsa3505.setHsaSyncRemark(errorMsg);
                        nhsa3505.setHsaSyncStatus("2");
                        nhsa3505.setHsaSyncTime(LocalDateTime.now());
                        nhsa3505.setDrugTracInfo(nhsa3505.getDrugTracInfo());
                        baseMapper.updateById(nhsa3505);

                        failCount++;
                        if (isManual) {
                            errorMessages.append("cfmxxh: ")
                                    .append(nhsa3505.getCfmxxh())
                                    .append(", cfxh: ")
                                    .append(nhsa3505.getCfxh())
                                    .append(" - ")
                                    .append(response.getBody()
                                            .getErr_msg())
                                    .append("; ");
                            log.error("上传数据失败，cfmxxh: {}, cfxh: {}, 错误: {}",
                                    nhsa3505.getCfmxxh(), nhsa3505.getCfxh(), response.getBody()
                                            .getErr_msg());
                        }
                    }
                } catch (Exception e) {
                    failCount++;
                    if (isManual) {
                        String errorMsg = "处理数据时发生异常: " + e.getMessage();
                        errorMessages.append("cfmxxh: ")
                                .append(nhsa3505.getCfmxxh())
                                .append(", cfxh: ")
                                .append(nhsa3505.getCfxh())
                                .append(" - ")
                                .append(errorMsg)
                                .append("; ");
                        log.error("处理数据时发生异常，cfmxxh: {}, cfxh: {}", nhsa3505.getCfmxxh(), nhsa3505.getCfxh(), e);
                    } else {
                        log.error("处理数据时发生异常", e);
                    }
                }
            }

            // 返回结果（仅手动上传需要返回）
            if (isManual) {
                String resultMessage = String.format("手动上传完成。总数: %d, 成功: %d, 失败: %d",
                        dataList.size(), successCount, failCount);
                if (failCount > 0) {
                    resultMessage += "。失败详情: " + errorMessages;
                }

                log.info(resultMessage);

                if (failCount == 0) {
                    return ApiResult.success(resultMessage);
                } else if (successCount > 0) {
                    return ApiResult.success(resultMessage); // 部分成功也返回success，但包含失败信息
                } else {
                    return ApiResult.error(resultMessage);
                }
            } else {
                log.info("批量上传完成，总数: {}, 成功: {}, 失败: {}", dataList.size(), successCount, failCount);
            }

            return ApiResult.success();

        } catch (Exception e) {
            log.error("处理上传逻辑时发生异常", e);
            if (isManual) {
                return ApiResult.error("处理上传失败: " + e.getMessage());
            }
            throw e;
        }
    }
    /**
     * 执行追溯码复用
     * 将已上传成功的拆零药品追溯码复用到相同药品编码的未赋码记录上
     */
    @Override
    public ApiResult<Map<String, Object>> reuseTraceCodesForPeriod(String startDate, String endDate) {
        Map<String, Object> resultMap = new HashMap<>();
        try {
            log.info("开始执行追溯码复用，时间范围：{} 至 {}", startDate, endDate);

            // 1. 查询当前统计周期的所有数据
            List<Nhsa3505> currentPeriodData = this.lambdaQuery()
                    .ge(Nhsa3505::getSelRetnTime, DateUtil.beginOfDay(DateUtil.parse(startDate)))
                    .le(Nhsa3505::getSelRetnTime, DateUtil.endOfDay(DateUtil.parse(endDate)))
                    .eq(Nhsa3505::getSdDps, SdDpsEnum.INPATIENT)
                    .eq(Nhsa3505::getTrdnFlag,"1")
                    .eq(Nhsa3505::getMdtrtSetlType, "1")
                    .list();

            log.info("查询到当前周期数据总量：{} 条", currentPeriodData.size());

            // 2. 构建追溯码池
            Map<String, List<TraceCodeInfo>> traceCodePool = buildTraceCodePool(currentPeriodData);
            log.info("构建追溯码池完成，包含 {} 种药品的追溯码", traceCodePool.size());

            // 3. 筛选需要赋码的数据
            List<Nhsa3505> needTraceCodeData = currentPeriodData.stream()
                    .filter(item -> (item.getDrugTracInfo() == null || item.getDrugTracInfo().trim().isEmpty())
                            && "0".equals(item.getHsaSyncStatus()))
                    .collect(Collectors.toList());

            log.info("筛选出需要赋码的拆零药品记录：{} 条", needTraceCodeData.size());
            log.info("筛选出需要赋码的拆零药品记录：{} ", JSONUtil.toJsonStr(needTraceCodeData));

            // 4. 执行追溯码复用分配
            int assignedCount = assignReusableTraceCodes(needTraceCodeData, traceCodePool);

            // 5. 批量更新数据库
            if (assignedCount > 0) {
                log.info("开始批量更新数据库，共 {} 条记录", assignedCount);
                log.info("开始批量更新数据库，数据详情:{}", JSONUtil.toJsonStr(needTraceCodeData));
                boolean updateSuccess = this.updateBatchById(needTraceCodeData);
                if (updateSuccess) {
                    log.info("批量更新成功，共更新 {} 条记录", assignedCount);
                } else {
                    log.error("批量更新失败");
                    return ApiResult.error("批量更新数据库失败");
                }
            }

            // 6. 构建返回结果
            resultMap.put("totalData", currentPeriodData.size());
            resultMap.put("traceCodePoolSize", traceCodePool.size());
            resultMap.put("needAssignCount", needTraceCodeData.size());
            resultMap.put("actualAssignedCount", assignedCount);
            resultMap.put("startDate", startDate);
            resultMap.put("endDate", endDate);
            resultMap.put("executeTime", LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));

            // 添加详细的药品追溯码分配统计
            Map<String, Integer> drugAssignStats = new HashMap<>();
            for (Nhsa3505 item : needTraceCodeData) {
                if (item.getDrugTracInfo() != null && !item.getDrugTracInfo().trim().isEmpty()) {
                    String drugName = item.getFixmedinsHilistName();
                    drugAssignStats.put(drugName, drugAssignStats.getOrDefault(drugName, 0) + 1);
                }
            }
            resultMap.put("drugAssignStats", drugAssignStats);

            log.info("追溯码复用执行完成，处理结果：{}", resultMap);
            return ApiResult.success("追溯码复用执行成功", resultMap);

        } catch (Exception e) {
            log.error("执行追溯码复用时发生异常", e);
            resultMap.put("error", e.getMessage());
            return ApiResult.error("执行追溯码复用失败：" + e.getMessage());
        }
    }
    
    /**
     * 构建追溯码池
     * 按药品编码分组，收集已成功上传的拆零追溯码
     */
    private Map<String, List<TraceCodeInfo>> buildTraceCodePool(List<Nhsa3505> currentPeriodData) {
        Map<String, List<TraceCodeInfo>> traceCodePool = new HashMap<>();
        
        currentPeriodData.stream()
                // 筛选条件：已上传成功 + 拆零标识 + 有追溯码
                .filter(item -> "1".equals(item.getTrdnFlag())
                        && item.getDrugTracInfo() != null 
                        && !item.getDrugTracInfo().trim().isEmpty())
                .forEach(item -> {
                    String drugCode = item.getMedListCodg();
                    if (drugCode == null || drugCode.trim().isEmpty()) {
                        return;
                    }
                    
                    String[] traceCodes = item.getDrugTracInfo().split(",");
                    
                    // 对分割后的追溯码进行去重处理
                    String[] uniqueTraceCodes = Arrays.stream(traceCodes)
                            .map(String::trim)
                            .filter(code -> !code.isEmpty())
                            .distinct()
                            .toArray(String[]::new);
                    
                    List<TraceCodeInfo> codeList = traceCodePool
                            .computeIfAbsent(drugCode, k -> new ArrayList<>());
                    
                    for (String code : uniqueTraceCodes) {
                        codeList.add(new TraceCodeInfo(
                                code, 
                                item.getSelRetnTime(),
                                item.getManuLotnum(),
                                item.getFixmedinsHilistName()
                        ));
                    }
                });
        
        // 记录每种药品的追溯码数量
        traceCodePool.forEach((drugCode, codes) -> {
            log.debug("药品编码 {} 可用追溯码数量：{}", drugCode, codes.size());
        });
        
        return traceCodePool;
    }
    
    /**
     * 追溯码复用分配
     */
    private int assignReusableTraceCodes(List<Nhsa3505> needTraceCodeData, 
                                         Map<String, List<TraceCodeInfo>> traceCodePool) {
        int assignedCount = 0;
        
        for (Nhsa3505 item : needTraceCodeData) {
            // 只处理拆零且无追溯码的记录
            if (!"1".equals(item.getTrdnFlag()) 
                    || (item.getDrugTracInfo() != null && !item.getDrugTracInfo().trim().isEmpty())) {
                continue;
            }
            
            String drugCode = item.getMedListCodg();
            if (drugCode == null || drugCode.trim().isEmpty()) {
                log.warn("记录 {} 的药品编码为空，跳过", item.getId());
                continue;
            }
            
            List<TraceCodeInfo> availableCodes = traceCodePool.get(drugCode);
            
            if (availableCodes != null && !availableCodes.isEmpty()) {
                // 使用优先策略选择追溯码
                TraceCodeInfo selectedCode = selectOptimalTraceCode(
                        availableCodes, 
                        item.getSelRetnTime(),
                        item.getManuLotnum()
                );
                
                if (selectedCode != null) {
                    // 赋值追溯码
                    item.setDrugTracInfo(selectedCode.getCode());
                    
                    // 添加备注说明
                    String remark = String.format("复用拆零追溯码，来源批次号：%s", 
                            selectedCode.getBatchNo());
                    item.setRemark(remark);
                    
                    assignedCount++;
                    log.info("药品 {} ({}) 复用追溯码：{}，来源批次：{}", 
                            item.getFixmedinsHilistName(), 
                            drugCode, 
                            selectedCode.getCode(),
                            selectedCode.getBatchNo());
                }
            } else {
                log.debug("药品 {} ({}) 无可用的追溯码", 
                        item.getFixmedinsHilistName(), drugCode);
            }
        }
        
        log.info("追溯码分配完成，成功分配 {} 条记录", assignedCount);
        return assignedCount;
    }
    
    /**
     * 选择最优追溯码（批次号优先原则）
     */
    private TraceCodeInfo selectOptimalTraceCode(List<TraceCodeInfo> codes, 
                                                 LocalDateTime targetTime,
                                                 String targetBatchNo) {
        if (codes == null || codes.isEmpty()) {
            return null;
        }
        
        // 1. 优先选择相同批次号的追溯码（随机分配）
        if (targetBatchNo != null && !targetBatchNo.trim().isEmpty()) {
            List<TraceCodeInfo> sameBatchCodes = codes.stream()
                    .filter(code -> targetBatchNo.equals(code.getBatchNo()))
                    .collect(java.util.stream.Collectors.toList());
            
            Optional<TraceCodeInfo> sameBatchCode = sameBatchCodes.isEmpty() 
                    ? Optional.empty() 
                    : Optional.of(sameBatchCodes.get(new java.util.Random().nextInt(sameBatchCodes.size())));
            
            if (sameBatchCode.isPresent()) {
                log.info("随机选择了相同批次号 {} 的追溯码（共{}个可选）", targetBatchNo, sameBatchCodes.size());
                return sameBatchCode.get();
            }
        }
        
        // 2. 如果没有相同批次号，则按时间最近原则选择
        if (targetTime == null) {
            return codes.get(0);
        }
        
        return codes.stream()
                .filter(code -> code != null && code.getUseTime() != null)
                .min(Comparator.comparing(code -> 
                        Math.abs(java.time.Duration.between(code.getUseTime(), targetTime).toMinutes())))
                .orElse(codes.get(0));
    }
    
    /**
     * 追溯码信息内部类
     */
    @Getter
    private static class TraceCodeInfo {
        private final String code;           // 追溯码
        private final LocalDateTime useTime; // 使用时间
        private final String batchNo;        // 批次号
        private final String drugName;       // 药品名称
        
        public TraceCodeInfo(String code, LocalDateTime useTime, String batchNo, String drugName) {
            this.code = code;
            this.useTime = useTime;
            this.batchNo = batchNo;
            this.drugName = drugName;
        }
    }
}
