package com.zsm.model.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.Date;

/**
 * 发药单查询请求参数
 *
 * <AUTHOR>
 * @date 2025/06/03
 */
@Data
@Schema(description = "发药单查询请求参数")
public class DispensingRecordQueryDto {

    @Schema(description = "患者姓名")
    private String patientName;

    @Schema(description = "处方号 (cfxh 或 outPresId)")
    private String prescriptionId;

    @Schema(description = "发药单状态 (fg_status)")
    private String dispensingStatus;

    @Schema(description = "发药科室ID")
    private String deptId;

    @Schema(description = "发药开始时间")
    private Date startTime;

    @Schema(description = "发药结束时间")
    private Date endTime;

    @Schema(description = "页码", example = "1")
    private Integer pageNum;

    @Schema(description = "每页数量", example = "10")
    private Integer pageSize;

    /**
     * 退药单标记;0: 发药, 1: 退药
     */
    @Schema(description = "退药单标记;0: 发药, 1: 退药")
    private String fgDps;
    /**
     * 发药单类型;1: 住院处方, 2:门诊处方
     */
    @Schema(description = "发药单类型;1: 住院处方, 2:门诊处方")
    private String sdDps;

    /**
     * 患者病区ID
     */
    @Schema(description = "患者病区ID")
    private String patWardId;
} 