package com.zsm.mapper;

import com.zsm.entity.YsfStoTcTask;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.zsm.model.dto.YsfStoTcTaskQueryDto;
import com.zsm.model.vo.YsfStoDpsTaskVo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 扫码任务记录表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-02
 */
@Mapper
public interface YsfStoTcTaskMapper extends BaseMapper<YsfStoTcTask> {
/**
     * 查询待处理任务关联的发药单患者信息和处方序号
     *
     * @return 患者ID和处方序号列表
     */
    List<YsfStoDpsTaskVo> selectDpsInfoByPendingTask();

    /**
     * 根据条件查询扫码任务分页列表
     *
     * @param page 分页参数
     * @param queryDto 查询条件
     * @return 任务分页列表
     */
    IPage<YsfStoTcTask> selectTaskList(IPage<YsfStoTcTask> page, @Param("queryDto") YsfStoTcTaskQueryDto queryDto);

    /**
     * 批量根据处方号查询最新任务
     * 
     * @param cfxhList 处方号列表
     * @return 最新任务列表
     */
    List<YsfStoTcTask> selectLatestByCfxh(@Param("cfxhList") List<String> cfxhList);
}
