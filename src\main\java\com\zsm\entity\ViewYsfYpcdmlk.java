package com.zsm.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.math.BigDecimal;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 阜阳肿瘤药品查代码清单库视图实体类
 * 
 * <AUTHOR>
 * @since 2025-07-01
 */
@Data
@TableName("VIEW_YSF_YPCDMLK")
@Schema(description = "阜阳肿瘤药品查代码清单库视图")
public class ViewYsfYpcdmlk implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * ID
     */
    @Schema(description = "ID")
    @TableField("idm")
    private Long idm;

    /**
     * 规格ID
     */
    @Schema(description = "规格ID")
    @TableField("gg_idm")
    private Long ggIdm;

    /**
     * 临床ID
     */
    @Schema(description = "临床ID")
    @TableField("lc_idm")
    private Long lcIdm;

    /**
     * 药品流号
     */
    @Schema(description = "药品流号")
    @TableField("yplh")
    private String yplh;

    /**
     * 药品名称
     */
    @Schema(description = "药品名称")
    @TableField("ypmc")
    private String ypmc;

    /**
     * 药品代码
     */
    @Schema(description = "药品代码")
    @TableField("ypdm")
    private String ypdm;

    /**
     * 拼音
     */
    @Schema(description = "拼音")
    @TableField("py")
    private String py;

    /**
     * 五笔
     */
    @Schema(description = "五笔")
    @TableField("wb")
    private String wb;

    /**
     * 药品规格
     */
    @Schema(description = "药品规格")
    @TableField("ypgg")
    private String ypgg;

    /**
     * 剂型代码
     */
    @Schema(description = "剂型代码")
    @TableField("jxdm")
    private String jxdm;

    /**
     * 分类代码
     */
    @Schema(description = "分类代码")
    @TableField("fldm")
    private String fldm;

    /**
     * 最小单位
     */
    @Schema(description = "最小单位")
    @TableField("zxdw")
    private String zxdw;

    /**
     * 规格单位
     */
    @Schema(description = "规格单位")
    @TableField("ggdw")
    private String ggdw;

    /**
     * 规格系数
     */
    @Schema(description = "规格系数")
    @TableField("ggxs")
    private BigDecimal ggxs;

    /**
     * 厂家代码
     */
    @Schema(description = "厂家代码")
    @TableField("cjdm")
    private String cjdm;

    /**
     * 厂家名称
     */
    @Schema(description = "厂家名称")
    @TableField("cjmc")
    private String cjmc;

    /**
     * 有效期
     */
    @Schema(description = "有效期")
    @TableField("yxq")
    private Integer yxq;

    /**
     * 药库单位
     */
    @Schema(description = "药库单位")
    @TableField("ykdw")
    private String ykdw;

    /**
     * 药库系数
     */
    @Schema(description = "药库系数")
    @TableField("ykxs")
    private BigDecimal ykxs;

    /**
     * 进货单位
     */
    @Schema(description = "进货单位")
    @TableField("jhdw")
    private String jhdw;

    /**
     * 进货系数
     */
    @Schema(description = "进货系数")
    @TableField("jhxs")
    private BigDecimal jhxs;

    /**
     * 门诊单位
     */
    @Schema(description = "门诊单位")
    @TableField("mzdw")
    private String mzdw;

    /**
     * 门诊系数
     */
    @Schema(description = "门诊系数")
    @TableField("mzxs")
    private BigDecimal mzxs;

    /**
     * 住院单位
     */
    @Schema(description = "住院单位")
    @TableField("zydw")
    private String zydw;

    /**
     * 住院系数
     */
    @Schema(description = "住院系数")
    @TableField("zyxs")
    private BigDecimal zyxs;

    /**
     * 医疗售价
     */
    @Schema(description = "医疗售价")
    @TableField("ylsj")
    private String ylsj;

    /**
     * 药品售价
     */
    @Schema(description = "药品售价")
    @TableField("ypfj")
    private String ypfj;

    /**
     * 默认进价
     */
    @Schema(description = "默认进价")
    @TableField("mrjj")
    private String mrjj;

    /**
     * 批准文号
     */
    @Schema(description = "批准文号")
    @TableField("pzwh")
    private String pzwh;

    /**
     * 对应代码_省管省医保
     */
    @Schema(description = "对应代码_省管省医保")
    @TableField("dydm_sgsyb")
    private String dydmSgsyb;

    /**
     * 对应代码_安徽省管省医保
     */
    @Schema(description = "对应代码_安徽省管省医保")
    @TableField("dydm_ahsgsyb")
    private String dydmAhsgsyb;
}