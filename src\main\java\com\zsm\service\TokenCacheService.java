package com.zsm.service;

import com.zsm.model.saas.response.AccessTokenReponse;
import com.zsm.utils.RedisUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * Token缓存服务类
 * 负责管理用户访问令牌的缓存逻辑
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Slf4j
@Service
public class TokenCacheService {

    @Autowired
    private RedisUtil redisUtil;

    /**
     * Token缓存键前缀
     */
    private static final String TOKEN_CACHE_PREFIX = "saas:token:";

    /**
     * 生成缓存键
     * 
     * @param userAccount 用户账号
     * @return 缓存键
     */
    private String generateCacheKey(String userAccount) {
        return TOKEN_CACHE_PREFIX + userAccount;
    }

    /**
     * 从缓存中获取访问令牌
     * 
     * @param userAccount 用户账号
     * @return AccessTokenReponse 访问令牌响应对象，如果缓存不存在或已过期则返回null
     */
    public AccessTokenReponse getTokenFromCache(String userAccount) {
        try {
            String cacheKey = generateCacheKey(userAccount);
            AccessTokenReponse tokenResponse = redisUtil.get(cacheKey);
            
            if (tokenResponse != null) {
                log.debug("从缓存中获取到Token, userAccount: {}, authorization: {}", 
                         userAccount, tokenResponse.getAuthorization());
                return tokenResponse;
            } else {
                log.debug("缓存中没有找到Token, userAccount: {}", userAccount);
                return null;
            }
        } catch (Exception e) {
            log.error("从缓存中获取Token失败, userAccount: {}", userAccount, e);
            return null;
        }
    }

    /**
     * 将访问令牌缓存到Redis，设置为当天24点过期
     * 
     * @param userAccount   用户账号
     * @param tokenResponse 访问令牌响应对象
     */
    public void cacheToken(String userAccount, AccessTokenReponse tokenResponse) {
        try {
            if (tokenResponse == null || tokenResponse.getAuthorization() == null) {
                log.warn("Token响应为空或authorization为空，不进行缓存, userAccount: {}", userAccount);
                return;
            }

            String cacheKey = generateCacheKey(userAccount);
            // 设置缓存到当天24点过期
            redisUtil.setUntilMidnight(cacheKey, tokenResponse);
            
            log.info("Token已缓存，设置为当天24点过期, userAccount: {}, authorization: {}", 
                    userAccount, tokenResponse.getAuthorization());
        } catch (Exception e) {
            log.error("缓存Token失败, userAccount: {}", userAccount, e);
        }
    }

    /**
     * 清除指定用户的Token缓存
     * 
     * @param userAccount 用户账号
     */
    public void clearTokenCache(String userAccount) {
        try {
            String cacheKey = generateCacheKey(userAccount);
            redisUtil.delete(cacheKey);
            log.info("Token缓存已清除, userAccount: {}", userAccount);
        } catch (Exception e) {
            log.error("清除Token缓存失败, userAccount: {}", userAccount, e);
        }
    }

    /**
     * 检查Token是否存在于缓存中
     * 
     * @param userAccount 用户账号
     * @return true 存在 false 不存在
     */
    public boolean hasTokenInCache(String userAccount) {
        try {
            String cacheKey = generateCacheKey(userAccount);
            return redisUtil.hasKey(cacheKey);
        } catch (Exception e) {
            log.error("检查Token缓存是否存在失败, userAccount: {}", userAccount, e);
            return false;
        }
    }

    /**
     * 获取Token缓存的剩余过期时间
     * 
     * @param userAccount 用户账号
     * @return 剩余过期时间(秒)，-1表示永不过期，-2表示key不存在
     */
    public long getTokenCacheExpire(String userAccount) {
        try {
            String cacheKey = generateCacheKey(userAccount);
            return redisUtil.getExpire(cacheKey);
        } catch (Exception e) {
            log.error("获取Token缓存过期时间失败, userAccount: {}", userAccount, e);
            return -2;
        }
    }
}