package com.zsm.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.math.BigDecimal;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 阜阳肿瘤门诊发药处方视图实体类
 * 
 * <AUTHOR>
 * @since 2025-07-01
 */
@Data
@TableName("VIEW_YSF_MZFYCF")
@Schema(description = "阜阳肿瘤门诊发药处方视图")
public class ViewYsfMzfycf implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 患者id
     */
    @Schema(description = "患者id")
    @TableField("patid")
    private String patid;

    /**
     * 规格
     */
    @Schema(description = "规格")
    @TableField("spec")
    private String spec;

    /**
     * 生产企业名称
     */
    @Schema(description = "生产企业名称")
    @TableField("prodentp_name")
    private String prodentpName;

    /**
     * 医疗目录编码
     */
    @Schema(description = "医疗目录编码")
    @TableField("med_list_codg")
    private String medListCodg;

    /**
     * 定点医药机构目录编号
     */
    @Schema(description = "定点医药机构目录编号")
    @TableField("fixmedins_hilist_id")
    private String fixmedinsHilistId;

    /**
     * 定点医药机构目录名称
     */
    @Schema(description = "定点医药机构目录名称")
    @TableField("fixmedins_hilist_name")
    private String fixmedinsHilistName;

    /**
     * 处方序号
     */
    @Schema(description = "处方序号")
    @TableField("cfxh")
    private Long cfxh;

    /**
     * 处方明细序号
     */
    @Schema(description = "处方明细序号")
    @TableField("cfmxxh")
    private Long cfmxxh;

    /**
     * 定点医药机构批次流水号
     */
    @Schema(description = "定点医药机构批次流水号")
    @TableField("fixmedins_bchno")
    private String fixmedinsBchno;

    /**
     * 开方医师姓名
     */
    @Schema(description = "开方医师姓名")
    @TableField("prsc_dr_name")
    private String prscDrName;

    /**
     * 药师姓名
     */
    @Schema(description = "药师姓名")
    @TableField("phar_name")
    private String pharName;

    /**
     * 药师执业资格证号
     */
    @Schema(description = "药师执业资格证号")
    @TableField("phar_prac_cert_no")
    private String pharPracCertNo;

    /**
     * 处方药标志
     */
    @Schema(description = "处方药标志")
    @TableField("rx_flag")
    private Integer rxFlag;

    /**
     * 拆零标志
     */
    @Schema(description = "拆零标志")
    @TableField("trdn_flag")
    private String trdnFlag;

    /**
     * 零售单据号
     */
    @Schema(description = "零售单据号")
    @TableField("rtal_docno")
    private Long rtalDocno;

    /**
     * 销售/退货数量
     */
    @Schema(description = "销售/退货数量")
    @TableField("sel_retn_cnt")
    private BigDecimal selRetnCnt;

    /**
     * 销售/退货时间
     */
    @Schema(description = "销售/退货时间")
    @TableField("sel_retn_time")
    private String selRetnTime;

    /**
     * 销售/退货经办人姓名
     */
    @Schema(description = "销售/退货经办人姓名")
    @TableField("sel_retn_opter_name")
    private String selRetnOpterName;

    /**
     * 就诊结算类型
     */
    @Schema(description = "就诊结算类型")
    @TableField("MDTRT_SETL_TYPE")
    private String mdtrtSetlType;

    /**
     * 生产批号
     */
    @Schema(description = "生产批号")
    @TableField("manu_lotnum")
    private String manuLotnum;

    /**
     * 生产日期
     */
    @Schema(description = "生产日期")
    @TableField("manu_date")
    private String manuDate;

    /**
     * 有效期止
     */
    @Schema(description = "有效期止")
    @TableField("expy_end")
    private String expyEnd;

    /**
     * 就医流水号
     */
    @Schema(description = "就医流水号")
    @TableField("mdtrt_sn")
    private String mdtrtSn;

    /**
     * 收据号
     */
    @Schema(description = "收据号")
    @TableField("sjh")
    private String sjh;

    /**
     * 人员姓名
     */
    @Schema(description = "人员姓名")
    @TableField("psn_name")
    private String psnName;

    /**
     * 环节序号
     */
    @Schema(description = "环节序号")
    @TableField("hjxh")
    private String hjxh;

    /**
     * 发药时间
     */
    @Schema(description = "发药时间")
    @TableField("send_time")
    private String sendTime;

    /**
     * 发药药房id
     */
    @Schema(description = "发药药房id")
    @TableField("fyyf")
    private String fyyf;

    /**
     * 发药药房名称
     */
    @Schema(description = "发药药房名称")
    @TableField("fyyfName")
    private String fyyfName;

    /**
     * 窗口号
     */
    @Schema(description = "窗口号")
    @TableField("windows")
    private String windows;

    /**
     * 病历号
     */
    @Schema(description = "病历号")
    @TableField("blh")
    private String blh;

    /**
     * 身份证号
     */
    @Schema(description = "身份证号")
    @TableField("sfzh")
    private String sfzh;

    /**
     * 最小销售/退货数量
     */
    @Schema(description = "最小销售/退货数量")
    @TableField("min_sel_retn_cnt")
    private BigDecimal minSelRetnCnt;

    /**
     * HIS剂量换算比例
     */
    @Schema(description = "HIS剂量换算比例")
    @TableField("his_con_ratio")
    private String hisConRatio;

}