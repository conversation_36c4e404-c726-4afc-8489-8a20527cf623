package com.zsm.task;

import cn.hutool.core.date.DateUtil;
import com.zsm.service.HangChuangService;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Scheduled;

/**
 * 太和人医-定时任务类
 *
 * <AUTHOR>
 * @date 2025/5/29 下午10:45
 */
// @Component
@Deprecated
@Slf4j
public class TaiHeRenYiTask {

    @Resource
    private HangChuangService hangChuangService;


    /**
     * 住院患者追溯码补录定时任务，每3小时执行一次（早上11点到夜里23点）
     * 按照上周日到本周六的时间范围处理，提升住院采集率
     */
    @Scheduled(cron = "0 0 11-23/3 * * ?")
    public void processWeeklyInpatientTraceabilityTask() {
        log.info("开始执行住院患者追溯码补录任务");
        try {

            final String today = DateUtil.today();

            // 调用查询今日出院患者的方法
            hangChuangService.processWeeklyInpatientTraceability(today, today);

            log.info("查询今日出院患者信息任务执行完成");

        } catch (Exception e) {
            log.error("住院患者追溯码补录任务执行失败", e);
            e.printStackTrace();
        }
    }
}
