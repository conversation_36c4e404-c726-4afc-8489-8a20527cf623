package com.zsm.model.vo;

import com.zsm.model.saas.response.GetTracCodgStoreDataResponse;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * 门诊处方查询响应VO
 *
 * <AUTHOR>
 * @date 2025-06-02
 */
@Data
@Schema(description = "门诊处方查询响应数据")
public class OutpatientPrescriptionResponseVo {

    /**
     * 响应代码
     * 用于标识API调用的结果状态
     * 通常200表示成功，其他值表示各种错误情况
     */
    @Schema(description = "响应代码")
    private Integer code;

    /**
     * 响应消息
     * 包含API调用结果的描述信息
     * 成功时通常为"success"，失败时包含错误详情
     */
    @Schema(description = "响应消息")
    private String message;

    /**
     * 处方数据列表
     * 包含所有符合查询条件的门诊处方明细信息
     * 每个元素代表一条处方记录
     */
    @Schema(description = "处方数据列表")
    private List<PrescriptionItem> dataList;

    /**
     * 处方明细项
     */
    @Data
    @Schema(description = "处方明细项")
    public static class PrescriptionItem {

        /**
         * 药品目录编码
         * 国家医保药品目录中的唯一编码
         * 用于标识特定的药品品种
         */
        @Schema(description = "药品目录编码")
        private String med_list_codg;

        /**
         * 定点医疗机构目录ID
         * 医疗机构内部药品目录的唯一标识
         * 与国家目录编码建立映射关系
         */
        @Schema(description = "定点医疗机构目录ID")
        private String fixmedins_hilist_id;

        /**
         * 定点医疗机构目录名称
         * 医疗机构内部使用的药品名称
         * 可能与通用名略有差异
         */
        @Schema(description = "定点医疗机构目录名称")
        private String fixmedins_hilist_name;

        /**
         * 定点医疗机构批次号
         * 医疗机构内部的药品批次管理编号
         * 用于追踪药品来源和质量
         */
        @Schema(description = "定点医疗机构批次号")
        private String fixmedins_bchno;

        /**
         * 开方医师证件号码
         * 处方医师的身份证号码
         * 用于医师身份验证和责任追溯
         */
        @Schema(description = "开方医师证件号码")
        private String prsc_dr_certno;

        /**
         * 开方医师姓名
         * 开具该处方的医师姓名
         * 与证件号码对应，确保处方合规性
         */
        @Schema(description = "开方医师姓名")
        private String prsc_dr_name;

        /**
         * 药师证件号码
         * 审核处方的药师身份证号码
         * 确保处方审核的合规性和可追溯性
         */
        @Schema(description = "药师证件号码")
        private String phar_certno;

        /**
         * 药师姓名
         * 审核该处方的药师姓名
         * 与证件号码对应，承担审核责任
         */
        @Schema(description = "药师姓名")
        private String phar_name;

        /**
         * 药师执业证书编号
         * 药师的执业资格证书编号
         * 证明药师具备合法执业资格
         */
        @Schema(description = "药师执业证书编号")
        private String phar_prac_cert_no;

        /**
         * 就诊序列号
         * 患者本次就诊的唯一标识
         * 用于关联处方与就诊记录
         */
        @Schema(description = "就诊序列号")
        private String mdtrt_sn;

        /**
         * 人员姓名
         * 患者的真实姓名
         * 用于核实患者身份
         */
        @Schema(description = "人员姓名")
        private String psn_name;

        /**
         * 生产批号
         * 药品生产厂家的批次编号
         * 用于药品质量追溯和召回
         */
        @Schema(description = "生产批号")
        private String manu_lotnum;

        /**
         * 生产日期
         * 药品的实际生产日期
         * 格式通常为YYYY-MM-DD
         */
        @Schema(description = "生产日期")
        private String manu_date;

        /**
         * 有效期截止
         * 药品的有效期截止日期
         * 超过此日期药品不能使用
         */
        @Schema(description = "有效期截止")
        private String expy_end;

        /**
         * 处方标志
         * 标识处方的类型和状态
         * 0-普通处方，1-急诊处方，2-儿童处方等
         */
        @Schema(description = "处方标志")
        private Integer rx_flag;

        /**
         * 拆零标志
         * 标识药品是否需要拆零发放
         * 0-不拆零，1-拆零发放
         */
        @Schema(description = "拆零标志")
        private Integer trdn_flag;

        /**
         * 处方号
         * 处方的唯一编号
         * 在医疗机构内具有唯一性
         */
        @Schema(description = "处方号")
        private String rxno;

        /**
         * 处方流转标志
         * 处方在不同系统间流转的状态标识
         * 用于跟踪处方的生命周期
         */
        @Schema(description = "处方流转标志")
        private String rx_circ_flag;

        /**
         * 零售单据号
         * 药品零售时的单据编号
         * 用于财务核算和销售追踪
         */
        @Schema(description = "零售单据号")
        private String rtal_docno;

        /**
         * 库存出库单号
         * 药品从库存出库时的单据号
         * 用于库存管理和出入库记录
         */
        @Schema(description = "库存出库单号")
        private String stoout_no;

        /**
         * 批次号
         * 药品在医疗机构内部的批次管理编号
         * 便于库存管理和质量追溯
         */
        @Schema(description = "批次号")
        private String bchno;

        /**
         * 售退数量
         * 实际发放给患者的药品数量
         * 负数表示退药，正数表示发药
         */
        @Schema(description = "售退数量")
        private String sel_retn_cnt;

        /**
         * 最小售退数量
         * 药品发放的最小包装单位数量
         * 用于拆零药品的管理
         */
        @Schema(description = "最小售退数量")
        private String min_sel_retn_cnt;

        /**
         * 售退单位
         * 药品发放时使用的计量单位
         * 如片、粒、瓶、盒等
         */
        @Schema(description = "售退单位")
        private String selRetnUnit;

        /**
         * HIS剂量单位
         * HIS系统中定义的药品剂量单位
         * 如mg、ml、g等
         */
        @Schema(description = "HIS剂量单位")
        private String hisDosUnit;

        /**
         * HIS包装单位
         * HIS系统中定义的药品包装单位
         * 如盒、瓶、支等
         */
        @Schema(description = "HIS包装单位")
        private String hisPacUnit;

        /**
         * 售退时间
         * 药品实际发放或退回的时间
         * 格式通常为YYYY-MM-DD HH:mm:ss
         */
        @Schema(description = "售退时间")
        private String sel_retn_time;

        /**
         * 售退经办人姓名
         * 执行药品发放或退回操作的工作人员姓名
         * 用于操作责任追溯
         */
        @Schema(description = "售退经办人姓名")
        private String sel_retn_opter_name;

        /**
         * 就诊结算类型
         * 患者就诊时的结算方式
         * 1-医保结算，2-自费结算，3-混合结算等
         */
        @Schema(description = "就诊结算类型")
        private Integer mdtrt_setl_type;

        /**
         * 规格
         * 药品的详细规格信息
         * 包含剂量、包装数量等信息
         */
        @Schema(description = "规格")
        private String spec;

        /**
         * 生产企业名称
         * 药品的生产厂家名称
         * 用于药品质量追溯和监管
         */
        @Schema(description = "生产企业名称")
        private String prodentp_name;

        /**
         * 处方序号
         * 处方在系统中的序列编号
         * 用于处方的排序和查找
         */
        @Schema(description = "处方序号")
        private String cfxh;

        /**
         * 处方明细序号
         * 处方明细项的序列编号
         * 一个处方可包含多个明细项
         */
        @Schema(description = "处方明细序号")
        private String cfmxxh;

        /**
         * 售价号
         * 药品售价的编号标识
         * 用于价格管理和费用计算
         */
        @Schema(description = "售价号")
        private String sjh;

        /**
         * 患者ID
         * 患者在医疗机构的唯一标识
         * 用于关联患者的所有医疗记录
         */
        @Schema(description = "患者ID")
        private String patient_id;

        /**
         * HIS剂量换算比例
         * HIS系统中不同单位间的换算比例
         * 用于药品剂量的准确计算
         */
        @Schema(description = "HIS剂量换算比例")
        private String his_con_ratio;

        /**
         * 发送标志
         * 数据发送状态的标识
         * 0-未发送，1-已发送，2-发送失败
         */
        @Schema(description = "发送标志")
        private Integer send_flag;

        /**
         * 发送时间
         * 数据发送的时间戳
         * 记录数据传输的时间点
         */
        @Schema(description = "发送时间")
        private String send_time;

        /**
         * 返回时间
         * 接收到响应的时间戳
         * 用于监控接口响应时间
         */
        @Schema(description = "返回时间")
        private String return_time;

        /**
         * 发药药房id
         * 执行发药操作的药房唯一标识
         * 用于药房管理和统计分析
         */
        @Schema(description = "发药药房id")
        private String dept_id;

        /**
         * 发药药房名称
         * 执行发药操作的药房名称
         * 与药房ID对应的可读性名称
         */
        @Schema(description = "发药药房名称")
        private String dept_name;

        /**
         * 窗口号
         * 药房发药窗口的编号
         * 用于患者取药和窗口管理
         */
        @Schema(description = "窗口号")
        private String window;

        /**
         * 药品追溯码任务ID
         * 药品追溯相关任务的唯一标识
         * 用于追踪药品流转过程
         */
        @Schema(description = "药品追溯码任务ID")
        private String taskIdDps;

        /**
         * 药品追溯码任务状态
         * 追溯任务的当前执行状态
         * 如待处理、处理中、已完成、失败等
         */
        @Schema(description = "药品追溯码任务状态")
        private String taskFgStatusDps;

        /**
         * 药品追溯码任务创建时间
         * 追溯任务创建的时间戳
         * 用于任务管理和监控
         */
        @Schema(description = "药品追溯码任务创建时间")
        private String taskScanTimeDps;        

        /**
         * 发药药房id
         * 执行发药操作的药房唯一标识
         * 用于药房管理和统计分析
         */
        @Schema(description = "发药药房id")
        private String fyyf;

        // saas拆零接口字段
        /**
         * 药品编码
         * SAAS系统中的药品唯一编码
         * 用于药品识别和管理
         */
        private String drugCode;
        
        /**
         * 药品追溯码列表
         * 该药品的所有追溯码信息
         * 用于药品全程追溯
         */
        private List<String> drugTracCodgs;
        
        /**
         * 发药数量
         * 实际发放的药品数量
         * 用于库存扣减和统计
         */
        private String dispCnt;
        
        /**
         * 当前库存数量
         * 药品的当前可用库存
         * 用于库存预警和管理
         */
        private BigDecimal currNum;
        
        /**
         * 追溯码库存数据
         * 追溯码相关的库存信息
         * 包含详细的库存状态数据
         */
        private GetTracCodgStoreDataResponse tracCodgStore;
    }
} 