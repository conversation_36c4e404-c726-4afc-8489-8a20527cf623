package com.zsm.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 霍邱1院 住院发药处方视图
 *
 * <AUTHOR>
 * @since 2025-01-24
 */
@Data
@TableName("VIEW_ZYFYCF_FY")
@Schema(description = "住院发药处方视图")
public class ViewZyfycfFy implements Serializable {

    private static final long serialVersionUID = 1L;

    @Schema(description = "状态码")
    @TableField("code")
    private String code;

    @Schema(description = "消息")
    @TableField("message")
    private String message;

    @Schema(description = "医疗目录编码")
    @TableField("med_list_codg")
    private String medListCodg;

    @Schema(description = "定点医药机构目录编号")
    @TableField("fixmedins_hilist_id")
    private String fixmedinsHilistId;

    @Schema(description = "定点医药机构目录名称")
    @TableField("fixmedins_hilist_name")
    private String fixmedinsHilistName;

    @Schema(description = "定点医药机构批次流水号")
    @TableField("fixmedins_bchno")
    private String fixmedinsBchno;

    @Schema(description = "开方医师证件号码")
    @TableField("prsc_dr_certno")
    private String prscDrCertno;

    @Schema(description = "开方医师姓名")
    @TableField("prsc_dr_name")
    private String prscDrName;

    @Schema(description = "药师证件号码")
    @TableField("phar_certno")
    private String pharCertno;

    @Schema(description = "药师姓名")
    @TableField("phar_name")
    private String pharName;

    @Schema(description = "药师执业资格证号")
    @TableField("phar_prac_cert_no")
    private String pharPracCertNo;

    @Schema(description = "就医流水号")
    @TableField("mdtrt_sn")
    private String mdtrtSn;

    @Schema(description = "人员姓名")
    @TableField("psn_name")
    private String psnName;

    @Schema(description = "生产批号")
    @TableField("manu_lotnum")
    private String manuLotnum;

    @Schema(description = "生产日期")
    @TableField("manu_date")
    private String manuDate;

    @Schema(description = "有效期止")
    @TableField("expy_end")
    private String expyEnd;

    @Schema(description = "处方药标志 0-否；1-是")
    @TableField("rx_flag")
    private String rxFlag;

    @Schema(description = "拆零标志 0-否；1-是")
    @TableField("trdn_flag")
    private String trdnFlag;

    @Schema(description = "处方号")
    @TableField("rxno")
    private String rxno;

    @Schema(description = "外购处方标志")
    @TableField("rx_circ_flag")
    private String rxCircFlag;

    @Schema(description = "零售单据号")
    @TableField("rtal_docno")
    private String rtalDocno;

    @Schema(description = "销售出库单据号")
    @TableField("stoout_no")
    private String stooutNo;

    @Schema(description = "批次号")
    @TableField("bchno")
    private String bchno;

    @Schema(description = "销售/退货数量")
    @TableField("sel_retn_cnt")
    private BigDecimal selRetnCnt;

    @Schema(description = "销售/退货时间")
    @TableField("sel_retn_time")
    private String selRetnTime;

    @Schema(description = "销售/退货经办人姓名")
    @TableField("sel_retn_opter_name")
    private String selRetnOpterName;

    @Schema(description = "就诊结算类型 1-医保结算 2-自费结算")
    @TableField("mdtrt_setl_type")
    private String mdtrtSetlType;

    @Schema(description = "规格")
    @TableField("spec")
    private String spec;

    @Schema(description = "生产企业名称")
    @TableField("prodentp_name")
    private String prodentpName;

    @Schema(description = "处方序号")
    @TableField("cfxh")
    private String cfxh;

    @Schema(description = "处方明细序号")
    @TableField("cfmxxh")
    private String cfmxxh;

    @Schema(description = "收据号")
    @TableField("sjh")
    private String sjh;

    @Schema(description = "患者id")
    @TableField("patient_id")
    private String patientId;

    @Schema(description = "发药药房")
    @TableField("fyyf")
    private String fyyf;

    @Schema(description = "HIS转换系数")
    @TableField("his_con_ratio")
    private BigDecimal hisConRatio;

    @Schema(description = "出院带药标志 1-出院带药 0-正常医嘱")
    @TableField("cydybz")
    private String cydybz;

    @Schema(description = "记录ID")
    @TableField("record_id")
    private String recordId;

    @Schema(description = "病区ID")
    @TableField("pat_ward_id")
    private String patWardId;

    @Schema(description = "病区名称")
    @TableField("pat_ward_name")
    private String patWardName;

    @Schema(description = "销售/退货单位")
    @TableField("sel_retn_unit")
    private String selRetnUnit;

    @Schema(description = "领药序号")
    @TableField("lyxh")
    private String lyxh;
}