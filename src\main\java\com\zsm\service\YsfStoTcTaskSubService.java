package com.zsm.service;

import com.zsm.entity.YsfStoTcTaskSub;
import com.baomidou.mybatisplus.extension.service.IService;
import com.zsm.model.TableDataInfo;
import com.zsm.model.dto.YsfStoTcTaskSubQueryDto;

/**
 * <p>
 * 扫码任务明细表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-02
 */
public interface YsfStoTcTaskSubService extends IService<YsfStoTcTaskSub> {

    /**
     * 根据任务ID和处方明细ID查询任务明细
     *
     * @param taskId 任务ID
     * @param cfmxxh 处方明细ID
     * @return 任务明细记录
     */
    YsfStoTcTaskSub getByTaskIdAndCfmxxh(String taskId, String cfmxxh);

    /**
     * 查询扫码任务明细
     *
     * @param id 扫码任务明细主键
     * @return 扫码任务明细
     */
    YsfStoTcTaskSub selectYsfStoTcTaskSubById(Long id);

    /**
     * 查询扫码任务明细列表
     *
     * @param queryDto 查询条件
     * @return 扫码任务明细分页数据
     */
    TableDataInfo queryTaskSubList(YsfStoTcTaskSubQueryDto queryDto);
}
