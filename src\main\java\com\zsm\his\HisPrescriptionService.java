package com.zsm.his;

import com.zsm.model.ApiResult;
import com.zsm.model.dto.InpatientPrescriptionQueryDto;
import com.zsm.model.dto.OutpatientPrescriptionQueryDto;
import com.zsm.model.vo.InpatientPrescriptionResponseVo;
import com.zsm.model.vo.OutpatientPrescriptionResponseVo;

import java.util.List;

/**
 * HIS门诊和住院处方查询服务抽象接口
 * 
 * <p>该接口定义了医院信息系统中处方查询的标准化方法，包括门诊和住院处方查询功能。
 * 不同医院的HIS系统实现可以通过实现此接口来提供统一的处方查询能力。</p>
 * 
 * <p><strong>设计目标：</strong></p>
 * <ul>
 *   <li>标准化处方查询接口，使不同HIS系统具有统一的API规范</li>
 *   <li>支持多种查询方式：患者ID、身份证号、卡号、处方号等</li>
 *   <li>提供扩展性，便于未来新增查询功能或适配新的HIS系统</li>
 *   <li>统一异常处理和返回格式</li>
 * </ul>
 * 
 * <p><strong>适用场景：</strong></p>
 * <ul>
 *   <li>药品追溯系统中的处方数据查询</li>
 *   <li>发药管理系统中的处方信息获取</li>
 *   <li>医保数据上报前的处方数据准备</li>
 *   <li>移动端应用的处方查询功能</li>
 * </ul>
 * 
 * <AUTHOR>
 * @version 1.0
 * @since 2025-06-26
 */
public interface HisPrescriptionService {

    /**
     * 查询门诊处方信息
     * 
     * <p>根据不同的查询条件获取门诊患者的处方发药明细信息。该方法支持多种查询方式，
     * 包括通过患者ID、身份证号、卡号、处方号等方式进行查询。</p>
     * 
     * <p><strong>支持的查询类型（cardType）：</strong></p>
     * <ul>
     *   <li><code>1</code> - 患者ID查询：直接使用患者ID进行查询</li>
     *   <li><code>2</code> - 身份证号查询：先通过身份证号获取患者ID，再查询处方</li>
     *   <li><code>3</code> - 卡号查询：先通过卡号获取患者ID，再查询处方</li>
     *   <li><code>4</code> - 处方号查询：直接使用处方号查询指定处方</li>
     *   <li><code>5</code> - 处方号患者查询：通过处方号获取患者ID，查询该患者所有处方</li>
     * </ul>
     * 
     * <p><strong>功能特性：</strong></p>
     * <ul>
     *   <li>自动处理追溯码库存信息查询和填充</li>
     *   <li>过滤已完成扫码任务的处方，避免重复操作</li>
     *   <li>异步保存处方数据到医保3505表</li>
     *   <li>支持时间范围过滤和发药状态过滤</li>
     * </ul>
     * 
     * <p><strong>数据处理流程：</strong></p>
     * <ol>
     *   <li>参数校验和患者ID获取</li>
     *   <li>调用HIS系统获取处方明细</li>
     *   <li>查询并填充追溯码库存信息</li>
     *   <li>过滤已完成的扫码任务</li>
     *   <li>异步保存到医保数据表</li>
     * </ol>
     * 
     * @param queryDto 门诊处方查询参数对象，包含以下主要字段：
     *                 <ul>
     *                   <li><code>cardType</code> - 查询类型（必填）</li>
     *                   <li><code>patient_id</code> - 患者ID（cardType=1时必填）</li>
     *                   <li><code>cardNo</code> - 卡号/身份证号（cardType=2,3时必填）</li>
     *                   <li><code>cfxh</code> - 处方号（cardType=4,5时必填）</li>
     *                   <li><code>startTime</code> - 开始时间（可选）</li>
     *                   <li><code>endTime</code> - 结束时间（可选）</li>
     *                   <li><code>fg_dps</code> - 发药标志（可选）</li>
     *                   <li><code>send_flag</code> - 发送标志（可选）</li>
     *                 </ul>
     * 
     * @return ApiResult&lt;List&lt;OutpatientPrescriptionResponseVo.PrescriptionItem&gt;&gt; 
     *         包含门诊处方明细列表的响应结果。每个处方项包含：
     *         <ul>
     *           <li>基本处方信息：处方号、处方明细号、患者信息等</li>
     *           <li>药品信息：药品编码、名称、规格、生产厂家等</li>
     *           <li>发药信息：发药数量、发药时间、发药人员等</li>
     *           <li>追溯信息：追溯标志、追溯码库存等（如果药品需要追溯）</li>
     *           <li>任务信息：扫码任务ID、任务状态等（如果存在相关任务）</li>
     *         </ul>
     * 
     * @throws com.zsm.common.exception.BusinessException 当查询参数不正确或业务逻辑校验失败时抛出
     * @throws RuntimeException 当系统异常或网络异常时抛出
     * 
     * @apiNote 该方法为核心业务方法，调用频率较高，实现时需要注意：
     *          <ul>
     *            <li>合理使用缓存机制提升查询性能</li>
     *            <li>做好异常处理，避免影响主业务流程</li>
     *            <li>异步操作不应阻塞主查询流程</li>
     *            <li>注意数据权限控制和敏感信息保护</li>
     *          </ul>
     */
    ApiResult<List<OutpatientPrescriptionResponseVo.PrescriptionItem>> queryOutpatientPrescription(OutpatientPrescriptionQueryDto queryDto);

    /**
     * 查询住院处方信息
     * 
     * <p>获取住院患者的处方明细信息，主要用于追溯系统中的住院处方数据查询和管理。
     * 该方法支持多种查询条件，可以根据时间范围、病区、科室等条件进行精确查询。</p>
     * 
     * <p><strong>主要用途：</strong></p>
     * <ul>
     *   <li>住院处方明细的查询和展示</li>
     *   <li>处方数据的统计和分析</li>
     *   <li>药品追溯链条中的处方环节数据获取</li>
     *   <li>医保数据上报前的住院处方数据准备</li>
     * </ul>
     * 
     * <p><strong>数据特点：</strong></p>
     * <ul>
     *   <li>包含详细的病区和科室信息</li>
     *   <li>处方时间精确到秒级</li>
     *   <li>包含完整的药品信息和医嘱信息</li>
     *   <li>可能包含长期医嘱和临时医嘱</li>
     * </ul>
     * 
     * <p><strong>与发药记录查询的区别：</strong></p>
     * <ul>
     *   <li>处方查询侧重于处方开具信息</li>
     *   <li>发药记录查询侧重于实际发药执行信息</li>
     *   <li>处方可能包含未发药的医嘱</li>
     *   <li>处方信息更多关注医师和药师的处方行为</li>
     * </ul>
     * 
     * <p><strong>功能特性：</strong></p>
     * <ul>
     *   <li>支持按时间范围查询</li>
     *   <li>支持按病区、科室过滤</li>
     *   <li>包含完整的患者信息和医嘱信息</li>
     *   <li>提供药品的详细信息</li>
     * </ul>
     * 
     * @param queryDto 住院处方查询参数对象，包含以下主要字段：
     *                 <ul>
     *                   <li><code>startTime</code> - 查询开始时间（建议必填）</li>
     *                   <li><code>endTime</code> - 查询结束时间（建议必填）</li>
     *                   <li><code>recordId</code> - 记录ID（可选，用于精确查询）</li>
     *                   <li><code>fgDps</code> - 发药标志（可选）</li>
     *                   <li><code>fyyf</code> - 费用药房（可选）</li>
     *                   <li><code>deptId</code> - 科室ID（可选）</li>
     *                   <li><code>patWardId</code> - 病区ID（可选）</li>
     *                 </ul>
     * 
     * @return ApiResult&lt;List&lt;InpatientPrescriptionResponseVo.InpatientPrescriptionItem&gt;&gt; 
     *         包含住院处方明细列表的响应结果。每个处方项包含：
     *         <ul>
     *           <li>处方基本信息：处方号、处方明细号、开具时间等</li>
     *           <li>患者信息：住院号、患者姓名、床位号、病区等</li>
     *           <li>药品信息：药品编码、名称、规格、批次、有效期等</li>
     *           <li>处方详情：处方数量、用法用量、频次等</li>
     *           <li>医疗信息：处方医师、科室、医嘱类型等</li>
     *         </ul>
     * 
     * @throws com.zsm.common.exception.BusinessException 当查询参数不正确时抛出
     * @throws RuntimeException 当系统异常或网络异常时抛出
     * 
     * @apiNote 住院处方查询注意事项：
     *          <ul>
     *            <li>建议限制查询时间范围，避免一次性查询过多数据</li>
     *            <li>可根据业务需要实现分页查询功能</li>
     *            <li>注意住院处方数据的实时性要求</li>
     *            <li>考虑实现数据权限控制，确保数据安全</li>
     *            <li>处方信息可能需要与发药记录关联使用</li>
     *          </ul>
     * 
     * @see InpatientPrescriptionQueryDto 住院处方查询参数类
     * @see InpatientPrescriptionResponseVo.InpatientPrescriptionItem 住院处方明细VO类
     */
    ApiResult<List<InpatientPrescriptionResponseVo.InpatientPrescriptionItem>> queryInpatientPrescription(InpatientPrescriptionQueryDto queryDto);

    /**
     * 同步药品字典
     * 
     * <p>从HIS系统同步药品字典数据到本地数据库或云端平台。
     * 该方法主要用于保持药品信息的同步，确保追溯系统中的药品数据与HIS系统一致。</p>
     * 
     * <p><strong>主要功能：</strong></p>
     * <ul>
     *   <li>从HIS系统获取全量药品字典数据</li>
     *   <li>数据格式转换和标准化</li>
     *   <li>批量上传到目标平台（如SAAS平台）</li>
     *   <li>支持增量和全量同步模式</li>
     * </ul>
     * 
     * <p><strong>数据同步流程：</strong></p>
     * <ol>
     *   <li>分页从HIS系统拉取药品数据</li>
     *   <li>数据格式转换和字段映射</li>
     *   <li>数据验证和清洗</li>
     *   <li>分批上传到目标平台</li>
     *   <li>记录同步结果和异常信息</li>
     * </ol>
     * 
     * <p><strong>数据映射规则：</strong></p>
     * <ul>
     *   <li>药品编码：HIS药品编码 + 生产企业编码</li>
     *   <li>药品名称：直接映射</li>
     *   <li>规格型号：直接映射</li>
     *   <li>生产厂家：直接映射</li>
     *   <li>包装单位：映射到最小包装单位</li>
     *   <li>转换比：基于包装数量计算</li>
     * </ul>
     * 
     * <p><strong>同步策略：</strong></p>
     * <ul>
     *   <li>支持定时自动同步</li>
     *   <li>支持手动触发同步</li>
     *   <li>分批处理，避免数据量过大</li>
     *   <li>异常重试机制</li>
     *   <li>同步进度和状态监控</li>
     * </ul>
     * 
     * @return ApiResult&lt;String&gt; 同步操作结果，包含：
     *         <ul>
     *           <li>同步状态：成功/失败</li>
     *           <li>处理数量：总数量、成功数量、失败数量</li>
     *           <li>错误信息：如果有异常则包含详细错误描述</li>
     *           <li>同步耗时：操作执行时间</li>
     *         </ul>
     * 
     * @throws com.zsm.common.exception.BusinessException 当HIS系统连接失败或数据格式错误时抛出
     * @throws RuntimeException 当网络异常或系统错误时抛出
     * 
     * @apiNote 药品字典同步注意事项：
     *          <ul>
     *            <li>建议在业务低峰期执行，避免影响正常业务</li>
     *            <li>大量数据同步时注意内存使用和超时控制</li>
     *            <li>确保网络连接稳定，避免数据丢失</li>
     *            <li>同步前建议备份现有数据</li>
     *            <li>监控同步过程，及时处理异常情况</li>
     *            <li>定期检查数据一致性</li>
     *          </ul>
     * 
     * @since 1.0
     */
    ApiResult<String> syncDrugDictionary();

}