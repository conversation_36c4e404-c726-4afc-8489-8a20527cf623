package com.zsm.service.impl;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.zsm.entity.ViewCyhzxx;
import com.zsm.mapper.ViewCyhzxxMapper;
import com.zsm.service.ViewCyhzxxService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

/**
 * <p>
 *  服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-10
 */
@DS("his")
@Service
public class ViewCyhzxxServiceImpl extends ServiceImpl<ViewCyhzxxMapper, ViewCyhzxx> implements ViewCyhzxxService {

}
