package com.zsm.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 阜阳肿瘤出院患者信息视图实体类
 * 
 * <AUTHOR>
 * @since 2025-07-01
 */
@Data
@TableName("VIEW_YSF_CYHZXX")
@Schema(description = "阜阳肿瘤出院患者信息视图")
public class ViewYsfCyhzxx implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 患者住院ID
     */
    @Schema(description = "患者住院ID")
    @TableField("pat_in_hos_id")
    private String patInHosId;

    /**
     * 患者ID
     */
    @Schema(description = "患者ID")
    @TableField("patient_id")
    private Long patientId;

    /**
     * 患者姓名
     */
    @Schema(description = "患者姓名")
    @TableField("patient_name")
    private String patientName;

    /**
     * 选择时间
     */
    @Schema(description = "选择时间")
    @TableField("sel_time")
    private String selTime;
}