package com.zsm;

import com.zsm.his.InpatientTraceabilityService;
import com.zsm.mapper.ViewYsfZyfycfMapper;
import com.zsm.model.vo.InpatientSettlementVo;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.List;

/**
 * 住院患者查询性能测试
 * 用于验证SQL层面去重优化的效果
 */
@Slf4j
@SpringBootTest
public class InpatientTraceabilityPerformanceTest {

    @Resource
    private ViewYsfZyfycfMapper viewYsfZyfycfMapper;

    @Resource
    private InpatientTraceabilityService inpatientTraceabilityService;

    private static final DateTimeFormatter DATE_FORMAT = DateTimeFormatter.ofPattern("yyyy-MM-dd");

    /**
     * 测试优化后的SQL直接去重查询性能
     */
    @Test
    public void testOptimizedQuery() {
        // 测试查询最近7天的数据
        String endDate = LocalDate.now().format(DATE_FORMAT);
        String startDate = LocalDate.now().minusDays(7).format(DATE_FORMAT);
        
        log.info("开始测试优化后的查询性能，时间范围：{} - {}", startDate, endDate);
        
        // 构建时间参数
        String startDateTime = startDate + " 00:00:00";
        String endDateTime = endDate + " 23:59:59";
        
        // 记录开始时间
        long startTime = System.currentTimeMillis();
        
        // 执行优化后的查询（SQL层面去重）
        List<InpatientSettlementVo> result = viewYsfZyfycfMapper.selectDistinctInpatients(startDateTime, endDateTime);
        
        // 记录结束时间
        long endTime = System.currentTimeMillis();
        long duration = endTime - startTime;
        
        log.info("========================================");
        log.info("优化后查询性能测试结果：");
        log.info("查询时间范围：{} 至 {}", startDate, endDate);
        log.info("返回记录数：{} 条", result != null ? result.size() : 0);
        log.info("查询耗时：{} 毫秒", duration);
        log.info("========================================");
        
        // 打印前5条记录作为样本
        if (result != null && !result.isEmpty()) {
            log.info("样本数据（前5条）：");
            result.stream().limit(5).forEach(vo -> {
                log.info("患者ID: {}, 姓名: {}, 医保流水号: {}, 人员编号: {}", 
                    vo.getPatientId(), 
                    vo.getPatientName(),
                    vo.getMdtrtSn(),
                    vo.getPsnNo());
            });
        }
    }
    
    /**
     * 比较不同时间范围的查询性能
     */
    @Test
    public void testQueryPerformanceComparison() {
        log.info("开始批量性能测试...");
        
        // 测试不同时间范围
        int[] dayRanges = {1, 7, 30, 90};
        
        for (int days : dayRanges) {
            String endDate = LocalDate.now().format(DATE_FORMAT);
            String startDate = LocalDate.now().minusDays(days).format(DATE_FORMAT);
            
            String startDateTime = startDate + " 00:00:00";
            String endDateTime = endDate + " 23:59:59";
            
            long startTime = System.currentTimeMillis();
            List<InpatientSettlementVo> result = viewYsfZyfycfMapper.selectDistinctInpatients(startDateTime, endDateTime);
            long duration = System.currentTimeMillis() - startTime;
            
            log.info("查询 {} 天数据：返回 {} 条记录，耗时 {} 毫秒", 
                days, 
                result != null ? result.size() : 0, 
                duration);
        }
        
        log.info("批量性能测试完成！");
    }
}
