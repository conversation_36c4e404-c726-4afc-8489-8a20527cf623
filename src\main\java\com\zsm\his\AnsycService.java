package com.zsm.his;

import com.zsm.model.dto.TraceabilityUploadDto;
import com.zsm.model.saas.request.ConfirmDispDrugDataRequest;
import com.zsm.model.saas.request.ConfirmDispDrugRequest;
import com.zsm.utils.SaasHttpUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
 * 异步处理服务类
 *
 * <AUTHOR>
 * @date 2025/6/9 14:38
 */
@Slf4j
@Service
public class AnsycService {

    /**
     * 阜阳人医异步保存sass确认拆零发药
     *
     * @param prescriptions 处方
     * @param token         令牌
     */
    @Async
    public void saveSassConfirmMedicationDispensingAsync(TraceabilityUploadDto.PrescriptionItem prescriptions, String token) {
        final List<TraceabilityUploadDto.DrugItem> needConfirmList = prescriptions.getDrugItems();
        // 构建确认发药接口请求参数
        List<ConfirmDispDrugDataRequest> requestDataList = new ArrayList<>();
        for (TraceabilityUploadDto.DrugItem item : needConfirmList) {
            ConfirmDispDrugDataRequest request = new ConfirmDispDrugDataRequest();
            request.setDrugCode(item.getDrugCode());

            if (!ObjectUtils.isEmpty(item.getDispCnt())) {
                // 使用本地存储的confirmDispCnt字段值
                request.setDispCnt(item.getDispCnt()
                        .intValue());
                request.setCfxh(item.getCfxh());
                request.setCfmxxh(item.getCfmxxh());
                requestDataList.add(request);
            }
        }

        // 批量处理，每批最多50条数据
        final int batchSize = 50;
        // final AtomicInteger totalSuccessCount = new AtomicInteger(0);
        // final AtomicInteger totalFailCount = new AtomicInteger(0);

        // 分批处理数据
        for (int i = 0; i < requestDataList.size(); i += batchSize) {
            // 获取当前批次的数据
            int endIndex = Math.min(i + batchSize, requestDataList.size());
            List<ConfirmDispDrugDataRequest> batchRequestDataList = requestDataList.subList(i, endIndex);
            // List<TraceabilityUploadDto.DrugItem> batchNeedConfirmList = needConfirmList.subList(i, endIndex);

            log.info("正在处理第{}批数据，共{}条", (i / batchSize) + 1, batchRequestDataList.size());

            // 构建批次请求
            ConfirmDispDrugRequest confirmRequest = new ConfirmDispDrugRequest();
            confirmRequest.setDataList(batchRequestDataList);

            // 调用confirmDispDrug接口
            SaasHttpUtil.confirmDispDrug(token, confirmRequest);

            // TODO: 如果小概率事件发生，两个窗口同时发一个拆零药品。 导致追溯码被占用，后续可能需要考虑一下如何实现这种重试的确认拆零发药的机制。

        }
    }
}
