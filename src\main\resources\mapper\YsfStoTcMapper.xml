<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zsm.mapper.YsfStoTcMapper">

    <!-- 批量查询追溯码信息 -->
    <select id="selectBatchByTraceinfo" resultType="com.zsm.entity.YsfStoTc">
        SELECT 
            drugtracinfo,
            id_tc,
            drug_code,
            amount_rem,
            unit_sale_factor,
            unit_tc,
            fg_active,
            id_dept,
            id_org,
            org_id,
            org_name,
            sd_tc_manage,
            create_by,
            create_time,
            update_by,
            update_time,
            del_flag
        FROM ysf_sto_tc
        WHERE drugtracinfo IN 
        <foreach collection="traceinfoList" item="traceinfo" open="(" separator="," close=")">
            #{traceinfo}
        </foreach>
        AND del_flag = '0'
    </select>

</mapper>
