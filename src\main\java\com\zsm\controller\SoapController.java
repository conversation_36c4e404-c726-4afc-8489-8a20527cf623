package com.zsm.controller;

import cn.hutool.http.HttpResponse;
import cn.hutool.http.HttpUtil;
import cn.hutool.json.JSONObject;

import com.zsm.model.ApiResult;
import com.zsm.utils.SoapUtil;
import com.zsm.common.exception.BusinessException;
import com.zsm.common.exception.BusinessErrorEnum;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

/**
 * SOAP请求示例控制器
 * 使用SoapUtil实现SOAP Web Service调用，异常由全局异常处理器统一处理
 */
@Deprecated
@Tag(name = "SOAP请求示例", description = "SOAP请求示例控制器")
@Slf4j
// @RestController
// @RequestMapping("/api/soap")
public class SoapController {

    /**
     * 发送SOAP请求（使用默认参数）
     *
     * @return SOAP响应结果
     */
    @Operation(summary = "发送SOAP请求（使用默认参数）")
    @GetMapping("/call")
    public ApiResult callSoapService() {
        return SoapUtil.callSoapService();
    }

    /**
     * 发送SOAP请求（自定义参数）
     *
     * @param input1 输入参数1
     * @param input2 输入参数2
     * @return SOAP响应结果
     */
    @Operation(summary = "发送SOAP请求（自定义参数）")
    @PostMapping("/call-with-params")
    public ApiResult callSoapServiceWithParams(
            @RequestParam String input1, 
            @RequestBody Object input2) {
        return SoapUtil.callSoapServiceWithParams(input1, input2);
    }

    /**
     * 解析WSDL文档
     *
     * @param wsdlUrl WSDL地址
     * @return WSDL解析结果
     */
    @Operation(summary = "解析WSDL文档")
    @GetMapping("/parse-wsdl")
    public JSONObject parseWsdl(@RequestParam String wsdlUrl) {
        // 参数校验由BusinessException处理
        if (wsdlUrl == null || wsdlUrl.trim().isEmpty()) {
            throw new BusinessException(BusinessErrorEnum.PARAM_ERROR.getCode(), "WSDL地址不能为空");
        }
        return SoapUtil.parseWsdl(wsdlUrl);
    }

    /**
     * 测试SOAP服务连通性
     *
     * @param soapUrl SOAP服务地址
     * @return 连通性测试结果
     */
    @Operation(summary = "测试SOAP服务连通性")
    @GetMapping("/test-connection")
    public ApiResult<JSONObject> testConnection(@RequestParam String soapUrl) {
        // 参数校验
        if (soapUrl == null || soapUrl.trim().isEmpty()) {
            throw new BusinessException(BusinessErrorEnum.PARAM_ERROR.getCode(), "SOAP服务地址不能为空");
        }

        JSONObject result = new JSONObject();
        long startTime = System.currentTimeMillis();

        try {
            log.info("开始测试SOAP服务连通性: {}", soapUrl);

            HttpResponse response = HttpUtil.createGet(soapUrl)
                    .timeout(5000) // 5秒超时
                    .execute();

            long endTime = System.currentTimeMillis();
            long responseTime = endTime - startTime;

            result.put("statusCode", response.getStatus());
            result.put("responseTime", responseTime);
            result.put("isReachable", response.isOk());

            if (response.isOk()) {
                log.info("SOAP服务连通性测试成功，响应时间: {}ms", responseTime);
                return ApiResult.success("SOAP服务连通性测试成功", result);
            } else {
                log.warn("SOAP服务连通性测试失败，状态码: {}", response.getStatus());
                throw new BusinessException(BusinessErrorEnum.SOAP_SERVICE_UNAVAILABLE.getCode(),
                        String.format("SOAP服务不可用，状态码: %d", response.getStatus()));
            }

        } catch (BusinessException e) {
            // 重新抛出业务异常
            throw e;
        } catch (Exception e) {
            log.error("SOAP服务连通性测试异常: {}", e.getMessage(), e);
            throw new BusinessException(BusinessErrorEnum.SOAP_SERVICE_UNAVAILABLE.getCode(),
                    "SOAP服务连通性测试失败: " + e.getMessage());
        }
    }
}